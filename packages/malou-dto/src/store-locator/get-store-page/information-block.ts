import { z } from 'zod';

import { latlngDtoValidator } from '../../restaurant';
import { urlValidator } from '../../utils';
import { ctaValidator } from '../common';

export const storeLocatorStorePageInformationBlockValidator = z
    .object({
        restaurantName: z.string(),
        imageUrl: urlValidator(),
        imageDescription: z.string(),
        mediaId: z.string().nullish(),
        fullAddress: z.string(),
        coordinates: latlngDtoValidator,
        phone: z.string().optional(),
        itineraryUrl: z.string(),
        attributesNames: z.array(z.string()),
        paymentMethods: z.array(z.string()),
        isNotOpenedYet: z.boolean(),
        hours: z.array(
            z.object({
                day: z.string(),
                formattedHour: z.string(),
                periods: z.array(
                    z.object({
                        openTime: z.string().optional(),
                        closeTime: z.string().optional(),
                        isClosed: z.boolean(),
                        openDay: z.string(),
                        closeDay: z.string(),
                    })
                ),
            })
        ),
        cta: ctaValidator.optional(),
    })
    .refine(({ hours, isNotOpenedYet }) => isNotOpenedYet || hours.length > 0, {
        message: 'No hours is only allowed if the restaurant is not opened yet',
    });
