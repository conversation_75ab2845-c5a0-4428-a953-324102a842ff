import { z } from 'zod';

import {
    PlatformKey,
    StoreLocatorAiSettingsLanguageStyle,
    StoreLocatorMapPageElementIds,
    StoreLocatorRestaurantPageElementIds,
} from '@malou-io/package-utils';

export const StoreLocatorOrganizationConfigurationResponseDtoSchema = z.object({
    id: z.string(),
    organizationId: z.string(),
    cloudfrontDistributionId: z.string(),
    baseUrl: z.string(),
    isLive: z.boolean(),
    styles: z.object({
        fonts: z.array(
            z.object({
                class: z.string(),
                src: z.string(),
                weight: z.string().optional(),
                style: z.string().optional(),
            })
        ),
        colors: z.array(
            z.object({
                class: z.string(),
                value: z.string(),
            })
        ),
        pages: z.object({
            store: z.record(z.nativeEnum(StoreLocatorRestaurantPageElementIds), z.array(z.string())),
            map: z.record(z.nativeEnum(StoreLocatorMapPageElementIds), z.array(z.string())),
            storeDraft: z.record(z.nativeEnum(StoreLocatorRestaurantPageElementIds), z.array(z.string())),
            mapDraft: z.record(z.nativeEnum(StoreLocatorMapPageElementIds), z.array(z.string())),
        }),
    }),
    plugins: z
        .object({
            googleAnalytics: z
                .object({
                    trackingId: z.string(),
                })
                .optional(),
        })
        .optional(),
    aiSettings: z.object({
        tone: z.array(z.string()),
        languageStyle: z.nativeEnum(StoreLocatorAiSettingsLanguageStyle),
        attributeIds: z.array(z.string()),
        restaurantKeywordIds: z.array(z.string()),
        specialAttributes: z.array(
            z.object({
                restaurantId: z.string(),
                text: z.string(),
            })
        ),
        attributes: z.array(
            z.object({
                id: z.string(),
                attributeId: z.string(),
                platformKey: z.nativeEnum(PlatformKey),
                attributeName: z.object({
                    fr: z.string(),
                    en: z.string().optional(),
                    es: z.string().optional(),
                    it: z.string().optional(),
                }),
            })
        ),
        keywords: z.array(
            z.object({
                restaurantKeywordId: z.string(),
                text: z.string(),
                restaurantId: z.string(),
                keywordId: z.string(),
            })
        ),
    }),
});

export type StoreLocatorOrganizationConfigurationResponseDto = z.infer<typeof StoreLocatorOrganizationConfigurationResponseDtoSchema>;
