import { z } from 'zod';

import { StoreLocatorLanguage } from '@malou-io/package-utils';

import { objectIdValidator } from '../../utils';
import { updateStoreLocatorParamsValidator } from '../common';
import { storeLocatorStorePageCallToActionsBlockValidator, storeLocatorStorePageGalleryBlockValidator } from '../get-store-page';
import { updateStoreLocatorStorePageDescriptionsBlockValidator } from './descriptions';
import { updateStoreLocatorStorePageInformationBlockValidator } from './information-block';
import { updateStoreLocatorStorePageReviewsBlockValidator } from './reviews-block';
import { updateStoreLocatorStorePageSocialNetworksBlockValidator } from './social-networks';

// -------------------------------------------------------------------------------

export const updateStoreLocatorStorePageParamsValidator = updateStoreLocatorParamsValidator;

export type UpdateStoreLocatorStorePageParamsDto = z.infer<typeof updateStoreLocatorStorePageParamsValidator>;

// -------------------------------------------------------------------------------
export const updateStoreLocatorStoreBodyValidator = z.array(
    z.object({
        restaurantId: objectIdValidator,
        lang: z.nativeEnum(StoreLocatorLanguage),
        information: updateStoreLocatorStorePageInformationBlockValidator.optional(),
        gallery: storeLocatorStorePageGalleryBlockValidator.optional(),
        reviews: updateStoreLocatorStorePageReviewsBlockValidator.optional(),
        callToActions: storeLocatorStorePageCallToActionsBlockValidator.optional(),
        descriptions: updateStoreLocatorStorePageDescriptionsBlockValidator.optional(),
        socialNetworks: updateStoreLocatorStorePageSocialNetworksBlockValidator.optional(),
    })
);

export type UpdateStoreLocatorStorePagesBodyDto = z.infer<typeof updateStoreLocatorStoreBodyValidator>;
