import { z } from 'zod';

import { imageValidator } from '../common';

const descriptionValidator = z.object({
    title: z.string(),
    image: imageValidator,
    blocks: z.array(
        z.object({
            title: z.string(),
            text: z.string(),
        })
    ),
});

export const updateStoreLocatorStorePageDescriptionsBlockValidator = z.object({
    items: z.array(descriptionValidator),
});
