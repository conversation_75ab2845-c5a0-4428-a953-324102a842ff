import { z } from 'zod';

import { StoreLocatorAiSettingsLanguageStyle, StoreLocatorRestaurantPageElementIds } from '@malou-io/package-utils';

import { objectIdValidator, urlValidator } from '../utils';
import { updateStoreLocatorParamsValidator } from './common';

//-----------------------------------------------------------------

export const getStoreLocatorOrganizationConfigurationValidator = z.object({
    cloudfrontDistributionId: z.string(),
    organizationName: z.string(),
    baseUrl: z.string(),
    tailwindConfig: z.string(),
    tailwindClassesMap: z.string(),
    favIconUrl: urlValidator(),
});

export type GetStoreLocatorOrganizationConfigurationDto = z.infer<typeof getStoreLocatorOrganizationConfigurationValidator>;

// -------------------------------------------------------------------------------

export const updateOrganizationConfigurationParamsValidator = updateStoreLocatorParamsValidator;

export type UpdateOrganizationConfigurationParamsDto = z.infer<typeof updateOrganizationConfigurationParamsValidator>;

// -------------------------------------------------------------------------------

export const updateOrganizationConfigurationAiSettingsBodyValidator = z.object({
    aiSettings: z.object({
        tone: z.array(z.string()).optional(),
        languageStyle: z.nativeEnum(StoreLocatorAiSettingsLanguageStyle).optional(),
        attributeIds: z.array(z.string()).optional(),
        restaurantKeywordIds: z.array(objectIdValidator).optional(),
        specialAttributes: z
            .array(
                z.object({
                    restaurantId: objectIdValidator,
                    text: z.string(),
                })
            )
            .optional(),
    }),
});

export type UpdateOrganizationConfigurationAiSettingsBodyDto = z.infer<typeof updateOrganizationConfigurationAiSettingsBodyValidator>;

// -------------------------------------------------------------------------------

export const updateRestaurantPagesParamsValidator = updateStoreLocatorParamsValidator;

export type UpdateRestaurantPagesParamsDto = z.infer<typeof updateRestaurantPagesParamsValidator>;

// --------------------------------------------------------------------------------

export const updateOrganizationConfigurationStorePagesBodyValidator = z.object({
    data: z.record(z.nativeEnum(StoreLocatorRestaurantPageElementIds), z.array(z.string())),
});

export type UpdateOrganizationConfigurationStorePagesBodyDto = z.infer<typeof updateOrganizationConfigurationStorePagesBodyValidator>;

// -------------------------------------------------------------------------------
