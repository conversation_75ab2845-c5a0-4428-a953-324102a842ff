import { FromSchema } from 'json-schema-to-ts';
import mongoose from 'mongoose';

import { DESERIALIZE_OPTIONS } from ':core/mongoose-json-schema/definitions/constants';
import { createMongooseSchemaFromJSONSchema } from ':core/mongoose-json-schema/definitions/jsonschema-to-mongoose';
import { storeLocatorRestaurantPageJSONSchema } from ':modules/store-locator-restaurant-page/store-locator-restaurant-page-schema';

const storeLocatorRestaurantPageSchema = createMongooseSchemaFromJSONSchema(storeLocatorRestaurantPageJSONSchema);

storeLocatorRestaurantPageSchema.index({
    restaurantId: 1,
});
storeLocatorRestaurantPageSchema.index({
    organizationId: 1,
});

storeLocatorRestaurantPageSchema.index(
    {
        organizationId: 1,
        status: 1,
        lang: 1,
        restaurantId: 1,
    },
    {
        unique: true,
    }
);

export type IStoreLocatorRestaurantPage = FromSchema<
    typeof storeLocatorRestaurantPageJSONSchema,
    {
        keepDefaultedPropertiesOptional: true;
        deserialize: DESERIALIZE_OPTIONS;
    }
>;

export const StoreLocatorRestaurantPageModel = mongoose.model<IStoreLocatorRestaurantPage>(
    storeLocatorRestaurantPageJSONSchema.title,
    storeLocatorRestaurantPageSchema
);
