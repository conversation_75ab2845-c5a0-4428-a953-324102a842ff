import { FromSchema } from 'json-schema-to-ts';
import { MongoError } from 'mongodb';
import mongoose from 'mongoose';

import { PlatformKey } from '@malou-io/package-utils';

import { DESERIALIZE_OPTIONS } from ':core/mongoose-json-schema/definitions/constants';
import { createMongooseSchemaFromJSONSchema } from ':core/mongoose-json-schema/definitions/jsonschema-to-mongoose';

import { postJSONSchema } from './post-schema';

const postSchema = createMongooseSchemaFromJSONSchema(postJSONSchema);

postSchema.virtual('feedback', {
    ref: 'Feedback',
    localField: 'feedbackId',
    foreignField: '_id',
    justOne: true,
});

postSchema.virtual('restaurant', {
    ref: 'Restaurant',
    localField: 'restaurantId',
    foreignField: '_id',
    justOne: true,
});

postSchema.virtual('platform', {
    ref: 'Platform',
    localField: 'platformId',
    foreignField: '_id',
    justOne: true,
});

postSchema.virtual('duplicatedFromRestaurant', {
    ref: 'Restaurant',
    localField: 'duplicatedFromRestaurantId',
    foreignField: '_id',
    justOne: true,
});

// partialFilterExpression authorizes the creation of multiple posts
// with no socialId and the same platformId
postSchema.index(
    { socialId: 1, platformId: 1 },
    {
        unique: true,
        partialFilterExpression: { socialId: { $exists: true } },
    }
);
postSchema.index({
    restaurantId: 1,
    key: 1,
    text: 1,
    socialCreatedAt: 1,
});

postSchema.index({
    restaurantId: 1,
    key: 1,
    keys: 1,
    published: 1,
    text: 1,
    socialCreatedAt: 1,
});

postSchema.index({
    bindingId: 1,
});

postSchema.index({
    platformId: 1,
    published: 1,
});

postSchema.index({ malouStoryId: 1 });
postSchema.index({ feedbackId: 1 });
postSchema.index({ 'author._id': 1, isStory: 1 }, { partialFilterExpression: { isStory: { $exists: true } } });
postSchema.index({ attachments: 1 });

postSchema.index({ restaurantId: 1, source: 1, sortDate: -1 });

postSchema.index({ tiktokPublishId: 1 }, { sparse: true });

/** From db */

postSchema.index({ socialId: 1 });
postSchema.index({ published: 1, socialCreatedAt: -1 });
postSchema.index({ 'author._id': 1, published: 1, text: 1, key: 1 });

/** */

// Handle 11000 duplicate key
postSchema.post('save', (error: Error, doc: unknown, next: (error?: Error) => void): void => {
    if (error instanceof MongoError && error.code === 11000) {
        return next({ duplicateRecordError: true } as any); // FIXME
    }
    return next(error);
});

postSchema.pre('validate', function (this: mongoose.Document & IPost, next) {
    if (this.key === PlatformKey.GMB && this.attachments && this.attachments.length > 1) {
        return next(new Error('GMB local posts cannot have more than one media.'));
    }
    return next();
});

export type IPost = FromSchema<
    typeof postJSONSchema,
    {
        keepDefaultedPropertiesOptional: true;
        deserialize: DESERIALIZE_OPTIONS;
    }
>;

export type IPostPublicationError = NonNullable<IPost['publicationErrors']>[number];
export type ISocialAttachment = NonNullable<IPost['socialAttachments']>[number];
export type IKeywordAnalysis = IPost['keywordAnalysis'];

export const PostModel = mongoose.model<IPost>(postJSONSchema.title, postSchema);
