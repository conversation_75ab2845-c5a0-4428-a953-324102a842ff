import { FromSchema } from 'json-schema-to-ts';
import mongoose from 'mongoose';

import { DESERIALIZE_OPTIONS } from ':core/mongoose-json-schema/definitions/constants';
import { createMongooseSchemaFromJSONSchema } from ':core/mongoose-json-schema/definitions/jsonschema-to-mongoose';

import { segmentAnalysesJSONSchema } from './segment-analyses-schema';

const segmentAnalysesSchema = createMongooseSchemaFromJSONSchema(segmentAnalysesJSONSchema);

segmentAnalysesSchema.virtual('segmentAnalysisParentTopics', {
    ref: 'SegmentAnalysisParentTopics',
    localField: 'segmentAnalysisParentTopicIds',
    foreignField: '_id',
    justOne: false,
});

segmentAnalysesSchema.index(
    { reviewSocialId: 1, platformKey: 1, category: 1, subcategory: 1, sentiment: 1, topic: 1, segment: 1 },
    { unique: true }
);
segmentAnalysesSchema.index({ platformSocialId: 1, platformKey: 1, reviewSocialCreatedAt: 1, topic: 1 });
segmentAnalysesSchema.index({ segmentAnalysisParentTopicIds: 1, reviewSocialCreatedAt: 1 });
segmentAnalysesSchema.index({ segmentAnalysisParentTopicIds: 1 });

export type ISegmentAnalysis = FromSchema<
    typeof segmentAnalysesJSONSchema,
    {
        keepDefaultedPropertiesOptional: true;
        deserialize: DESERIALIZE_OPTIONS;
    }
>;

export const SegmentAnalysisModel = mongoose.model<ISegmentAnalysis>(segmentAnalysesJSONSchema.title, segmentAnalysesSchema);
