import { IsExact } from ':core/mongoose-json-schema/type-utils';
import { IApi<PERSON><PERSON> } from ':modules/api-keys/api-key-model';
import { VirtualApiKey } from ':modules/api-keys/virtual-api-key';
import { IAttribute } from ':modules/attributes/attribute-model';
import { VirtualAttribute } from ':modules/attributes/attribute-virtual';
import { IAutomation } from ':modules/automations/automation-model';
import { IIntelligentSubjectAutomation } from ':modules/automations/discriminators/intelligent-subject-automation/intelligent-subject-automation-model';
import { VirtualIntelligentSubjectAutomation } from ':modules/automations/discriminators/intelligent-subject-automation/virtual-intelligent-subject-automation';
import { IReviewReplyAutomation } from ':modules/automations/discriminators/review-reply-automation/review-reply-automation-model';
import { VirtualReviewReplyAutomation } from ':modules/automations/discriminators/review-reply-automation/virtual-review-reply-automation';
import { VirtualAutomation } from ':modules/automations/virtual-automation';
import { ICampaign } from ':modules/campaigns/campaign-model';
import { VirtualCampaign, VirtualContactInteractions } from ':modules/campaigns/virtual-campaign';
import { ICategory } from ':modules/categories/category-model';
import { VirtualCategory } from ':modules/categories/virtual-category';
import { IClient } from ':modules/clients/client-model';
import { VirtualClient } from ':modules/clients/virtual-client';
import { IComment } from ':modules/comments/comment-model';
import { VirtualComment } from ':modules/comments/virtual-comment';
import { IConversation } from ':modules/conversations/conversation-model';
import { VirtualConversation } from ':modules/conversations/virtual-conversation';
import { ICredential } from ':modules/credentials/credential-model';
import { VirtualCredential } from ':modules/credentials/virtual-credential';
import { ICustomer } from ':modules/customers/customer-model';
import { VirtualCustomer } from ':modules/customers/virtual-customer';
import { IDiagnostic } from ':modules/diagnostics';
import { VirtualDiagnostic, VirtualDiagnosticRestaurant } from ':modules/diagnostics/virtual-diagnostic';
import { IGiftDraw } from ':modules/gift-draws/gift-draw-model';
import { VirtualGiftDraw } from ':modules/gift-draws/virtual-gift-draw';
import { IGiftStock } from ':modules/gift-stocks/gift-stock-model';
import { VirtualGiftStock } from ':modules/gift-stocks/virtual-gift-stock';
import { IKeywordTemp } from ':modules/keywords-temp/keyword-model';
import { VirtualBricks, VirtualKeywordTemp } from ':modules/keywords-temp/virtual-keyword';
import { IKeyword } from ':modules/keywords/keyword-model';
import { VirtualKeyword } from ':modules/keywords/virtual-keyword';
import { ILabel } from ':modules/labels/label-model';
import { VirtualLabel } from ':modules/labels/virtual-label';
import { IMedia } from ':modules/media/media-model';
import { VirtualMedia } from ':modules/media/virtual-media';
import { IMention } from ':modules/mentions/mention-model';
import { VirtualMention } from ':modules/mentions/virtual-mention';
import { IMessage } from ':modules/messages/message-model';
import { VirtualMessage } from ':modules/messages/virtual-message';
import { ISticker } from ':modules/nfcs/discriminators/stickers/sticker-model';
import { VirtualSticker } from ':modules/nfcs/discriminators/stickers/virtual-sticker';
import { ITotem } from ':modules/nfcs/discriminators/totems/totem-model';
import { VirtualTotem } from ':modules/nfcs/discriminators/totems/virtual-totem';
import { INfc } from ':modules/nfcs/nfc-model';
import { VirtualNfc } from ':modules/nfcs/virtual-nfc';
import { INotification } from ':modules/notifications/notification-model';
import { VirtualNotification } from ':modules/notifications/virtual-notification';
import { IOrganization } from ':modules/organizations/organization-model';
import { VirtualOrganization } from ':modules/organizations/virtual-organization';
import { IPlatformInsight } from ':modules/platform-insights/platform-insight-model';
import { VirtualPlatformInsight } from ':modules/platform-insights/virtual-platform-insight';
import { IPlatform } from ':modules/platforms/platform-model';
import { VirtualPlatform } from ':modules/platforms/virtual-platform';
import { IPost } from ':modules/posts/post-model';
import { VirtualPost } from ':modules/posts/virtual-post';
import { IPrivateReview } from ':modules/private-reviews/private-review-model';
import { VirtualPrivateReview } from ':modules/private-reviews/virtual-private-review';
import { IPushNotification } from ':modules/push-notifications/push-notification-model';
import { VirtualPushNotification } from ':modules/push-notifications/virtual-push-notification';
import { IReport } from ':modules/reports/report-model';
import { VirtualConfigurations, VirtualReport } from ':modules/reports/virtual-report';
import { IRestaurantAiSettings } from ':modules/restaurant-ai-settings/restaurant-ai-settings-model';
import { VirtualRestaurantAiSettings, VirtualTranslations } from ':modules/restaurant-ai-settings/virtual-restaurant-ai-settings';
import { IRestaurantAttribute } from ':modules/restaurant-attributes/restaurant-attribute-model';
import { VirtualRestaurantAttribute } from ':modules/restaurant-attributes/virtual-restaurant-attribute';
import { IRestaurantKeyword } from ':modules/restaurant-keywords/restaurant-keyword-model';
import { VirtualRestaurantKeyword } from ':modules/restaurant-keywords/virtual-restaurant-keyword';
import { IRestaurant } from ':modules/restaurants/restaurant-model';
import { VirtualRestaurant } from ':modules/restaurants/virtual-restaurant';
import { IReview } from ':modules/reviews/review-model';
import { VirtualReview } from ':modules/reviews/virtual-review';
import { IRoiInsights } from ':modules/roi-insights/roi-insights-model';
import { VirtualRoiInsights } from ':modules/roi-insights/virtual-roi-insights';
import { IRoiSettings } from ':modules/roi-settings/roi-settings-model';
import { VirtualRoiSettings } from ':modules/roi-settings/virtual-roi-settings';
import { IScan } from ':modules/scans/scan-model';
import { VirtualScan } from ':modules/scans/virtual-scan';
import { ISegmentAnalysis } from ':modules/segment-analyses/segment-analyses-model';
import { VirtualSegmentAnalysis } from ':modules/segment-analyses/virtual-segment-analyses';
import { ISegmentAnalysisParentTopics } from ':modules/segment-analysis-parent-topics/segment-analysis-parent-topics-model';
import { VirtualSegmentAnalysisParentTopics } from ':modules/segment-analysis-parent-topics/virtual-segment-analysis-parent-topics';
import { IStoreLocatorOrganizationConfig } from ':modules/store-locator-organization-config/store-locator-organization-config-model';
import { VirtualStoreLocatorOrganizationConfig } from ':modules/store-locator-organization-config/virtual-store-locator-organization-config';
import { ITemplate } from ':modules/templates/template-model';
import { VirtualTemplate } from ':modules/templates/virtual-template';
import { IUserRestaurant } from ':modules/user-restaurants/user-restaurant-model';
import { VirtualUserRestaurant } from ':modules/user-restaurants/virtual-user-restaurant';
import { IUser } from ':modules/users/user-model';
import { VirtualReceiveMessagesNotifications, VirtualSettings, VirtualUser } from ':modules/users/virtual-user';
import { IAggregatedWheelOfFortune } from ':modules/wheels-of-fortune/discriminators/aggregated-wheels-of-fortune/aggregated-wheel-of-fortune-model';
import { IRestaurantWheelOfFortune } from ':modules/wheels-of-fortune/discriminators/restaurant-wheels-of-fortune/restaurant-wheel-of-fortune-model';
import { VirtualRestaurantWheelOfFortune } from ':modules/wheels-of-fortune/discriminators/restaurant-wheels-of-fortune/virtual-restaurant-wheel-of-fortune';
import { VirtualWheelOfFortune, VirtualWheelOfFortuneParameters } from ':modules/wheels-of-fortune/virtual-wheel-of-fortune';
import { IWheelOfFortune } from ':modules/wheels-of-fortune/wheel-of-fortune-model';

// it's literally a switch case
export type VirtualsGetter<TYPE_OF_THE_MODEL> =
    IsExact<TYPE_OF_THE_MODEL, IRestaurant> extends true
        ? VirtualRestaurant
        : IsExact<TYPE_OF_THE_MODEL, IUser> extends true
          ? VirtualUser
          : IsExact<TYPE_OF_THE_MODEL, IPlatform> extends true
            ? VirtualPlatform
            : IsExact<TYPE_OF_THE_MODEL, ICredential> extends true
              ? VirtualCredential
              : IsExact<TYPE_OF_THE_MODEL, IReview> extends true
                ? VirtualReview
                : IsExact<TYPE_OF_THE_MODEL, IOrganization> extends true
                  ? VirtualOrganization
                  : IsExact<TYPE_OF_THE_MODEL, IApiKey> extends true
                    ? VirtualApiKey
                    : IsExact<TYPE_OF_THE_MODEL, IAutomation> extends true
                      ? VirtualAutomation
                      : IsExact<TYPE_OF_THE_MODEL, IAttribute> extends true
                        ? VirtualAttribute
                        : IsExact<TYPE_OF_THE_MODEL, IReviewReplyAutomation> extends true
                          ? VirtualReviewReplyAutomation
                          : IsExact<TYPE_OF_THE_MODEL, ICampaign> extends true
                            ? VirtualCampaign
                            : IsExact<TYPE_OF_THE_MODEL, ICategory> extends true
                              ? VirtualCategory
                              : IsExact<TYPE_OF_THE_MODEL, IClient> extends true
                                ? VirtualClient
                                : IsExact<TYPE_OF_THE_MODEL, IComment> extends true
                                  ? VirtualComment
                                  : IsExact<TYPE_OF_THE_MODEL, IConversation> extends true
                                    ? VirtualConversation
                                    : IsExact<TYPE_OF_THE_MODEL, ICredential> extends true
                                      ? VirtualCredential
                                      : IsExact<TYPE_OF_THE_MODEL, ICustomer> extends true
                                        ? VirtualCustomer
                                        : IsExact<TYPE_OF_THE_MODEL, IKeyword> extends true
                                          ? VirtualKeyword
                                          : IsExact<TYPE_OF_THE_MODEL, IKeywordTemp> extends true
                                            ? VirtualKeywordTemp
                                            : IsExact<TYPE_OF_THE_MODEL, ILabel> extends true
                                              ? VirtualLabel
                                              : IsExact<TYPE_OF_THE_MODEL, IMedia> extends true
                                                ? VirtualMedia
                                                : IsExact<TYPE_OF_THE_MODEL, IMention> extends true
                                                  ? VirtualMention
                                                  : IsExact<TYPE_OF_THE_MODEL, IMessage> extends true
                                                    ? VirtualMessage
                                                    : IsExact<TYPE_OF_THE_MODEL, INfc> extends true
                                                      ? VirtualNfc
                                                      : IsExact<TYPE_OF_THE_MODEL, INotification> extends true
                                                        ? VirtualNotification
                                                        : IsExact<TYPE_OF_THE_MODEL, IPlatformInsight> extends true
                                                          ? VirtualPlatformInsight
                                                          : IsExact<TYPE_OF_THE_MODEL, IPost> extends true
                                                            ? VirtualPost
                                                            : IsExact<TYPE_OF_THE_MODEL, IPrivateReview> extends true
                                                              ? VirtualPrivateReview
                                                              : IsExact<TYPE_OF_THE_MODEL, IPushNotification> extends true
                                                                ? VirtualPushNotification
                                                                : IsExact<TYPE_OF_THE_MODEL, IReport> extends true
                                                                  ? VirtualReport
                                                                  : IsExact<TYPE_OF_THE_MODEL, IRestaurantAttribute> extends true
                                                                    ? VirtualRestaurantAttribute
                                                                    : IsExact<TYPE_OF_THE_MODEL, IScan> extends true
                                                                      ? VirtualScan
                                                                      : IsExact<TYPE_OF_THE_MODEL, ITemplate> extends true
                                                                        ? VirtualTemplate
                                                                        : IsExact<TYPE_OF_THE_MODEL, IUserRestaurant> extends true
                                                                          ? VirtualUserRestaurant
                                                                          : IsExact<TYPE_OF_THE_MODEL, IRoiSettings> extends true
                                                                            ? VirtualRoiSettings
                                                                            : IsExact<TYPE_OF_THE_MODEL, IUser['settings']> extends true
                                                                              ? VirtualSettings
                                                                              : IsExact<
                                                                                      TYPE_OF_THE_MODEL,
                                                                                      IUser['settings']['receiveMessagesNotifications']
                                                                                  > extends true
                                                                                ? VirtualReceiveMessagesNotifications
                                                                                : IsExact<
                                                                                        TYPE_OF_THE_MODEL,
                                                                                        ICampaign['contactInteractions'][0]
                                                                                    > extends true
                                                                                  ? VirtualContactInteractions
                                                                                  : IsExact<TYPE_OF_THE_MODEL, IWheelOfFortune> extends true
                                                                                    ? VirtualWheelOfFortune
                                                                                    : IsExact<
                                                                                            TYPE_OF_THE_MODEL,
                                                                                            IRestaurantWheelOfFortune
                                                                                        > extends true
                                                                                      ? VirtualRestaurantWheelOfFortune
                                                                                      : IsExact<
                                                                                              TYPE_OF_THE_MODEL,
                                                                                              IAggregatedWheelOfFortune
                                                                                          > extends true
                                                                                        ? // same as WheelOfFortune because they are the same
                                                                                          VirtualWheelOfFortune
                                                                                        : IsExact<
                                                                                                TYPE_OF_THE_MODEL,
                                                                                                IWheelOfFortune['parameters']
                                                                                            > extends true
                                                                                          ? VirtualWheelOfFortuneParameters
                                                                                          : IsExact<
                                                                                                  TYPE_OF_THE_MODEL,
                                                                                                  IGiftDraw
                                                                                              > extends true
                                                                                            ? VirtualGiftDraw
                                                                                            : IsExact<
                                                                                                    TYPE_OF_THE_MODEL,
                                                                                                    IReport['configurations'][0]
                                                                                                > extends true
                                                                                              ? VirtualConfigurations
                                                                                              : IsExact<
                                                                                                      TYPE_OF_THE_MODEL,
                                                                                                      IGiftStock
                                                                                                  > extends true
                                                                                                ? VirtualGiftStock
                                                                                                : IsExact<
                                                                                                        TYPE_OF_THE_MODEL,
                                                                                                        ITotem
                                                                                                    > extends true
                                                                                                  ? VirtualTotem
                                                                                                  : IsExact<
                                                                                                          TYPE_OF_THE_MODEL,
                                                                                                          ISticker
                                                                                                      > extends true
                                                                                                    ? VirtualSticker
                                                                                                    : IsExact<
                                                                                                            TYPE_OF_THE_MODEL,
                                                                                                            IRestaurantKeyword
                                                                                                        > extends true
                                                                                                      ? VirtualRestaurantKeyword
                                                                                                      : IsExact<
                                                                                                              TYPE_OF_THE_MODEL,
                                                                                                              IKeywordTemp['bricks'][0]
                                                                                                          > extends true
                                                                                                        ? VirtualBricks
                                                                                                        : IsExact<
                                                                                                                TYPE_OF_THE_MODEL,
                                                                                                                IRoiInsights
                                                                                                            > extends true
                                                                                                          ? VirtualRoiInsights
                                                                                                          : IsExact<
                                                                                                                  TYPE_OF_THE_MODEL,
                                                                                                                  ISegmentAnalysis
                                                                                                              > extends true
                                                                                                            ? VirtualSegmentAnalysis
                                                                                                            : IsExact<
                                                                                                                    TYPE_OF_THE_MODEL,
                                                                                                                    ISegmentAnalysisParentTopics
                                                                                                                > extends true
                                                                                                              ? VirtualSegmentAnalysisParentTopics
                                                                                                              : IsExact<
                                                                                                                      TYPE_OF_THE_MODEL,
                                                                                                                      IDiagnostic
                                                                                                                  > extends true
                                                                                                                ? VirtualDiagnostic
                                                                                                                : IsExact<
                                                                                                                        TYPE_OF_THE_MODEL,
                                                                                                                        IDiagnostic['restaurant']
                                                                                                                    > extends true
                                                                                                                  ? VirtualDiagnosticRestaurant
                                                                                                                  : IsExact<
                                                                                                                          TYPE_OF_THE_MODEL,
                                                                                                                          IRestaurantAiSettings
                                                                                                                      > extends true
                                                                                                                    ? VirtualRestaurantAiSettings
                                                                                                                    : IsExact<
                                                                                                                            TYPE_OF_THE_MODEL,
                                                                                                                            IRestaurantAiSettings['reviewSettings']
                                                                                                                        > extends true
                                                                                                                      ? VirtualTranslations
                                                                                                                      : IsExact<
                                                                                                                              TYPE_OF_THE_MODEL,
                                                                                                                              IStoreLocatorOrganizationConfig
                                                                                                                          > extends true
                                                                                                                        ? VirtualStoreLocatorOrganizationConfig
                                                                                                                        : IsExact<
                                                                                                                                TYPE_OF_THE_MODEL,
                                                                                                                                IIntelligentSubjectAutomation
                                                                                                                            > extends true
                                                                                                                          ? VirtualIntelligentSubjectAutomation
                                                                                                                          : unknown;
