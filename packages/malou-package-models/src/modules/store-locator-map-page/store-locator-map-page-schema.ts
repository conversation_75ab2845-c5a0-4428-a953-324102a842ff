import { cleanUrl, isValidUrl, StoreLocatorLanguage, StoreLocatorPageStatus } from '@malou-io/package-utils';

import { JSONSchemaExtraProps } from ':core/mongoose-json-schema/definitions/types';

export const storeLocatorMapPageJSONSchema = {
    $schema: 'http://json-schema.org/draft-06/schema#',
    type: 'object',
    additionalProperties: false,
    properties: {
        _id: {
            type: 'string',
            format: 'objectId',
        },
        status: {
            type: 'string',
            enum: Object.values(StoreLocatorPageStatus),
        },
        organizationId: {
            type: 'string',
            format: 'objectId',
        },
        lang: {
            type: 'string',
            enum: Object.values(StoreLocatorLanguage),
        },
        fullUrl: {
            type: 'string',
            format: 'uri',
            validate: {
                validator: (v) => isValidUrl(v),
                message: (props) => `Url should be valid, value: ${props.value}`,
            },
            set: cleanUrl,
        },
        relativePath: {
            type: 'string',
            description: 'Relative path to the store locator map page',
        },
        blocks: {
            $ref: '#/definitions/Blocks',
        },
        createdAt: {
            type: 'string',
            format: 'date-time',
        },
        updatedAt: {
            type: 'string',
            format: 'date-time',
        },
    },
    definitions: {
        Blocks: {
            type: 'object',
            additionalProperties: false,
            properties: {
                head: {
                    $ref: '#/definitions/HeadBlock',
                },
                map: {
                    $ref: '#/definitions/MapBlock',
                },
            },
            required: ['head', 'map'],
            title: 'Blocks',
        },
        HeadBlock: {
            type: 'object',
            additionalProperties: false,
            properties: {
                title: {
                    type: 'string',
                },
                description: {
                    type: 'string',
                },
                twitterDescription: {
                    type: 'string',
                },
                keywords: {
                    type: 'string',
                },
                schemaOrgCuisineType: {
                    type: 'string',
                },
                facebookImageUrl: {
                    type: 'string',
                    format: 'uri',
                    validate: {
                        validator: (v) => isValidUrl(v),
                        message: (props) => `Url should be valid, value: ${props.value}`,
                    },
                },
                twitterImageUrl: {
                    type: 'string',
                    format: 'uri',
                    validate: {
                        validator: (v) => isValidUrl(v),
                        message: (props) => `Url should be valid, value: ${props.value}`,
                    },
                },
                snippetImageUrl: {
                    type: 'string',
                    format: 'uri',
                    validate: {
                        validator: (v) => isValidUrl(v),
                        message: (props) => `Url should be valid, value: ${props.value}`,
                    },
                },
                backup: {
                    type: 'object',
                    additionalProperties: true,
                    properties: {},
                },
            },
            required: [
                'title',
                'description',
                'twitterDescription',
                'keywords',
                'schemaOrgCuisineType',
                'facebookImageUrl',
                'twitterImageUrl',
                'snippetImageUrl',
            ],
            title: 'HeadBlock',
        },
        MapBlock: {
            type: 'object',
            additionalProperties: false,
            properties: {
                pins: {
                    type: 'object',
                    additionalProperties: false,
                    properties: {
                        activePin: {
                            $ref: '#/definitions/Image',
                        },
                        inactivePin: {
                            $ref: '#/definitions/Image',
                        },
                    },
                    required: ['activePin', 'inactivePin'],
                },
                popup: {
                    type: 'object',
                    additionalProperties: false,
                    properties: {
                        noStoreImage: {
                            $ref: '#/definitions/Image',
                        },
                    },
                    required: ['noStoreImage'],
                },
            },
            required: ['pins', 'popup'],
            title: 'MapBlock',
        },
        Image: {
            type: 'object',
            additionalProperties: false,
            properties: {
                description: {
                    type: 'string',
                },
                url: {
                    type: 'string',
                    format: 'uri',
                    validate: {
                        validator: (v) => isValidUrl(v),
                        message: (props) => `Url should be valid, value: ${props.value}`,
                    },
                },
                mediaId: {
                    type: 'string',
                    format: 'objectId',
                },
            },
            required: ['description', 'url'],
            title: 'Image',
        },
    },
    required: ['_id', 'organizationId', 'lang', 'fullUrl', 'blocks', 'relativePath', 'createdAt', 'updatedAt', 'status'],
    title: 'StoreLocatorMapPage',
} as const satisfies JSONSchemaExtraProps;
