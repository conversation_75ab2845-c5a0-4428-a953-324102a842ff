import { Column, Container, Img, Row, Text } from '@react-email/components';
import { DateTime } from 'luxon';

import { BasicReviewProps } from '@malou-io/package-dto';
import { Locale } from '@malou-io/package-utils';

import { Translation } from ':shared/services';
import { getImage } from ':shared/utils';

import { RatingStars } from './rating-stars';

export const BasicReviewBox = (props: BasicReviewProps & { locale: Locale }) => {
    const { socialAttachmentUrls, reviewerName, rating, text, socialCreatedAt, platformKey, locale, translatedFrom } = props;

    const translate = new Translation(locale).getTranslator();

    return (
        <Container className="mb-5 px-5 py-3 border border-solid border-[#F2F2FF] rounded" style={{ background: '#F9FAFF' }}>
            <Row align="left" className="mb-2">
                <Column align="left" width={50}>
                    <Img src={getImage(platformKey)} height={35} width={35} />
                </Column>
                <Column align="left">
                    <Text className="m-0 text-[12px] text-malou-color-text-1">{reviewerName}</Text>
                    <Text className="m-0 text-[12px] text-malou-color-text-2 italic">
                        {DateTime.fromJSDate(socialCreatedAt).toFormat('dd MMMM yyyy', {
                            locale,
                        })}
                    </Text>
                </Column>

                <Column align="right">
                    <RatingStars rating={rating}></RatingStars>
                </Column>
            </Row>

            <Row align="left" width={100} className="mb-2">
                {!!socialAttachmentUrls?.length &&
                    Array(6)
                        .fill(0)
                        .map((_, index) => (
                            <Column align="left">
                                {socialAttachmentUrls[index] && (
                                    <Img src={socialAttachmentUrls[index]} width={48} height={48} className="rounded mr-2" />
                                )}
                            </Column>
                        ))}
            </Row>
            <Row>
                <Column align="left">
                    <span className="text-[12px] text-malou-color-text-2">{text}</span>
                    {translatedFrom && (
                        <Text className="m-0 text-[10px] italic font-[400] text-malou-color-text-2 sm:text-[12px]">
                            {translate?.reviews.translated_from({
                                language: translate?.common.langs[translatedFrom as keyof typeof translate.common.langs](),
                            })}
                        </Text>
                    )}
                </Column>
            </Row>
        </Container>
    );
};
