import { ReviewIntelligentSubjectProps } from '@malou-io/package-dto';
import { EmailPadding, IntelligentSubjectName, Locale, PlatformKey, ReviewAnalysisSentiment } from '@malou-io/package-utils';

import { MalouLayout } from ':shared/components';
import { MalouBasicHeader } from ':shared/components/layout/header';
import { Translation } from ':shared/services';
import { EmailMargin } from ':shared/types/report.enum';

import { Content } from './content';

export const ReviewIntelligentSubjectTemplate = (props: ReviewIntelligentSubjectProps) => {
    const { locale, unsubscribeLink, trackingUrl } = props;
    const translator = new Translation(locale).getTranslator();
    return (
        <MalouLayout
            customHeader={
                <MalouBasicHeader
                    locale={locale}
                    dateTimeFormat="MMM yyyy"
                    notificationTitle={translator.reviews.intelligent_subjects.notificationTitle()}
                    trackingUrl={trackingUrl}
                    xcss="text-malou-color-state-error rounded bg-malou-color-state-error/[.15] font-medium"
                />
            }
            context={{ locale, unsubscribeLink, trackingUrl }}
            emailMarginY={EmailMargin.LOW}
            paddingX={EmailPadding.LOW}>
            <Content {...props} />
        </MalouLayout>
    );
};

ReviewIntelligentSubjectTemplate.defaultProps = {
    locale: Locale.EN,
    receiver: 'Jean-Christophe',
    restaurantName: 'La bonne fourchette',
    link: 'https://malou.io',
    reviewerProfilePictureUrl: 'https://picsum.photos/600/400',
    socialAttachmentUrls: [
        'https://picsum.photos/600/400',
        'https://picsum.photos/600/400',
        'https://picsum.photos/600/400',
        'https://picsum.photos/600/400',
        'https://picsum.photos/600/400',
        'https://picsum.photos/600/400',
    ],
    reviewerName: 'Jean',
    rating: 2,
    text: 'Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt ut labore et dolore magna aliqua.',
    socialCreatedAt: new Date('2024-08-11'),
    platformName: 'Google',
    platformKey: PlatformKey.GMB,
    intelligentSubjects: [
        {
            subject: IntelligentSubjectName.DISCRIMINATION,
            isDetected: false,
        },
        {
            subject: IntelligentSubjectName.HYGIENE,
            isDetected: true,
            sentiment: ReviewAnalysisSentiment.NEGATIVE,
        },
    ],
    translatedFrom: 'fr',
};

export default ReviewIntelligentSubjectTemplate;
