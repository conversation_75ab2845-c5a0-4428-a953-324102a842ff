/* eslint-disable @typescript-eslint/member-ordering */
import { AfterViewInit, Directive, ElementRef, inject, NgZone, OnDestroy, output } from '@angular/core';
import { debounceTime, fromEvent } from 'rxjs';

export type ScrollState = 'noScroll' | 'atStart' | 'scrolling' | 'atEnd';

@Directive({
    selector: '[appScrollState]',
    standalone: true,
})
export class ScrollStateDirective implements AfterViewInit, OnDestroy {
    private readonly _el = inject(ElementRef<HTMLElement>);
    private readonly _ngZone = inject(NgZone);

    readonly horizontalScrollState = output<ScrollState>();
    readonly verticalScrollState = output<ScrollState>();

    private _previousHorizontalState: ScrollState | undefined = undefined;
    private _previousVerticalState: ScrollState | undefined = undefined;

    readonly resizeObserver = new ResizeObserver(() => {
        this._emitStates();
    });

    constructor() {
        this.resizeObserver.observe(this._el.nativeElement);
        this._ngZone.runOutsideAngular(() => {
            fromEvent(this._el.nativeElement, 'scroll')
                .pipe(debounceTime(25))
                .subscribe(() => this._emitStates());
        });
    }

    ngAfterViewInit(): void {
        this._emitStates();
    }

    ngOnDestroy(): void {
        this.resizeObserver.disconnect();
    }

    private _emitStates(): void {
        const el = this._el.nativeElement;

        const hState = this._getHorizontalState(el);
        const vState = this._getVerticalState(el);

        if (hState !== this._previousHorizontalState) {
            this.horizontalScrollState.emit(hState);
            this._previousHorizontalState = hState;
        }

        if (vState !== this._previousVerticalState) {
            this.verticalScrollState.emit(vState);
            this._previousVerticalState = vState;
        }
    }

    private _getHorizontalState(el: HTMLElement): ScrollState {
        if (el.scrollWidth <= el.clientWidth) {
            return 'noScroll';
        }
        if (el.scrollLeft === 0) {
            return 'atStart';
        }
        if (el.scrollLeft + el.clientWidth >= el.scrollWidth) {
            return 'atEnd';
        }
        return 'scrolling';
    }

    private _getVerticalState(el: HTMLElement): ScrollState {
        if (el.scrollHeight <= el.clientHeight) {
            return 'noScroll';
        }
        if (el.scrollTop === 0) {
            return 'atStart';
        }
        if (el.scrollTop + el.clientHeight >= el.scrollHeight) {
            return 'atEnd';
        }
        return 'scrolling';
    }
}
