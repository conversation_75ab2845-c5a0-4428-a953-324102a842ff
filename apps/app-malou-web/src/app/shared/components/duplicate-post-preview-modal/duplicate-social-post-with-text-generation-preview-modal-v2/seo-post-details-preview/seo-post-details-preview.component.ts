import { NgTemplateOutlet } from '@angular/common';
import { ChangeDetectionStrategy, Component, inject, input, OnInit, output, signal } from '@angular/core';
import { FormControl, ReactiveFormsModule } from '@angular/forms';
import { MatIconModule } from '@angular/material/icon';
import { MatTooltipModule } from '@angular/material/tooltip';
import { TranslateModule, TranslateService } from '@ngx-translate/core';
import { DateTime } from 'luxon';

import { CallToActionType, isBeforeToday, KeywordScoreTextType, SeoPostTopic } from '@malou-io/package-utils';

import { eventTitleTextLimit, postCaptionTextLimit } from ':core/constants';
import { PostDatePickerComponent } from ':modules/posts-v2/post-date-picker/post-date-picker.component';
import { PostToDuplicate } from ':modules/posts-v2/social-posts/models/post-to-duplicate';
import { NewPostModalContext } from ':modules/posts/context/new-post-modal.context';
import { CallToActionOption } from ':modules/posts/new-post-modal/new-post-modal.component';
import { PostCallToActionComponent } from ':modules/posts/new-post-modal/post-call-to-action/post-call-to-action.component';
import { PostCaptionComponent } from ':modules/posts/new-post-modal/post-caption/post-caption.component';
import { InputDatePickerComponent } from ':shared/components/input-date-picker/input-date-picker.component';
import { InputTextComponent } from ':shared/components/input-text/input-text.component';
import { KeywordsScoreGaugeComponent } from ':shared/components/keywords-score-gauge/keywords-score-gauge.component';
import { KeywordsScoreGaugeType } from ':shared/components/keywords-score-gauge/keywords-score-gauge.interface';
import { SimpleSelectComponent } from ':shared/components/simple-select/simple-select.component';
import { isPastHour } from ':shared/helpers';
import { getCallToActionDefaultUrl } from ':shared/helpers/call-to-action-url';
import { createTextWithEmoji, CursorPosition } from ':shared/helpers/text-area-emoji.helpers';
import { Keyword, Post, Restaurant } from ':shared/models';
import { SvgIcon } from ':shared/modules/svg-icon.enum';
import { ApplySelfPurePipe } from ':shared/pipes/apply-fn.pipe';
import { EnumTranslatePipe } from ':shared/pipes/enum-translate.pipe';
import { ImagePathResolverPipe } from ':shared/pipes/image-path-resolver.pipe';

@Component({
    selector: 'app-seo-post-details-preview',
    imports: [
        NgTemplateOutlet,
        MatTooltipModule,
        MatIconModule,
        TranslateModule,
        ReactiveFormsModule,
        SimpleSelectComponent,
        KeywordsScoreGaugeComponent,
        InputTextComponent,
        InputDatePickerComponent,
        PostCallToActionComponent,
        PostCaptionComponent,
        PostDatePickerComponent,
        ApplySelfPurePipe,
        ImagePathResolverPipe,
    ],
    templateUrl: './seo-post-details-preview.component.html',
    styleUrl: './seo-post-details-preview.component.scss',
    changeDetection: ChangeDetectionStrategy.OnPush,
})
export class SeoPostDetailsPreviewComponent implements OnInit {
    readonly post = input.required<PostToDuplicate>();
    readonly restaurantKeywords = input.required<Keyword[]>();
    readonly restaurant = input.required<Restaurant>();
    readonly valid = output<boolean>();
    readonly errors = output<string[]>();

    readonly newPostModalContext = inject(NewPostModalContext);
    private readonly _translateService = inject(TranslateService);
    private readonly _enumTranslatePipe = inject(EnumTranslatePipe);

    readonly SvgIcon = SvgIcon;
    readonly SeoPostTopic = SeoPostTopic;
    readonly KeywordsScoreGaugeType = KeywordsScoreGaugeType;

    readonly gaugeTextType = signal<KeywordScoreTextType>(KeywordScoreTextType.POST);
    readonly EVENT_TITLE_TEXT_LIMIT = eventTitleTextLimit;
    readonly DEFAULT_MIN_EVENT_DATE = DateTime.now().plus({ hours: 1 }).toJSDate();
    readonly DEFAULT_MAX_EVENT_DATE = DateTime.now().plus({ years: 1 }).minus({ days: 1 }).toJSDate();
    readonly MIN_DATE = new Date();
    readonly POST_CAPTION_TEXT_LIMIT = postCaptionTextLimit;

    postTopics: { type: SeoPostTopic; text: string }[] = [];
    callToActions: CallToActionOption[] = [];

    get postTopic(): { type: SeoPostTopic; text: string } | null {
        return this.newPostModalContext.postTopic;
    }

    get postDescriptionControl(): FormControl {
        return this.newPostModalContext.postForm.get('post.text') as FormControl;
    }

    ngOnInit(): void {
        this.newPostModalContext.currentPost.set(Post.fromSocialPostToDuplicate(this.post()));
        this.newPostModalContext.postLang.set(this.post().language ?? null);

        this._initializeForm(this.restaurant());
        this.onSelectCallToAction(CallToActionType.NONE);
    }

    private _duplicatedPostErrors(postText?: string | null, postPlannedPublicationDate?: Date | null): string[] {
        const errorMessages: string[] = [];

        if (postText?.trim().length === 0) {
            errorMessages.push(this._translateService.instant('social_posts.upsert_social_post_modal.errors.add_text'));
        }

        if (!postPlannedPublicationDate) {
            errorMessages.push(
                this._translateService.instant('social_posts.upsert_social_post_modal.errors.planned_publication_date_in_past')
            );
        } else {
            // eslint-disable-next-line @typescript-eslint/quotes
            const hourWithMinute = DateTime.fromJSDate(postPlannedPublicationDate).toFormat("HH':'mm");
            if (isBeforeToday(postPlannedPublicationDate) || isPastHour({ date: postPlannedPublicationDate, hourWithMinute })) {
                errorMessages.push(
                    this._translateService.instant('social_posts.upsert_social_post_modal.errors.planned_publication_date_in_past')
                );
            }
        }

        return errorMessages;
    }

    private _initializeForm(restaurant: Restaurant): void {
        this.postTopics = [
            { type: SeoPostTopic.STANDARD, text: this._translateService.instant('posts.new_post.new') },
            { type: SeoPostTopic.EVENT, text: this._translateService.instant('posts.new_post.event') },
            { type: SeoPostTopic.OFFER, text: this._translateService.instant('posts.new_post.offer') },
        ];

        this.callToActions = [
            {
                type: CallToActionType.BOOK,
                text: this._enumTranslatePipe.transform(CallToActionType.BOOK, 'call_to_action_type'),
                actionLink: getCallToActionDefaultUrl(CallToActionType.BOOK, restaurant),
                disabledWithTooltip: !getCallToActionDefaultUrl(CallToActionType.BOOK, restaurant),
                tooltipMessage: this._translateService.instant('posts.new_post.add_informations_to_use'),
            },
            {
                type: CallToActionType.ORDER,
                text: this._enumTranslatePipe.transform(CallToActionType.ORDER, 'call_to_action_type'),
                actionLink: getCallToActionDefaultUrl(CallToActionType.ORDER, restaurant),
                disabledWithTooltip: !getCallToActionDefaultUrl(CallToActionType.ORDER, restaurant),
                tooltipMessage: this._translateService.instant('posts.new_post.add_informations_to_use'),
            },
            {
                type: CallToActionType.LEARN_MORE,
                text: this._enumTranslatePipe.transform(CallToActionType.LEARN_MORE, 'call_to_action_type'),
                actionLink: getCallToActionDefaultUrl(CallToActionType.LEARN_MORE, restaurant),
                disabledWithTooltip: false,
            },
            {
                type: CallToActionType.SIGN_UP,
                text: this._enumTranslatePipe.transform(CallToActionType.SIGN_UP, 'call_to_action_type'),
                actionLink: getCallToActionDefaultUrl(CallToActionType.SIGN_UP, restaurant),
                disabledWithTooltip: false,
            },
            {
                type: CallToActionType.CALL,
                text: this._enumTranslatePipe.transform(CallToActionType.CALL, 'call_to_action_type'),
                actionLink: getCallToActionDefaultUrl(CallToActionType.CALL, restaurant),
                disabledWithTooltip: !getCallToActionDefaultUrl(CallToActionType.CALL, restaurant),
                tooltipMessage: this._translateService.instant('posts.new_post.add_informations_to_use'),
            },
            { type: CallToActionType.NONE, text: this._enumTranslatePipe.transform(CallToActionType.NONE, 'call_to_action_type') },
        ];

        this.newPostModalContext.initPostForm({
            restaurant,
            restaurantKeywords: this.restaurantKeywords(),
            postTopics: this.postTopics,
            postCaptionTextLimit: this.POST_CAPTION_TEXT_LIMIT,
        });

        this.newPostModalContext.postForm.valueChanges.subscribe((value) => {
            const postErrors = this._duplicatedPostErrors(value.post?.text, value.post?.plannedPublicationDate);
            this.valid.emit(postErrors.length === 0);
            this.errors.emit(postErrors);
        });

        this.onSelectCallToAction(CallToActionType.NONE);
    }

    addKeyword(keyword: string): void {
        const currentText = this.newPostModalContext.postForm.get('post.text')?.value;
        if (currentText && currentText.length + keyword.length > this.POST_CAPTION_TEXT_LIMIT) {
            return;
        }
        const textarea = document.getElementById('postText') as HTMLTextAreaElement | null;
        if (!textarea) {
            return;
        }
        const cursorPosition: CursorPosition = {
            startPosition: textarea.selectionStart,
            endPosition: textarea.selectionEnd,
        };
        const newText = createTextWithEmoji(
            cursorPosition,
            currentText ?? '',
            this._computeTextWithSpace(currentText ?? '', cursorPosition, keyword)
        );
        this.newPostModalContext.postForm.get('post.text')?.setValue(newText);
        setTimeout(() => {
            textarea.selectionStart = cursorPosition.endPosition + keyword.length + 1;
            textarea.focus();
        }, 1);
    }

    onSelectedDateChange(date: Date | null): void {
        if (!date) {
            return;
        }
        this.newPostModalContext.postForm.get('post.plannedPublicationDate')?.patchValue(date);
    }

    onSelectUrl(value: string | null): void {
        this.newPostModalContext.postForm.get('post.callToAction.url')?.patchValue(value);
    }

    onSelectCallToAction(value: CallToActionType): void {
        const callToAction = this.callToActions.find((t) => t.type === value);
        if (callToAction) {
            this.newPostModalContext.postForm.get('post.callToAction.actionType')?.patchValue(callToAction);
            if (callToAction.actionLink) {
                this.newPostModalContext.postForm.get('post.callToAction.url')?.patchValue(callToAction.actionLink);
            }
            if (callToAction.type === CallToActionType.NONE) {
                this.newPostModalContext.postForm.get('post.callToAction.url')?.patchValue(null);
            }
        }
    }

    getCorrectEndDate(): Date {
        const now = new Date();
        const endDate = this.newPostModalContext.postForm.get('post.event.endDate')?.value;
        return endDate && new Date(endDate).getTime() > now.getTime() ? endDate : this.DEFAULT_MAX_EVENT_DATE;
    }

    private _computeTextWithSpace(currentText: string, { startPosition, endPosition }: CursorPosition, text: string): string {
        const hasSpaceBefore = currentText[startPosition - 1] === ' ';
        const hasSpaceAfter = currentText[endPosition] === ' ';
        const shouldAddSpaceBefore = currentText[startPosition - 1] && !hasSpaceBefore;
        const shouldAddSpaceAfter = currentText[endPosition] && !hasSpaceAfter;
        return `${shouldAddSpaceBefore ? ' ' : ''}${text}${shouldAddSpaceAfter ? ' ' : ''}`;
    }

    displayWithPostTopic(topic: { type: SeoPostTopic; text: string }): string {
        return topic.text;
    }
}
