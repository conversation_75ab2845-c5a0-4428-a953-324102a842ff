import { CdkTextareaAutosize } from '@angular/cdk/text-field';
import { NgTemplateOutlet } from '@angular/common';
import { ChangeDetectionStrategy, Component, computed, inject, input, OnInit, signal } from '@angular/core';
import { FormControl, FormGroup, FormsModule, ReactiveFormsModule } from '@angular/forms';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatIconModule } from '@angular/material/icon';
import { MatSelectChange, MatSelectModule } from '@angular/material/select';
import { MatTooltipModule } from '@angular/material/tooltip';
import { TranslateModule, TranslateService } from '@ngx-translate/core';
import { BehaviorSubject } from 'rxjs';

import { ApplicationLanguage, CallToActionType, KeywordScoreTextType, PlatformKey, PostSource } from '@malou-io/package-utils';

import { times } from ':core/constants';
import { CallToActionOption } from ':modules/posts/new-post-modal/new-post-modal.component';
import { PostCallToActionComponent } from ':modules/posts/new-post-modal/post-call-to-action/post-call-to-action.component';
import { AddFbLocationV2Component } from ':shared/components/add-fb-location-v2/add-fb-location-v2.component';
import { DuplicatePostForm } from ':shared/components/duplicate-post-preview-modal/duplicate-social-post-with-text-generation-preview-modal-v2/duplicate-social-post-with-text-generation-preview-modal-v2.component';
import { InputDatePickerComponent } from ':shared/components/input-date-picker/input-date-picker.component';
import { InputTextComponent } from ':shared/components/input-text/input-text.component';
import { InputTextTheme } from ':shared/components/input-text/input-text.interface';
import { KeywordsScoreGaugeComponent } from ':shared/components/keywords-score-gauge/keywords-score-gauge.component';
import { KeywordsScoreGaugeType } from ':shared/components/keywords-score-gauge/keywords-score-gauge.interface';
import { PlatformLogoComponent } from ':shared/components/platform-logo/platform-logo.component';
import { isPastHour } from ':shared/helpers';
import { getCallToActionDefaultUrl } from ':shared/helpers/call-to-action-url';
import { FbLocation, Keyword, Restaurant } from ':shared/models';
import { SvgIcon } from ':shared/modules/svg-icon.enum';
import { ApplyPurePipe, ApplySelfPurePipe } from ':shared/pipes/apply-fn.pipe';
import { EnumTranslatePipe } from ':shared/pipes/enum-translate.pipe';
import { FormatTimePipe } from ':shared/pipes/format-time.pipe';
import { ImagePathResolverPipe } from ':shared/pipes/image-path-resolver.pipe';

@Component({
    selector: 'app-social-post-caption-preview-card-v2',
    imports: [
        NgTemplateOutlet,
        MatIconModule,
        MatSelectModule,
        MatTooltipModule,
        MatFormFieldModule,
        FormsModule,
        ReactiveFormsModule,
        TranslateModule,
        InputDatePickerComponent,
        InputTextComponent,
        ApplySelfPurePipe,
        ImagePathResolverPipe,
        ApplyPurePipe,
        FormatTimePipe,
        AddFbLocationV2Component,
        CdkTextareaAutosize,
        PlatformLogoComponent,
        PostCallToActionComponent,
        KeywordsScoreGaugeComponent,
    ],
    changeDetection: ChangeDetectionStrategy.OnPush,
    templateUrl: './social-post-caption-preview-card-v2.component.html',
    styleUrl: './social-post-caption-preview-card-v2.component.scss',
})
export class SocialPostCaptionPreviewCardV2Component implements OnInit {
    readonly shouldDisplayDateForm = input.required<boolean>();
    readonly postForm = input.required<FormGroup<DuplicatePostForm>>();
    readonly shouldKeepSameCaptionForAllPosts = input.required<boolean>();
    readonly postDestination = input.required<PostSource>();
    readonly postLang = input.required<ApplicationLanguage>();
    readonly keywords = input.required<Keyword[]>();

    private readonly _translateService = inject(TranslateService);
    private readonly _enumTranslatePipe = inject(EnumTranslatePipe);

    readonly currentLang = signal<ApplicationLanguage>(inject(TranslateService).currentLang as ApplicationLanguage);

    readonly MAX_LENGTH_FOR_SOCIAL_POST = 1500;
    readonly TIMES = times;
    readonly MIN_DATE = new Date();
    readonly SvgIcon = SvgIcon;
    readonly InputTextTheme = InputTextTheme;
    readonly PostSource = PostSource;
    readonly KeywordsScoreGaugeType = KeywordsScoreGaugeType;

    callToActions: CallToActionOption[];
    readonly textType = KeywordScoreTextType.POST;

    readonly isLoadingLocation$: BehaviorSubject<{ loading: boolean; location: FbLocation | null }> = new BehaviorSubject({
        loading: false,
        location: null,
    });

    readonly allHashtagTexts = computed(() => this.postForm().get('hashtagTexts')!.value);

    isPastHour = isPastHour;

    get restaurant(): Restaurant {
        return this.postForm().get('restaurant')!.value;
    }

    get platformSocialId(): string | null {
        return this.postForm().get('platformSocialId')!.value;
    }

    get location(): FormControl<FbLocation | null> {
        return this.postForm().controls.location;
    }

    get keys(): PlatformKey[] {
        return this.postForm().get('keys')!.value;
    }

    get fbPlatformName(): string | null {
        return this.postForm().get('fbPlatformName')!.value;
    }

    get fbPlatformId(): string | null {
        return this.postForm().get('fbPlatformId')!.value;
    }

    get fbPlatformCity(): string | null {
        return this.postForm().get('fbPlatformCity')!.value;
    }

    get hashtagTexts(): string[] {
        return this.postForm().get('hashtagTexts')!.value;
    }

    get hasPlatformsConnected(): boolean {
        return this.postForm().get('hasPlatformsConnected')!.value;
    }

    get postCallToActionType(): CallToActionType | null {
        return this.postForm().get('postCallToActionType')!.value;
    }

    get postCallToActionUrl(): string | null {
        return this.postForm().get('postCallToActionUrl')!.value;
    }

    get postCaption(): string | null {
        return this.postForm().get('postCaption')!.value;
    }

    ngOnInit(): void {
        this.callToActions = [
            {
                type: CallToActionType.BOOK,
                text: this._enumTranslatePipe.transform(CallToActionType.BOOK, 'call_to_action_type'),
                actionLink: getCallToActionDefaultUrl(CallToActionType.BOOK, this.restaurant),
                disabledWithTooltip: !getCallToActionDefaultUrl(CallToActionType.BOOK, this.restaurant),
                tooltipMessage: this._translateService.instant('posts.new_post.add_informations_to_use'),
            },
            {
                type: CallToActionType.ORDER,
                text: this._enumTranslatePipe.transform(CallToActionType.ORDER, 'call_to_action_type'),
                actionLink: getCallToActionDefaultUrl(CallToActionType.ORDER, this.restaurant),
                disabledWithTooltip: !getCallToActionDefaultUrl(CallToActionType.ORDER, this.restaurant),
                tooltipMessage: this._translateService.instant('posts.new_post.add_informations_to_use'),
            },
            {
                type: CallToActionType.LEARN_MORE,
                text: this._enumTranslatePipe.transform(CallToActionType.LEARN_MORE, 'call_to_action_type'),
                actionLink: getCallToActionDefaultUrl(CallToActionType.LEARN_MORE, this.restaurant),
                disabledWithTooltip: false,
            },
            {
                type: CallToActionType.SIGN_UP,
                text: this._enumTranslatePipe.transform(CallToActionType.SIGN_UP, 'call_to_action_type'),
                actionLink: getCallToActionDefaultUrl(CallToActionType.SIGN_UP, this.restaurant),
                disabledWithTooltip: false,
            },
            {
                type: CallToActionType.CALL,
                text: this._enumTranslatePipe.transform(CallToActionType.CALL, 'call_to_action_type'),
                actionLink: getCallToActionDefaultUrl(CallToActionType.CALL, this.restaurant),
                disabledWithTooltip: !getCallToActionDefaultUrl(CallToActionType.CALL, this.restaurant),
                tooltipMessage: this._translateService.instant('posts.new_post.add_informations_to_use'),
            },
            { type: CallToActionType.NONE, text: this._enumTranslatePipe.transform(CallToActionType.NONE, 'call_to_action_type') },
        ];
    }

    onSelectUrl(value: string | null): void {
        this.postForm().get('postCallToActionUrl')?.patchValue(value);
    }

    onSelectCallToAction(value: CallToActionType): void {
        const callToAction = this.callToActions.find((t) => t.type === value);
        if (callToAction) {
            this.postForm().get('postCallToActionType')?.patchValue(callToAction?.type);
            if (callToAction.actionLink) {
                this.postForm().get('postCallToActionUrl')?.patchValue(callToAction.actionLink);
            }
            if (callToAction.type === CallToActionType.NONE) {
                this.postForm().get('postCallToActionUrl')?.patchValue(null);
            }
        }
    }

    changeSelectedTime(event: MatSelectChange): void {
        this.postForm().patchValue({
            postTime: event.value,
        });
    }

    onLocationChange(location: FbLocation | null): void {
        this.postForm().patchValue({
            location,
        });
    }

    removeHashtag(hashtagIndex: number): void {
        this.postForm().patchValue({
            hashtagTexts: this.hashtagTexts.filter((_, index) => index !== hashtagIndex),
        });
    }
}
