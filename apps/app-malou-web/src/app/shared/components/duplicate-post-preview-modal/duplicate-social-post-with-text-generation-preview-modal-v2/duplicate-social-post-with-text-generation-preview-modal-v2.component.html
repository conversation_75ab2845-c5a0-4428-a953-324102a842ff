<div class="flex h-full w-full flex-col border-y !border-malou-color-border-primary">
    <div class="flex h-full bg-malou-color-background-light">
        <div class="hide-scrollbar mx-8 mt-6 flex flex-[0_0_30%] flex-col gap-1 overflow-y-auto">
            <ng-container
                [ngTemplateOutlet]="isSingleSeoDuplication() ? seoDestinationOriginalPost : socialDestinationOriginalPost"></ng-container>
        </div>
        <mat-divider class="!border-malou-color-border-primary" [vertical]="true"></mat-divider>
        <div class="flex w-full flex-1 flex-col overflow-y-auto bg-white">
            @if (isLoading() || !isLoaderMinDurationReached()) {
                <ng-container [ngTemplateOutlet]="loadingTemplate"></ng-container>
            } @else if (isCaptionsGenerationError()) {
                <ng-container [ngTemplateOutlet]="errorCaptionsGeneration"></ng-container>
            } @else {
                <ng-container [ngTemplateOutlet]="isSingleSeoDuplication() ? seoPostDetails : socialPostCaptionsList"></ng-container>
            }
        </div>
    </div>
</div>

<ng-template #socialDestinationOriginalPost>
    <app-schedule-post-form [(willPostAllAtSameTime)]="willPostAllAtSameTime" [scheduleForm]="customizedDatePostForm">
    </app-schedule-post-form>
    @if (sharedData.postDestination !== PostSource.SEO) {
        <app-keep-same-post-caption-toggle
            [(shouldKeepSameCaptionForAllPosts)]="shouldKeepSameCaptionForAllPosts"
            [disabled]="isLoading() || !isLoaderMinDurationReached()"></app-keep-same-post-caption-toggle>
    }
    <app-original-post-preview-v2
        class="mb-6"
        [post]="postToDuplicate"
        [postIndex]="inputData.index"
        [totalPostCount]="sharedData.postsToDuplicate.length"></app-original-post-preview-v2>
</ng-template>

<ng-template #seoDestinationOriginalPost>
    <app-social-to-seo-original-duplicated-post
        class="h-full"
        [originalPost]="postToDuplicate"
        [isFromUpsertPostModal]="sharedData.isDirectlyAfterUpsertPostModal ?? false">
    </app-social-to-seo-original-duplicated-post>
</ng-template>

<ng-template #loadingTemplate>
    <div class="m-4 flex h-full items-center justify-center overflow-x-hidden">
        <app-multiple-steps-loader [steps]="loaderSteps()" [illustration]="Illustration.Chef"></app-multiple-steps-loader>
    </div>
</ng-template>

<ng-template #errorCaptionsGeneration>
    <div class="flex h-[442px] flex-col items-center justify-center py-6">
        <img class="mb-6 h-20 w-20" alt="Taster illustration" [src]="'Taster' | illustrationPathResolver" />
        <span class="malou-text-14--bold mb-2">{{ 'common.unknown_error' | translate }}</span>
    </div>
</ng-template>

<ng-template #seoPostDetails>
    <app-seo-post-details-preview
        [post]="postToDuplicateWithOptimizedCaption"
        [restaurantKeywords]="restaurantKeywords()"
        [restaurant]="inputData.selectedRestaurants[0]"
        (valid)="valid.emit($event)"
        (errors)="errors.emit($event)"></app-seo-post-details-preview>
</ng-template>

<ng-template #socialPostCaptionsList>
    @for (postForm of previewCaptionPostForms.controls; track postForm; let isLast = $last; let index = $index) {
        <app-social-post-caption-preview-card-v2
            [shouldDisplayDateForm]="!willPostAllAtSameTime()"
            [postForm]="postForm"
            [shouldKeepSameCaptionForAllPosts]="shouldKeepSameCaptionForAllPosts()"
            [postDestination]="sharedData.postDestination"
            [postLang]="postLang()"
            [keywords]="postForm.get('keywords')?.value ?? []"></app-social-post-caption-preview-card-v2>
        @if (!isLast) {
            <mat-divider class="!mx-5 !border-malou-color-border-primary"></mat-divider>
        }
    }
</ng-template>
