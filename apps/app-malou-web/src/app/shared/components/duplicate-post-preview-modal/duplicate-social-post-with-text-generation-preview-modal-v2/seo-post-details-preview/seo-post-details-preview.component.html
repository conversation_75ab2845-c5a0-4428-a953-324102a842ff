<div class="flex gap-10 p-8">
    <ng-container [ngTemplateOutlet]="restaurantInformationsTemplate"></ng-container>

    @if (newPostModalContext.postForm) {
        <ng-container [ngTemplateOutlet]="postFormTemplate"></ng-container>
    }
</div>

<ng-template #restaurantInformationsTemplate>
    <div class="flex flex-col">
        <div class="flex items-start gap-x-2">
            <div class="shrink-0 self-start">
                <div class="relative">
                    <img
                        class="malou-avatar--small !rounded-md"
                        [src]="(restaurant().logo | applySelfPure: 'getMediaUrl' : 'small') || ('default_logo' | imagePathResolver)" />
                </div>
            </div>
            <div class="min-w-0 grow">
                <div class="flex flex-col">
                    <div
                        class="malou-text-13--semibold malou-color-text-1 truncate"
                        [matTooltip]="restaurant().internalName || restaurant().name"
                        [matTooltipShowDelay]="700">
                        {{ restaurant().internalName || restaurant().name }}
                    </div>
                    <div
                        class="malou-text-10--regular malou-color-text-2 truncate italic"
                        [matTooltip]="(newPostModalContext.currentRestaurant() | applySelfPure: 'getFullFormattedAddress') ?? ''"
                        [matTooltipShowDelay]="700">
                        {{ newPostModalContext.currentRestaurant() | applySelfPure: 'getFullFormattedAddress' }}
                    </div>
                </div>
            </div>
        </div>
        <ng-container [ngTemplateOutlet]="postDateTemplate"></ng-container>
    </div>
</ng-template>

<ng-template #postFormTemplate>
    <form class="w-full" [formGroup]="newPostModalContext.postForm">
        <form class="w-full" formGroupName="post">
            <section>
                <app-simple-select
                    formControlName="postTopic"
                    [testId]="'seo-post-type-input'"
                    [title]="'posts.new_post.post_type' | translate"
                    [values]="postTopics"
                    [displayWith]="displayWithPostTopic"></app-simple-select>
            </section>
            @if (postTopic?.type !== SeoPostTopic.STANDARD) {
                <section class="mt-8">
                    <form formGroupName="event">
                        <app-input-text
                            class="mb-12"
                            formControlName="title"
                            [title]="'posts.new_post.add_event_title' | translate"
                            [showMaxLength]="true"
                            [maxLength]="EVENT_TITLE_TEXT_LIMIT">
                        </app-input-text>
                        <p class="malou-color-text-2 malou-text-10--regular mt-4">
                            {{ 'posts.new_post.event_dates' | translate }}
                        </p>
                        <div class="flex justify-between gap-x-2">
                            <app-input-date-picker
                                class="grow-2"
                                formControlName="startDate"
                                [manualInputAvailable]="false"
                                [min]="MIN_DATE"
                                [max]="getCorrectEndDate()"></app-input-date-picker>
                            <app-input-date-picker
                                class="grow-2"
                                formControlName="endDate"
                                [manualInputAvailable]="false"
                                [min]="newPostModalContext.postForm.get('post.event.startDate')?.value ?? DEFAULT_MIN_EVENT_DATE"
                                [max]="DEFAULT_MAX_EVENT_DATE"></app-input-date-picker>
                        </div>
                    </form>
                </section>
            }
            @if (postTopic?.type === SeoPostTopic.OFFER) {
                <section class="mt-8">
                    <p class="malou-color-text-2 malou-text-10--regular mb-2">
                        {{ 'posts.new_post.add_offer_infos' | translate }}
                        <small class="post-new-optional">{{ 'posts.new_post.optional' | translate }}</small>
                    </p>
                    <form class="sm:flex sm:flex-col sm:gap-y-1" formGroupName="offer">
                        <app-input-text formControlName="couponCode" [placeholder]="'posts.new_post.code_placeholder' | translate">
                        </app-input-text>
                        <app-input-text formControlName="onlineUrl" [placeholder]="'posts.new_post.link_placeholder' | translate">
                        </app-input-text>
                        <app-input-text formControlName="termsConditions" [placeholder]="'posts.new_post.terms_placeholder' | translate">
                        </app-input-text>
                    </form>
                </section>
            }
            @let rest = newPostModalContext.currentRestaurant();
            @if (rest && postTopic && [SeoPostTopic.STANDARD, SeoPostTopic.EVENT].includes(postTopic!.type)) {
                <section class="mt-8">
                    <app-post-call-to-action
                        [restaurant]="rest"
                        [currentCallToActionType]="newPostModalContext.postForm.get('post.callToAction.actionType')?.value?.type ?? null"
                        [currentCallToActionUrl]="newPostModalContext.postForm.get('post.callToAction.url')?.value ?? null"
                        [callToActions]="callToActions"
                        [canNavigate]="false"
                        (onSelectUrl)="onSelectUrl($event)"
                        (onSelectCallToAction)="onSelectCallToAction($event)"></app-post-call-to-action>
                </section>
            }
            <section class="mt-8">
                <div class="flex flex-col">
                    <div
                        class="malou-text-13--semibold malou-bg-purple-accent-light flex w-full items-center justify-start rounded-t-md p-2 pl-4 text-malou-color-chart-purple--accent">
                        <mat-icon class="mb-1 mr-1 h-4 !w-4" [svgIcon]="SvgIcon.MAGIC_WAND"></mat-icon>
                        <span>{{ 'restaurant_ai_settings.modals.upsert.proposal' | translate }}</span>
                    </div>
                    <div class="malou-bg-purple-accent-light rounded-b-md p-4 !pt-0">
                        <app-post-caption
                            [postDescriptionFormControl]="postDescriptionControl"
                            [textAreaId]="'postText'"
                            [shouldHideLegend]="true"
                            [shouldHideAiGenerateButtons]="true"></app-post-caption>
                    </div>
                </div>
                <div class="mt-4"></div>
                <app-keywords-score-gauge
                    [text]="postDescriptionControl.value"
                    [restaurant]="restaurant()"
                    [textType]="gaugeTextType()"
                    [keywords]="restaurantKeywords()"
                    [lang]="newPostModalContext.postLang() ?? ''"
                    [parentElementId]="newPostModalContext.currentPost()?._id ?? null"
                    [title]="'posts.new_post.keyword_gauge_title' | translate"
                    [shouldCacheScore]="false"
                    [type]="KeywordsScoreGaugeType.DETAILED_DARK"
                    (addKeyword)="addKeyword($event)"
                    (langChanged)="newPostModalContext.onChangePostLang($event)"
                    #keywordsScoreGauge>
                </app-keywords-score-gauge>
            </section>
        </form>
    </form>
</ng-template>

<ng-template #postDateTemplate>
    @if (newPostModalContext.postForm.get('post.plannedPublicationDate')?.value; as postDate) {
        <app-post-date-picker
            class="mt-4 w-fit"
            [heapTrackBtnId]="'tracking_duplicate_social_posts_to_seo_init_post_date_picker'"
            [selectedDate]="postDate"
            [withBorder]="true"
            (selectedDateChange)="onSelectedDateChange($event)" />
    }
</ng-template>
