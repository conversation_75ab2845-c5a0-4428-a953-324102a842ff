<div class="m-5 flex">
    <div class="mr-6 flex min-w-[200px] max-w-[200px] flex-col">
        <ng-container
            [ngTemplateOutlet]="restaurantInformationsTemplate"
            [ngTemplateOutletContext]="{ restaurant: restaurantsData().restaurant }"></ng-container>
        <div class="ml-[31px] mt-4">
            <app-keywords-score-gauge
                [text]="restaurantsData().postCaption ?? ''"
                [restaurant]="restaurantsData().restaurant"
                [textType]="textType"
                [keywords]="restaurantsData().keywords ?? []"
                [lang]="postLang()"
                [type]="KeywordsScoreGaugeType.SIMPLE_WITH_KEYWORDS_TOOLTIP"></app-keywords-score-gauge>
        </div>
        @if (shouldDisplayDateForm()) {
            <ng-container [ngTemplateOutlet]="postPublicationStatusFormTemplate"></ng-container>
        }
    </div>
    <div class="w-full">
        <div class="flex flex-col">
            <div
                class="malou-text-13--semibold malou-bg-purple-accent-light flex w-full items-center justify-start rounded-t-md p-2 pl-4 text-malou-color-chart-purple--accent"
                [class.hidden]="shouldKeepSameCaptionForAllPosts()">
                <mat-icon class="mb-1 mr-1 h-4 !w-4" [svgIcon]="SvgIcon.MAGIC_WAND"></mat-icon>
                <span>{{ 'restaurant_ai_settings.modals.upsert.proposal' | translate }}</span>
            </div>
            <div
                class="rounded-b-md !pt-0"
                [class.malou-bg-purple-accent-light]="!shouldKeepSameCaptionForAllPosts()"
                [class.p-4]="!shouldKeepSameCaptionForAllPosts()">
                <div
                    class="malou-text-12--medium focus:!border-malou-color-border-secondary] rounded-md border bg-white p-3 leading-5 text-malou-color-text-2 focus:!border-[1px] focus:outline-none"
                    contenteditable="true"
                    [class.border-malou-color-border-primary]="shouldKeepSameCaptionForAllPosts()"
                    [class.border-transparent]="!shouldKeepSameCaptionForAllPosts()"
                    [innerHTML]="initialCaption()"
                    (focus)="onFocusEditableContent(contentEditableDiv, hiddenTextarea)"
                    #contentEditableDiv></div>

                <textarea
                    class="malou-text-12--medium hidden h-auto w-full resize-none rounded-md border bg-white p-3 leading-5 text-malou-color-text-2 focus:!border-[1px] focus:!border-malou-color-border-secondary focus:outline-none"
                    [(ngModel)]="restaurantsData().postCaption"
                    [id]="'custom-textarea-' + restaurantIndex()"
                    [maxlength]="MAX_LENGTH_FOR_SEO_POST"
                    (blur)="onBlurTextarea(restaurantIndex(), contentEditableDiv, hiddenTextarea)"
                    #hiddenTextarea>
                </textarea>
                @if (hasCallToActionUrl() || missingCallToActionUrlError()) {
                    <div class="mt-4">
                        <app-input-text
                            class="w-full"
                            inputType="url"
                            [placeholder]="'posts.new_post.url_placeholder' | translate"
                            [defaultValue]="callToActionUrl() ?? undefined"
                            [readOnly]="shouldDisableCallToActionUrlInput()"
                            (inputTextChange)="onCallToActionUrlChange($event, restaurantIndex())"></app-input-text>
                        @if (missingCallToActionUrlError()) {
                            <mat-error class="malou-color-state-error malou-text-10 mt-2 italic">
                                {{ missingCallToActionUrlError() }}
                            </mat-error>
                        }
                    </div>
                }
            </div>
        </div>
    </div>
</div>

<ng-template #postPublicationStatusFormTemplate>
    <div class="ml-[33px] flex items-center gap-2" [formGroup]="postDateForm()">
        <!-- TODO: use new datepicker component with modal from RS v2 when created -->
        <app-input-date-picker
            class="mt-1 flex h-[30px] w-[130px] cursor-pointer items-baseline gap-x-2 text-malou-color-text-1"
            formControlName="plannedPublicationDate"
            [manualInputAvailable]="false"
            [ownInputValidation]="true"
            [min]="MIN_DATE"
            [shouldDisplayBorder]="false"
            [theme]="InputTextTheme.INLINE_CARD"></app-input-date-picker>
        <div class="mt-[2px] flex h-7 w-[75px] flex-col">
            <mat-select
                class="!m-0 mt-8 !h-0 !w-[95px] bg-malou-color-background-light !opacity-0"
                panelClass="malou-select-panel"
                formControlName="postTime"
                [hideSingleSelectionIndicator]="true"
                (selectionChange)="changeSelectedTime($event)">
                @for (time of TIMES; track time) {
                    <mat-option
                        [value]="time"
                        [disabled]="
                            isPastHour
                                | applyPure
                                    : {
                                          hourWithMinute: time,
                                          date: postDateForm().get('plannedPublicationDate')?.value,
                                      }
                        ">
                        {{ time | formatTime: currentLang() === 'en' }}
                    </mat-option>
                }
            </mat-select>
            <app-input-text
                class="grow"
                formControlName="postTime"
                inputType="time"
                [svgIcon]="SvgIcon.CLOCK"
                [theme]="InputTextTheme.INLINE_CARD"></app-input-text>
        </div>
    </div>
    @if (postDateForm().errors?.publicationDateInTheFuture) {
        <span class="malou-text-10 malou-color-state-error py-1 italic"> {{ 'common.date_past' | translate }} </span>
    }
</ng-template>

<ng-template let-restaurant="restaurant" #restaurantInformationsTemplate>
    <div class="flex items-center gap-x-2">
        <div class="shrink-0">
            <div class="relative">
                <img
                    class="malou-avatar--small !rounded-md"
                    [src]="(restaurant.logo | applySelfPure: 'getMediaUrl' : 'small') || ('default_logo' | imagePathResolver)" />
            </div>
        </div>
        <div class="min-w-0 grow">
            <div class="flex flex-col">
                <div
                    class="malou-text-13--semibold malou-color-text-1 truncate"
                    [matTooltip]="restaurant.internalName || restaurant.name"
                    [matTooltipShowDelay]="700">
                    {{ restaurant.internalName || restaurant.name }}
                </div>
                <div
                    class="malou-text-10--regular malou-color-text-2 truncate italic"
                    [matTooltip]="(restaurant | applySelfPure: 'getFullFormattedAddress') ?? ''"
                    [matTooltipShowDelay]="700">
                    {{ restaurant | applySelfPure: 'getFullFormattedAddress' }}
                </div>
            </div>
        </div>
    </div>
</ng-template>
