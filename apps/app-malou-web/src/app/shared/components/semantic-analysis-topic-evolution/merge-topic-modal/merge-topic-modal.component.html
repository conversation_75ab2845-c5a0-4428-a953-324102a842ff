<div class="malou-dialog">
    <div class="malou-dialog__header gap-x-5">
        <div class="flex flex-col">
            <span>{{ 'statistics.e_reputation.reviews_analysis.topic_evolution.select_merge_name' | translate }}</span>
        </div>
        <button class="malou-btn-icon" mat-icon-button (click)="close()">
            <mat-icon color="primary" [svgIcon]="SvgIcon.CROSS"></mat-icon>
        </button>
    </div>

    <div class="malou-dialog__body flex grow flex-col gap-x-5 !overflow-hidden !pb-[26px] md:hidden">
        <div class="flex h-[45px] items-center rounded-[5px] bg-malou-color-background-dark p-3">
            <img class="ml-1 mr-3 !h-4 !w-4" [src]="Emoji.WARNING | emojiPathResolver" />
            <span class="malou-text-10--medium text-malou-color-text-1">{{ 'common.irreversible_actions' | translate }}</span>
        </div>
        <mat-radio-group
            class="my-3 ml-6 flex max-h-[250px] flex-col gap-x-5 overflow-y-auto"
            color="primary"
            (change)="onSelectedTopicChange($event)">
            @for (topic of data.selectedTopics; track $index) {
                <mat-radio-button
                    class="malou-small-radio-button h-[30px]"
                    [value]="topic.parentTopicId"
                    [checked]="topic.parentTopicId === selectedTopicId()">
                    <span class="malou-text-10--medium text-malou-color-text-1">{{
                        topic.displayedNameInCurrentLang || capitalize(topic.name)
                    }}</span>
                </mat-radio-button>
            }
        </mat-radio-group>
        <div class="mt-2 flex justify-end">
            <button class="malou-btn-raised--secondary mr-2 !h-11" mat-raised-button (click)="close()">
                {{ 'common.cancel' | translate }}
            </button>
            <button
                class="malou-btn-raised--primary !h-11"
                mat-raised-button
                [disabled]="!selectedTopicId() || isLoading()"
                (click)="mergeTopics()">
                @if (isLoading()) {
                    <app-malou-spinner class="!h-4 !w-4" [size]="'small'"></app-malou-spinner>
                } @else {
                    <span>{{ 'common.merge' | translate }}</span>
                }
            </button>
        </div>
    </div>
</div>
