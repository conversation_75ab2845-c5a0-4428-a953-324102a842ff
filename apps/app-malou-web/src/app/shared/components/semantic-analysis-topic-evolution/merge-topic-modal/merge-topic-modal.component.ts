import { ChangeDetectionStrategy, Component, inject, OnInit, signal } from '@angular/core';
import { MatButtonModule } from '@angular/material/button';
import { MAT_DIALOG_DATA, MatDialogRef } from '@angular/material/dialog';
import { MatIconModule } from '@angular/material/icon';
import { MatRadioChange, MatRadioModule } from '@angular/material/radio';
import { TranslateModule } from '@ngx-translate/core';
import { capitalize } from 'lodash';

import { HeapEventName } from '@malou-io/package-utils';

import { MalouSpinnerComponent } from ':core/components/spinner/spinner/malou-spinner.component';
import { HeapService } from ':core/services/heap.service';
import { SegmentAnalysisParentTopicsService } from ':core/services/segment-analysis-parent-topics.service';
import { SegmentAnalysisParentTopicInsightsWithTranslation } from ':shared/components/semantic-analysis-topic-evolution/semantic-analysis-topic-evolution.component';
import { SvgIcon } from ':shared/modules/svg-icon.enum';
import { Emoji, EmojiPathResolverPipe } from ':shared/pipes/emojis-path-resolver.pipe';

@Component({
    selector: 'app-merge-topic-modal',
    imports: [MatIconModule, MatRadioModule, MatButtonModule, TranslateModule, MalouSpinnerComponent, EmojiPathResolverPipe],
    templateUrl: './merge-topic-modal.component.html',
    styleUrl: './merge-topic-modal.component.scss',
    changeDetection: ChangeDetectionStrategy.OnPush,
})
export class MergeTopicModalComponent implements OnInit {
    private readonly _dialogRef = inject(MatDialogRef<MergeTopicModalComponent>);
    public readonly data: { selectedTopics: SegmentAnalysisParentTopicInsightsWithTranslation[]; restaurantId: string } =
        inject(MAT_DIALOG_DATA);

    private readonly _segmentAnalysisParentTopicsService = inject(SegmentAnalysisParentTopicsService);
    private readonly _heapService = inject(HeapService);

    readonly SvgIcon = SvgIcon;
    readonly Emoji = Emoji;
    readonly capitalize = capitalize;

    readonly selectedTopicId = signal<string | null>(null);
    readonly isLoading = signal(false);

    ngOnInit(): void {
        this.selectedTopicId.set(this.data.selectedTopics[0]?.parentTopicId);
    }

    onSelectedTopicChange(event: MatRadioChange): void {
        this.selectedTopicId.set(event.value);
    }

    mergeTopics(): void {
        const selectedTopicId = this.selectedTopicId();
        if (!selectedTopicId) {
            return;
        }
        this.isLoading.set(true);
        const topicIdsToMerge = this.data.selectedTopics
            .map((topic) => topic.parentTopicId)
            .filter((topicId) => topicId !== selectedTopicId);
        this._segmentAnalysisParentTopicsService
            .merge({
                topicIdsToMerge,
                topicIdToKeep: selectedTopicId,
                restaurantId: this.data.restaurantId,
            })
            .subscribe({
                next: () => {
                    this._heapService.track(HeapEventName.TRACKING_SEMANTIC_ANALYSIS_MERGE_CONFIRM);
                    this.isLoading.set(false);
                    this._dialogRef.close({ topicIdsToHide: topicIdsToMerge, keptTopicId: selectedTopicId });
                },
                error: () => {
                    this.isLoading.set(false);
                },
            });
    }

    close(data?: { topicIdsToHide: string[]; keptTopicId: string }): void {
        this._dialogRef.close(data);
    }
}
