@if (hasData() && isPdfDownload()) {
    <div class="flex flex-col gap-2">
        @for (tab of builtTabsForPdf(); track $index) {
            <ng-container [ngTemplateOutlet]="graphTemplate" [ngTemplateOutletContext]="{ pdfSelectedTabData: tab }"></ng-container>
        }
    </div>
} @else {
    <ng-container [ngTemplateOutlet]="graphTemplate"></ng-container>
}

<ng-template let-pdfSelectedTabData="pdfSelectedTabData" #graphTemplate>
    <div class="malou-simple-card flex h-[600px] w-full break-inside-avoid flex-col gap-3 px-6 py-3 md:px-2">
        <div class="flex h-full min-h-60 flex-col gap-3">
            <div>
                <ng-container [ngTemplateOutlet]="filterTemplate"></ng-container>
            </div>
            @if (hasData()) {
                <div>
                    <ng-container [ngTemplateOutlet]="navigationTemplate" [ngTemplateOutletContext]="{ pdfSelectedTabData }"></ng-container>
                </div>
                <div class="h-full">
                    <ng-container
                        [ngTemplateOutlet]="topicEvolutionTemplate"
                        [ngTemplateOutletContext]="{ pdfSelectedTabData }"></ng-container>
                </div>
            } @else {
                <div class="flex flex-col items-center py-4">
                    <img class="mb-6 h-20 w-20" alt="Taster illustration" [src]="Illustration.Taster | illustrationPathResolver" />
                    <span class="malou-text-14--bold mb-2">{{ 'common.no_data' | translate }}</span>
                </div>
            }
        </div>
    </div>
</ng-template>

<ng-template #filterTemplate>
    <div class="flex flex-wrap items-center justify-between gap-2">
        <span class="malou-text-section-title malou-color-text-1">
            {{ 'statistics.e_reputation.reviews_analysis.topic_evolution.title' | translate }}
        </span>
        <div class="flex items-center gap-2">
            @if (isPdfDownload()) {
                <span class="malou-text-14--regular italic">
                    {{ 'common.view_by' | translate }}
                    {{ viewByDisplayWith | applyPure: chartViewBy() | lowercase }}
                </span>
            } @else {
                <app-select
                    [values]="VIEW_BY_FILTER_VALUES"
                    [theme]="SelectBaseDisplayStyle.WITH_BACKGROUND"
                    [inputReadOnly]="true"
                    [displayWith]="viewByDisplayWith"
                    [formControl]="viewByControl"
                    [idPrefix]="'tracking_semantic_analysis_view_by_'"
                    [getIdSuffixFn]="getIdSuffixFn"></app-select>
            }
        </div>
    </div>
</ng-template>

<ng-template let-pdfSelectedTabData="pdfSelectedTabData" #navigationTemplate>
    <app-tab-nav-bar
        [tabs]="tabs()"
        [defaultSelectedTab]="pdfSelectedTabData ?? defaultSelectedTab()"
        [theme]="TabNavBarDisplayStyle.LIGHT"
        (tabChange)="onTabChange($event)"></app-tab-nav-bar>
</ng-template>

<ng-template let-pdfSelectedTabData="pdfSelectedTabData" #topicEvolutionTemplate>
    <div class="flex h-full">
        <div class="w-[33%]">
            <ng-container [ngTemplateOutlet]="topicsTemplate" [ngTemplateOutletContext]="{ pdfSelectedTabData }"></ng-container>
        </div>
        <div class="w-[67%] pl-5">
            @if (isLoading()) {
                <app-skeleton skeletonClass="!h-[440px] secondary-bg"></app-skeleton>
            } @else {
                <ng-container [ngTemplateOutlet]="chartTemplate" [ngTemplateOutletContext]="{ pdfSelectedTabData }"></ng-container>
            }
        </div>
    </div>
</ng-template>

<ng-template let-pdfSelectedTabData="pdfSelectedTabData" #topicsTemplate>
    <div class="flex flex-col">
        <ng-container
            [ngTemplateOutlet]="topicTitleTemplate"
            [ngTemplateOutletContext]="{
                pdfSelectedTabData,
            }"></ng-container>
        @if (!isPdfDownload()) {
            <ng-container [ngTemplateOutlet]="topicFiltersTemplate"></ng-container>
        }
    </div>

    <div class="flex flex-col gap-1" [ngClass]="{ 'mt-2': isPdfDownload() }">
        @let filteredTopics = pdfSelectedTabData?.sortedTopics ?? filteredAndSortedCurrentCategoryTopics();
        @let mainTopic = pdfSelectedTabData?.currentMainTopic ?? currentMainTopic();
        @if (filteredTopics.length || (mainTopic && isCurrentMainTopicMatchingSearch()) || searchText()) {
            <div class="flex max-h-[350px] flex-col overflow-y-auto rounded-[5px]">
                @if (mainTopic && isCurrentMainTopicMatchingSearch()) {
                    <ng-container
                        [ngTemplateOutlet]="singleTopicTemplate"
                        [ngTemplateOutletContext]="{
                            isMainTopic: true,
                            topic: mainTopic,
                            isFirstElement: true,
                            isLastElement: filteredTopics.length === 0 && !shouldDisplayAddTopicOption(),
                        }"></ng-container>
                    @if (filteredTopics.length !== 0 || shouldDisplayAddTopicOption()) {
                        <div class="min-h-[1px] w-full bg-malou-color-border-primary"></div>
                    }
                }
                @for (topic of filteredTopics; track topic.name; let isFirstElement = $first; let isLastElement = $last) {
                    <ng-container
                        [ngTemplateOutlet]="singleTopicTemplate"
                        [ngTemplateOutletContext]="{
                            isMainTopic: false,
                            topic,
                            isFirstElement: isFirstElement && !isCurrentMainTopicMatchingSearch(),
                            isLastElement: isLastElement && !shouldDisplayAddTopicOption(),
                        }"></ng-container>
                    @if (!isLastElement || shouldDisplayAddTopicOption()) {
                        <div class="min-h-[1px] w-full bg-malou-color-border-primary"></div>
                    }
                }
                @if (shouldDisplayAddTopicOption()) {
                    <ng-container
                        [ngTemplateOutlet]="addTopicOptionTemplate"
                        [ngTemplateOutletContext]="{
                            topicText: searchText(),
                        }"></ng-container>
                }
            </div>
        }
        @if (!searchText() && !filteredTopics.length) {
            @let category = pdfSelectedTabData?.category ?? currentCategory();
            @let categoryText = category ? (category | enumTranslate: 'review_analysis_tags')?.toLowerCase() : '';
            @let sentiment = pdfSelectedTabData?.sentiment ?? currentSentiment();
            <ng-container
                [ngTemplateOutlet]="emptyTopicsTemplate"
                [ngTemplateOutletContext]="{
                    illustration: sentiment === ReviewAnalysisSentiment.POSITIVE ? Illustration.Stars : Illustration.BestFoodInTown,
                    title:
                        sentiment === ReviewAnalysisSentiment.POSITIVE
                            ? ('statistics.e_reputation.reviews_analysis.topic_evolution.no_positive_topic.title' | translate)
                            : ('statistics.e_reputation.reviews_analysis.topic_evolution.no_negative_topic.title' | translate),
                    subtitle:
                        sentiment === ReviewAnalysisSentiment.POSITIVE
                            ? ('statistics.e_reputation.reviews_analysis.topic_evolution.no_positive_topic.subtitle'
                              | translate: { category: categoryText })
                            : ('statistics.e_reputation.reviews_analysis.topic_evolution.no_negative_topic.subtitle'
                              | translate: { category: categoryText }),
                    actionText:
                        sentiment === ReviewAnalysisSentiment.POSITIVE
                            ? ('statistics.e_reputation.reviews_analysis.topic_evolution.no_positive_topic.see_my_negative_topics'
                              | translate)
                            : ('statistics.e_reputation.reviews_analysis.topic_evolution.no_negative_topic.see_my_positive_topics'
                              | translate),
                    onActionClick: toggleCurrentSentiment,
                }"></ng-container>
        }
    </div>
</ng-template>

<ng-template let-pdfSelectedTabData="pdfSelectedTabData" #topicTitleTemplate>
    <div class="my-1 flex h-[34px] w-full items-center justify-between">
        <div>
            <span class="malou-text-15--bold ml-1 text-malou-color-text-1">
                {{ 'statistics.e_reputation.reviews_analysis.topic_evolution.topics' | translate }}
            </span>
        </div>
        <div class="flex items-center">
            @if (!isDisplayingMergeView()) {
                <ng-container
                    [ngTemplateOutlet]="sentimentSelectorTemplate"
                    [ngTemplateOutletContext]="{ sentiment: ReviewAnalysisSentiment.NEGATIVE, pdfSelectedTabData }"></ng-container>
                <ng-container
                    [ngTemplateOutlet]="sentimentSelectorTemplate"
                    [ngTemplateOutletContext]="{ sentiment: ReviewAnalysisSentiment.POSITIVE, pdfSelectedTabData }"></ng-container>
            }
        </div>
    </div>
</ng-template>

<ng-template #topicFiltersTemplate>
    @if (isDisplayingMergeView()) {
        <div class="my-2 flex h-9 w-full justify-end">
            <button class="malou-btn-flat !malou-text-12--medium mr-2 p-2 !text-malou-color-text-1" (click)="cancelMergeView()">
                {{ 'common.cancel' | translate }}
            </button>
            <button
                class="malou-btn-flat !malou-text-12--medium rounded-[5px] border border-malou-color-border-primary bg-malou-color-background-light p-2 !text-malou-color-primary"
                id="tracking_semantic_analysis_merge_click"
                [disabled]="selectedTopicsForMerge().length < 2"
                (click)="openMergeTopicsModal()">
                {{ 'common.merge' | translate }}
            </button>
        </div>
    } @else {
        <div class="my-2 flex w-full justify-between">
            <div class="mr-1 w-full">
                <app-search
                    [value]="searchText()"
                    [placeholder]="'statistics.e_reputation.reviews_analysis.topic_evolution.search_or_add' | translate"
                    [theme]="SearchBarDisplayStyle.SMALL"
                    (searchChange)="onSearchValueChange($event)"></app-search>
            </div>
            <div
                class="mr-1 flex h-9 !min-w-9 items-center justify-center rounded-[5px] border border-malou-color-border-primary bg-malou-color-background-light p-2"
                [matTooltip]="'common.merge' | translate">
                <mat-icon
                    class="mt-[4px] !h-5 !w-3.5 cursor-pointer text-malou-color-primary"
                    [svgIcon]="SvgIcon.MERGE"
                    (click)="openMergeView()"></mat-icon>
            </div>
            <div class="flex h-9 w-9 items-center justify-center rounded-[5px] bg-malou-color-background-dark p-2">
                <mat-icon
                    class="!h-5 cursor-pointer text-malou-color-primary"
                    [svgIcon]="selectedSortBy() === TopicSortBy.ASC ? SvgIcon.SORT_UP : SvgIcon.SORT_DOWN"
                    (click)="toggleSelectedSortBy()"></mat-icon>
            </div>
        </div>
    }
</ng-template>

<ng-template let-sentiment="sentiment" let-pdfSelectedTabData="pdfSelectedTabData" #sentimentSelectorTemplate>
    @let selectedSentiment = pdfSelectedTabData?.sentiment ?? currentSentiment();
    <div
        class="cursor-pointer rounded-[5px] p-2"
        [id]="'tracking_semantic_analysis_' + (sentiment === ReviewAnalysisSentiment.POSITIVE ? 'positive' : 'negative') + '_filter_click'"
        [ngClass]="{
            'malou-text-12--medium bg-malou-color-background-dark text-malou-color-text-1': selectedSentiment === sentiment,
            'malou-text-12--regular text-malou-color-text-2': selectedSentiment !== sentiment,
        }"
        (click)="updateSentiment(sentiment)">
        {{
            sentiment === ReviewAnalysisSentiment.POSITIVE
                ? ('statistics.e_reputation.reviews_analysis.topic_evolution.positive' | translate)
                : ('statistics.e_reputation.reviews_analysis.topic_evolution.negative' | translate)
        }}
    </div>
</ng-template>

<ng-template
    let-topic="topic"
    let-isMainTopic="isMainTopic"
    let-pdfSelectedTabData="pdfSelectedTabData"
    let-isFirstElement="isFirstElement"
    let-isLastElement="isLastElement"
    #singleTopicTemplate>
    @let sentiment = pdfSelectedTabData?.sentiment ?? currentSentiment();
    @let sentimentCountKey = sentiment === ReviewAnalysisSentiment.POSITIVE ? 'positiveCount' : 'negativeCount';
    @let sentimentCountEvolutionKey = sentiment === ReviewAnalysisSentiment.POSITIVE ? 'positiveCountEvolution' : 'negativeCountEvolution';
    <div class="flex w-full items-center justify-between">
        @if (isDisplayingMergeView()) {
            <div class="mr-2 w-[20px]">
                @if (!isMainTopic) {
                    <mat-checkbox
                        color="primary"
                        [checked]="isTopicSelectedForMerge()(topic)"
                        (change)="toggleSelectedTopicForMerge($event.checked, topic)"
                        (pointerdown)="$event.stopPropagation()"
                        (click)="$event.stopPropagation()">
                    </mat-checkbox>
                }
            </div>
        }
        <div
            class="hover-arrow flex w-full cursor-pointer items-center justify-between border-x border-malou-color-border-primary p-2 hover:bg-malou-color-background-light"
            [ngClass]="{
                'rounded-t-[5px] border-t': isFirstElement,
                'rounded-b-[5px] !border-b': isLastElement,
                '!border-r-2 !border-r-malou-color-primary': selectedTopicName() === topic.name && !isFirstElement && !isLastElement,
                'pretty-border relative': selectedTopicName() === topic.name && (isFirstElement || isLastElement),
                'pretty-border--first': selectedTopicName() === topic.name && isFirstElement,
                'pretty-border--last': selectedTopicName() === topic.name && isLastElement,
                '!border-malou-color-background-dark !bg-malou-color-background-dark': selectedTopicName() === topic.name,
                'min-h-[45px]': isPdfDownload(),
                'h-[50px]': !isPdfDownload(),
            }"
            (click)="updateSelectedTopic(topic, isMainTopic)">
            <div class="flex items-center" [ngClass]="{ 'w-[calc(100%-50px)]': !isMainTopic }">
                @if (!isMainTopic) {
                    <mat-icon
                        class="mr-1 !h-4 cursor-pointer"
                        [ngClass]="{
                            'text-malou-color-state-warn': topic.isFavorite,
                            'text-malou-color-background-dark': !topic.isFavorite && selectedTopicName() !== topic.name,
                            'text-malou-color-text-white': !topic.isFavorite && selectedTopicName() === topic.name,
                        }"
                        [svgIcon]="topic.isFavorite ? SvgIcon.FAVORITE_ACTIVE : SvgIcon.FAVORITE_INACTIVE"
                        (click)="toggleFavoriteStatus(topic)"></mat-icon>
                }
                <div class="flex flex-col items-start" [ngClass]="{ 'ml-2': isMainTopic, 'max-w-[calc(100%-28px)]': !isMainTopic }">
                    <div class="flex w-full items-center">
                        <span
                            class="text-malou-color-text-1"
                            [matTooltip]="
                                textContainer.offsetWidth < textContainer.scrollWidth
                                    ? topic.displayedNameInCurrentLang || capitalize(topic.name)
                                    : null
                            "
                            [ngClass]="{
                                'custom-truncate': !isPdfDownload(),
                                'malou-text-13--bold': isMainTopic,
                                'malou-text-12--semibold': !isMainTopic,
                            }"
                            #textContainer>
                            {{ topic.displayedNameInCurrentLang || capitalize(topic.name) }}
                        </span>
                        @if (topic.isNew) {
                            <span
                                class="malou-text-8--semibold ml-2 rounded bg-malou-color-gradient-2 px-1 pb-0.5 pt-[3px] text-malou-color-text-white">
                                {{ 'statistics.e_reputation.reviews_analysis.topic_evolution.new' | translate }}
                            </span>
                        }
                    </div>
                    @if (!isPdfDownload() && (topic.positiveCount || topic.negativeCount)) {
                        <button
                            class="malou-btn-flat !malou-text-8--semibold !text-malou-color-primary"
                            id="tracking_semantic_analysis_see_related_reviews_click"
                            (click)="openTopicReviewsModal(topic, isMainTopic)">
                            {{ 'statistics.e_reputation.reviews_analysis.topic_evolution.see_associated_reviews' | translate }}
                        </button>
                    }
                </div>
            </div>
            <div class="ml-2 mr-1">
                <span class="number-evolution-container flex gap-3">
                    <span class="malou-text-12--medium text-malou-color-text-1">{{ topic[sentimentCountKey] }}</span>
                    @if (topic[sentimentCountEvolutionKey]) {
                        <app-number-evolution
                            [value]="topic[sentimentCountEvolutionKey]"
                            [displayedValue]="''"
                            [shouldSwitchColors]="sentiment === ReviewAnalysisSentiment.NEGATIVE"></app-number-evolution>
                    } @else {
                        <div class="min-w-[18px]"></div>
                    }
                </span>
                @if (topic.isUserInput) {
                    <span
                        class="hover-arrow-container malou-chip--error-light mr-2 hidden !h-6 !w-6 items-center rounded-full"
                        (click)="deleteTopic(topic)">
                        <mat-icon class="!h-4 cursor-pointer text-malou-color-state-error" [svgIcon]="SvgIcon.TRASH"></mat-icon>
                    </span>
                }
            </div>
        </div>
    </div>
</ng-template>

<ng-template let-pdfSelectedTabData="pdfSelectedTabData" #chartTemplate>
    @if (noResults()) {
        @if (selectedTopic().isUserInput && formattedCreatedAtForSelectedTopic()) {
            <div class="flex h-full w-full items-center justify-center">
                <div class="flex flex-col items-center">
                    <img class="mb-5 h-48" alt="Cloche Illustration" [src]="Illustration.Cloche | illustrationPathResolver" />
                    <div class="malou-text-10--medium text-malou-color-text-2">
                        {{
                            'statistics.e_reputation.reviews_analysis.topic_evolution.no_data_user_input_topic'
                                | translate: { date: formattedCreatedAtForSelectedTopic() }
                        }}
                    </div>
                </div>
            </div>
        } @else {
            <div></div>
        }
    } @else {
        @let positiveData = pdfSelectedTabData?.positiveChartData ?? positiveChartData();
        @let negativeData = pdfSelectedTabData?.negativeChartData ?? negativeChartData();
        @let dateLabels = pdfSelectedTabData?.dateLabels ?? labels();
        <app-area-chart
            [positiveChartData]="positiveData"
            [negativeChartData]="negativeData"
            [labels]="dateLabels"
            [viewBy]="chartViewBy()"
            [chartTopicFilter]="chartTopicFilter()"></app-area-chart>
    }
</ng-template>

<ng-template let-topicText="topicText" #addTopicOptionTemplate>
    <div
        class="flex h-[50px] w-full items-center justify-between border border-t-0 border-malou-color-border-primary bg-malou-color-background-light p-3">
        <div class="flex items-center justify-start">
            <img class="mr-2 !max-h-7" alt="Search Illustration" [src]="Illustration.Search | illustrationPathResolver" />
            <div
                class="malou-text-12--regular text-malou-color-text-1"
                [innerHTML]="
                    'statistics.e_reputation.reviews_analysis.topic_evolution.the_topic_does_not_exist_html' | translate: { topicText }
                "></div>
        </div>
        <button
            class="malou-btn-raised--secondary !h-8 !rounded-[5px] !bg-malou-color-background-white"
            mat-raised-button
            (click)="addTopic(topicText)">
            <span>{{ 'common.add' | translate }}</span>
        </button>
    </div>
</ng-template>

<ng-template
    let-illustration="illustration"
    let-title="title"
    let-subtitle="subtitle"
    let-actionText="actionText"
    let-onActionClick="onActionClick"
    #emptyTopicsTemplate>
    <div class="mt-10 flex h-[calc(100%-82px)] flex-col items-center justify-center">
        <div class="mb-4 w-24 md:mb-5 md:w-24">
            <img alt="Best food in town illustration" [src]="illustration | illustrationPathResolver" />
        </div>
        <div class="px-7 text-center">
            @if (title) {
                <span class="malou-text-14--bold mb-2 text-malou-color-text-1">
                    {{ title }}
                </span>
            }
            @if (subtitle) {
                <p class="malou-text-10--regular malou-color-text-2 mb-2">
                    {{ subtitle }}
                </p>
            }
            @if (actionText && onActionClick && !isPdfDownload()) {
                <button class="malou-btn-flat !malou-text-10--semibold !text-malou-color-primary" (click)="onActionClick()">
                    {{ actionText }}
                </button>
            }
        </div>
    </div>
</ng-template>
