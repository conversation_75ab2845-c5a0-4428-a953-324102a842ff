<div class="gap-[0.5px]" [ngClass]="wrapperClassesComputed()">
    <app-button
        [id]="id()"
        [text]="text()"
        [tooltip]="tooltip()"
        [disabled]="disabled()"
        [loading]="loading()"
        [buttonClasses]="mainButtonClassesComputed()"
        [customStyle]="customStyle"
        [testId]="testId()"
        [ngClass]="{ 'pointer-events-none': disabled() || loading() }"
        (buttonClick)="onMainButtonClick.emit()">
        <ng-template #textTemplate>
            <div
                class="flex items-center gap-x-3"
                [ngClass]="{ 'h-[36px]': size() === MenuButtonSize.MEDIUM, 'h-[50px]': size() === MenuButtonSize.LARGE }">
                <div class="malou-text-12--medium flex h-full items-center">
                    {{ text() }}
                </div>
            </div>
        </ng-template>
    </app-button>
    <app-button
        [disabled]="disabled()"
        [loading]="loading()"
        [customStyle]="customStyle"
        [buttonClasses]="menuButtonClassesComputed()"
        [matMenuTriggerFor]="matMenu"
        [ngClass]="{ 'pointer-events-none': disabled() || loading() }">
        <ng-template #textTemplate>
            <div
                class="flex items-center gap-x-3"
                [ngClass]="{ 'h-[36px]': size() === MenuButtonSize.MEDIUM, 'h-[50px]': size() === MenuButtonSize.LARGE }">
                <div class="flex items-center">
                    <mat-icon class="!w-4.5" [svgIcon]="SvgIcon.CHEVRON_DOWN"></mat-icon>
                </div>
            </div>
        </ng-template>
    </app-button>

    <mat-menu class="malou-mat-menu malou-box-shadow !rounded-xl" #matMenu="matMenu">
        <ng-content></ng-content>
    </mat-menu>
</div>
