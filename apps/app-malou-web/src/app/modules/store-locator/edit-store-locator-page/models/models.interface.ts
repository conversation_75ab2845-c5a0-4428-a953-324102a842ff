import { UpdateStoreLocatorStorePagesBodyDto } from '@malou-io/package-dto';

import { StoreLocatorPageBlockType } from ':modules/store-locator/edit-store-locator-page/edit-store-locator-page.interface';

interface StoreLocatorBlockDataUpdateWithStatus<T> {
    isModified: boolean;
    data: T | undefined;
}

export interface StoreLocatorBlockTypeUpdateMap {
    [StoreLocatorPageBlockType.INFORMATION]: NonNullable<
        StoreLocatorBlockDataUpdateWithStatus<UpdateStoreLocatorStorePagesBodyDto[number]['information']>
    >;
    [StoreLocatorPageBlockType.GALLERY]: NonNullable<
        StoreLocatorBlockDataUpdateWithStatus<UpdateStoreLocatorStorePagesBodyDto[number]['gallery']>
    >;
    [StoreLocatorPageBlockType.REVIEWS]: NonNullable<
        StoreLocatorBlockDataUpdateWithStatus<UpdateStoreLocatorStorePagesBodyDto[number]['reviews']>
    >;
    [StoreLocatorPageBlockType.CALL_TO_ACTION]: NonNullable<
        StoreLocatorBlockDataUpdateWithStatus<UpdateStoreLocatorStorePagesBodyDto[number]['callToActions']>
    >;
    [StoreLocatorPageBlockType.SOCIAL_NETWORKS]: NonNullable<
        StoreLocatorBlockDataUpdateWithStatus<UpdateStoreLocatorStorePagesBodyDto[number]['socialNetworks']>
    >;
    [StoreLocatorPageBlockType.DESCRIPTION]: NonNullable<
        StoreLocatorBlockDataUpdateWithStatus<UpdateStoreLocatorStorePagesBodyDto[number]['descriptions']>
    >;
}
