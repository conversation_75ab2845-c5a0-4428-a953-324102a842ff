import { ChangeDetectionStrategy, Component, computed, inject, signal, WritableSignal } from '@angular/core';
import { takeUntilDestroyed, toObservable } from '@angular/core/rxjs-interop';
import { AbstractControl, FormBuilder, FormGroup, ReactiveFormsModule, Validators } from '@angular/forms';
import { MatButtonModule } from '@angular/material/button';
import { MatExpansionModule } from '@angular/material/expansion';
import { MatIconModule } from '@angular/material/icon';
import { MatTabsModule } from '@angular/material/tabs';
import { MatTooltipModule } from '@angular/material/tooltip';
import { TranslateModule } from '@ngx-translate/core';
import { skip } from 'rxjs';

import { StoreLocatorRestaurantPageElementIds } from '@malou-io/package-utils';

import { StoreLocatorPageBlockFormComponent } from ':modules/store-locator/edit-store-locator-page/blocks/edit-store-locator-page-block-form.component';
import { PropertyType } from ':modules/store-locator/edit-store-locator-page/blocks/edit-store-locator-page-blocks.interface';
import { mapUpdateStylesConfiguration } from ':modules/store-locator/edit-store-locator-page/blocks/edit-store-locator-page.utils';
import {
    InformationBlockContent,
    InformationBlockContentForm,
    InformationBlockContentFormInputValidation,
    InformationBlockStyleData,
    InformationBlockStyleForm,
} from ':modules/store-locator/edit-store-locator-page/blocks/information-block/information-block.interface';
import { EditStoreLocatorPageColorSelectorComponent } from ':modules/store-locator/edit-store-locator-page/blocks/shared/color-selector/color-selector.component';
import { StoreLocatorPageBlockType } from ':modules/store-locator/edit-store-locator-page/edit-store-locator-page.interface';
import { SlideToggleComponent } from ':shared/components-v3/slide-toggle/slide-toggle.component';
import { ImageUploaderComponent } from ':shared/components/image-uploader/image-uploader.component';
import { InputTextComponent } from ':shared/components/input-text/input-text.component';
import { Media } from ':shared/models';
import { UrlValidator } from ':shared/validators/url.validator';

@Component({
    selector: 'app-store-locator-edit-page-information-block-form',
    templateUrl: './information-block-form.component.html',
    styleUrls: ['./information-block-form.component.scss'],
    imports: [
        MatTabsModule,
        TranslateModule,
        MatIconModule,
        MatTooltipModule,
        ReactiveFormsModule,
        ImageUploaderComponent,
        MatExpansionModule,
        SlideToggleComponent,
        InputTextComponent,
        MatButtonModule,
        EditStoreLocatorPageColorSelectorComponent,
        StoreLocatorPageBlockFormComponent,
    ],
    changeDetection: ChangeDetectionStrategy.OnPush,
})
export class StoreLocatorPageInformationBlockFormComponent extends StoreLocatorPageBlockFormComponent {
    private readonly _formBuilder = inject(FormBuilder);

    readonly InformationBlockContentFormInputValidation = InformationBlockContentFormInputValidation;

    contentForm: FormGroup<InformationBlockContentForm>;
    styleForm: FormGroup<InformationBlockStyleForm>;

    readonly isButtonsPanelExpanded: WritableSignal<boolean> = signal<boolean>(false);

    readonly informationBlockUpdatedData = computed(
        () => this.storeLocatorPageState()?.getBlockUpdatedData$(StoreLocatorPageBlockType.INFORMATION)?.()?.data
    );

    readonly title = computed(() => this.informationBlockUpdatedData()?.title ?? '');

    readonly uploadedMedia: WritableSignal<Media | null> = signal<Media | null>(null);
    readonly isCtaEnabled: WritableSignal<boolean> = signal<boolean>(true);

    constructor() {
        super();
        this._initContentForm();
        this._initStyleForm();
        toObservable(this.currentEditingRestaurant)
            .pipe(skip(1), takeUntilDestroyed(this.destroyRef))
            .subscribe(() => {
                this._patchContentForm();
            });
    }

    onCtaToggle(): void {
        this.isCtaEnabled.set(!this.isCtaEnabled());
        this.contentForm.get('cta.text')?.[this.isCtaEnabled() ? 'enable' : 'disable']();
        this.contentForm.get('cta.url')?.[this.isCtaEnabled() ? 'enable' : 'disable']();
    }

    onMediaSelected(media: Media | null): void {
        const informationBlockData = this.informationBlockUpdatedData();
        if (informationBlockData && media) {
            this.storeLocatorPageState()?.updateBlock({
                blockType: StoreLocatorPageBlockType.INFORMATION,
                blockData: {
                    ...informationBlockData,
                    image: {
                        description: informationBlockData.image.description,
                        url: media.urls.original,
                        mediaId: media.id,
                    },
                },
            });
        }
    }

    get titleControl(): AbstractControl | null {
        return this.contentForm.get('title');
    }

    get ctaTextControl(): AbstractControl | null {
        return this.contentForm.get('cta.text');
    }

    private _checkIfBlockInError(): void {
        const titleControl = this.titleControl;
        const ctaTextControl = this.contentForm.get('cta.text');
        const ctaUrlControl = this.contentForm.get('cta.url');
        if (!titleControl || !ctaTextControl || !ctaUrlControl) {
            return;
        }
        const isTitleInError = !!titleControl?.errors && titleControl.dirty;
        const isCtaTextInError = !!ctaTextControl?.errors && ctaTextControl.dirty;
        const isCtaUrlInError = !!ctaUrlControl?.errors && ctaUrlControl.dirty;
        this.editStoreLocatorPageContext.isBlockInError.set({
            blockType: StoreLocatorPageBlockType.INFORMATION,
            isError: isTitleInError || isCtaTextInError || isCtaUrlInError,
        });
    }

    private _initContentForm(): void {
        const cta = this.informationBlockUpdatedData()?.cta;
        if (!cta) {
            this.isCtaEnabled.set(false);
        }

        this.uploadedMedia.set(
            new Media({
                urls: { original: this.informationBlockUpdatedData()?.image?.url ?? '' },
                dimensions: {},
            })
        );

        this.contentForm = this._formBuilder.group({
            title: this._formBuilder.control(this.informationBlockUpdatedData()?.title ?? '', {
                validators: [
                    Validators.required,
                    Validators.minLength(InformationBlockContentFormInputValidation.TITLE_MIN_LENGTH),
                    Validators.maxLength(InformationBlockContentFormInputValidation.TITLE_MAX_LENGTH),
                ],
                nonNullable: true,
            }),
            cta: this._formBuilder.group({
                text: this._formBuilder.control(
                    {
                        value: cta?.text ?? '',
                        disabled: !this.isCtaEnabled(),
                    },
                    {
                        validators: [
                            Validators.required,
                            Validators.minLength(InformationBlockContentFormInputValidation.CTA_TEXT_MIN_LENGTH),
                            Validators.maxLength(InformationBlockContentFormInputValidation.CTA_TEXT_MAX_LENGTH),
                        ],
                        nonNullable: true,
                    }
                ),
                url: this._formBuilder.control(
                    { value: cta?.url ?? '', disabled: !this.isCtaEnabled() },
                    {
                        validators: [
                            Validators.required,
                            Validators.minLength(InformationBlockContentFormInputValidation.CTA_URL_MIN_LENGTH),
                            UrlValidator(),
                        ],
                        nonNullable: true,
                    }
                ),
            }),
        });

        this.contentForm.valueChanges.pipe(skip(1), takeUntilDestroyed(this.destroyRef)).subscribe((value: InformationBlockContent) => {
            const informationBlockData = this.informationBlockUpdatedData();
            this._checkIfBlockInError();
            if (informationBlockData) {
                this.storeLocatorPageState()?.updateBlock({
                    blockType: StoreLocatorPageBlockType.INFORMATION,
                    blockData: {
                        ...informationBlockData,
                        title: value.title,
                        cta: value.cta
                            ? {
                                  text: value.cta.text,
                                  url: value.cta.url,
                              }
                            : undefined,
                    },
                });
            }
        });
    }

    private _patchContentForm(): void {
        const cta = this.informationBlockUpdatedData()?.cta;
        if (!cta) {
            this.isCtaEnabled.set(false);
        }

        this.uploadedMedia.set(
            new Media({
                urls: { original: this.informationBlockUpdatedData()?.image?.url ?? '' },
                dimensions: {},
            })
        );

        this.contentForm.patchValue({
            title: this.informationBlockUpdatedData()?.title,
            cta: {
                text: this.informationBlockUpdatedData()?.cta?.text ?? '',
                url: this.informationBlockUpdatedData()?.cta?.url ?? '',
            },
        });
        this.contentForm.get('cta.text')?.[this.isCtaEnabled() ? 'enable' : 'disable']();
        this.contentForm.get('cta.url')?.[this.isCtaEnabled() ? 'enable' : 'disable']();
        this.contentForm.markAsPristine();
        this.isCtaEnabled.set(!!cta);
    }

    private _initStyleForm(): void {
        const styleMap = this.getStyleMap([
            StoreLocatorRestaurantPageElementIds.INFORMATION_WRAPPER,
            StoreLocatorRestaurantPageElementIds.INFORMATION_TITLE,
            StoreLocatorRestaurantPageElementIds.INFORMATION_ICONS,
            StoreLocatorRestaurantPageElementIds.INFORMATION_BANNER_CTA_2,
        ]);

        const informationBlockWrapperStyle = styleMap[StoreLocatorRestaurantPageElementIds.INFORMATION_WRAPPER] || {};
        const informationBlockTitleStyle = styleMap[StoreLocatorRestaurantPageElementIds.INFORMATION_TITLE] || {};
        const informationBlockIconStyle = styleMap[StoreLocatorRestaurantPageElementIds.INFORMATION_ICONS] || {};
        const informationBlockButtonStyle = styleMap[StoreLocatorRestaurantPageElementIds.INFORMATION_BANNER_CTA_2] || {};

        this.styleForm = this._formBuilder.group({
            general: this._formBuilder.group({
                titleColor: this._formBuilder.control(informationBlockTitleStyle.color, {
                    validators: [Validators.required],
                    nonNullable: true,
                }),
                backgroundColor: this._formBuilder.control(informationBlockWrapperStyle.backgroundColor, {
                    validators: [Validators.required],
                    nonNullable: true,
                }),
                textColor: this._formBuilder.control(informationBlockWrapperStyle.color, {
                    validators: [Validators.required],
                    nonNullable: true,
                }),
                iconColor: this._formBuilder.control(informationBlockIconStyle.fill, {
                    validators: [Validators.required],
                    nonNullable: true,
                }),
            }),
            buttons: this._formBuilder.group({
                primaryBackgroundColor: this._formBuilder.control(informationBlockButtonStyle.backgroundColor, {
                    validators: [Validators.required],
                    nonNullable: true,
                }),
                primaryBorderColor: this._formBuilder.control(informationBlockButtonStyle.borderColor, {
                    validators: [Validators.required],
                    nonNullable: true,
                }),
                primaryTextColor: this._formBuilder.control(informationBlockButtonStyle.color, {
                    validators: [Validators.required],
                    nonNullable: true,
                }),
            }),
        });
        this.styleForm.valueChanges.pipe(takeUntilDestroyed(this.destroyRef)).subscribe((value) => {
            this._updateStyleConfiguration(value as InformationBlockStyleData);
        });
    }

    private _updateStyleConfiguration(value: InformationBlockStyleData): void {
        const organizationStyleConfiguration = this.organizationStyleConfiguration();

        if (organizationStyleConfiguration) {
            const dataToUpdate = mapUpdateStylesConfiguration(organizationStyleConfiguration, [
                {
                    elementId: StoreLocatorRestaurantPageElementIds.INFORMATION_WRAPPER,
                    data: [
                        {
                            propertyType: PropertyType.BackgroundColor,
                            value: value.general.backgroundColor,
                        },
                        {
                            propertyType: PropertyType.Color,
                            value: value.general.textColor,
                        },
                    ],
                },
                {
                    elementId: StoreLocatorRestaurantPageElementIds.INFORMATION_TITLE,
                    data: [
                        {
                            propertyType: PropertyType.Color,
                            value: value.general.titleColor,
                        },
                    ],
                },
                {
                    elementId: StoreLocatorRestaurantPageElementIds.INFORMATION_ICONS,
                    data: [
                        {
                            propertyType: PropertyType.Fill,
                            value: value.general.iconColor,
                        },
                    ],
                },
                {
                    elementId: StoreLocatorRestaurantPageElementIds.INFORMATION_BANNER_CTA_2,
                    data: [
                        {
                            propertyType: PropertyType.BackgroundColor,
                            value: value.buttons.primaryBackgroundColor,
                        },
                        {
                            propertyType: PropertyType.BorderColor,
                            value: value.buttons.primaryBorderColor,
                        },
                        {
                            propertyType: PropertyType.Color,
                            value: value.buttons.primaryTextColor,
                        },
                    ],
                },
            ]);
            organizationStyleConfiguration.updateStyle(dataToUpdate);
        }
    }
}
