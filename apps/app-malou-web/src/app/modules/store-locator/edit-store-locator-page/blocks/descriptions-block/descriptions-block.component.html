<div
    class="flex h-fit flex-col items-center justify-center"
    [class.cursor-not-allowed]="shouldShowCursorNotAllowed()"
    (click)="handleShowFormBlock(StoreLocatorPageBlockType.DESCRIPTION)">
    @for (item of items(); track $index) {
        @let isEven = $index % 2 === 0;
        <div
            class="w-full"
            [ngStyle]="
                isEven
                    ? stylesMap()[StoreLocatorRestaurantPageElementIds.DESCRIPTIONS_BLOCK_EVEN]
                    : stylesMap()[StoreLocatorRestaurantPageElementIds.DESCRIPTIONS_BLOCK_UNEVEN]
            ">
            <div class="flex w-full justify-center">
                <div class="relative h-auto w-1/2 overflow-hidden" [class.order-last]="isEven">
                    <img class="absolute h-full w-full object-cover object-center" [src]="item.imageUrl" [alt]="item.imageDescription" />
                </div>
                <div class="box-border w-1/2 px-14 py-28">
                    <h2
                        class="mb-6 w-full text-4xl font-extrabold"
                        [ngStyle]="
                            isEven
                                ? stylesMap()[StoreLocatorRestaurantPageElementIds.DESCRIPTIONS_BLOCK_TITLE_EVEN]
                                : stylesMap()[StoreLocatorRestaurantPageElementIds.DESCRIPTIONS_BLOCK_TITLE_UNEVEN]
                        ">
                        {{ item.title }}
                    </h2>

                    <div class="flex flex-col gap-4">
                        @for (descriptionBlock of item.blocks; track $index) {
                            <h3 class="text-xl" [ngStyle]="stylesMap()[StoreLocatorRestaurantPageElementIds.DESCRIPTIONS_BLOCK_SUBTITLE]">
                                {{ descriptionBlock.title }}
                            </h3>
                            <p class="text-[1rem]">{{ descriptionBlock.text }}</p>
                        }
                    </div>
                </div>
            </div>
        </div>
    }
</div>
