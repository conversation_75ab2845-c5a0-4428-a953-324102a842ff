import { ChangeDetectionStrategy, Component, computed, inject, signal, WritableSignal } from '@angular/core';
import { takeUntilDestroyed, toObservable } from '@angular/core/rxjs-interop';
import { FormArray, FormBuilder, FormGroup, ReactiveFormsModule, Validators } from '@angular/forms';
import { MatButtonModule } from '@angular/material/button';
import { MatExpansionModule } from '@angular/material/expansion';
import { MatIconModule } from '@angular/material/icon';
import { MatTabsModule } from '@angular/material/tabs';
import { MatTooltipModule } from '@angular/material/tooltip';
import { TranslateModule } from '@ngx-translate/core';
import { skip } from 'rxjs';

import { StoreLocatorRestaurantPageElementIds } from '@malou-io/package-utils';

import {
    DescriptionBlockSection,
    DescriptionsBlockContentFormInputValidation,
    DescriptionsBlockSectionForm,
    DescriptionsBlockStyleData,
    DescriptionsBlockStyleForm,
} from ':modules/store-locator/edit-store-locator-page/blocks/descriptions-block/descriptions-block.interface';
import { StoreLocatorPageBlockFormComponent } from ':modules/store-locator/edit-store-locator-page/blocks/edit-store-locator-page-block-form.component';
import { PropertyType } from ':modules/store-locator/edit-store-locator-page/blocks/edit-store-locator-page-blocks.interface';
import { mapUpdateStylesConfiguration } from ':modules/store-locator/edit-store-locator-page/blocks/edit-store-locator-page.utils';
import { EditStoreLocatorPageColorSelectorComponent } from ':modules/store-locator/edit-store-locator-page/blocks/shared/color-selector/color-selector.component';
import { StoreLocatorPageBlockType } from ':modules/store-locator/edit-store-locator-page/edit-store-locator-page.interface';
import { ImageUploaderComponent } from ':shared/components/image-uploader/image-uploader.component';
import { InputTextComponent } from ':shared/components/input-text/input-text.component';
import { TextAreaComponent } from ':shared/components/text-area/text-area.component';
import { Media } from ':shared/models';

@Component({
    selector: 'app-store-locator-edit-page-descriptions-block-form',
    templateUrl: './descriptions-block-form.component.html',
    styleUrls: ['./descriptions-block-form.component.scss'],
    imports: [
        TranslateModule,
        MatTabsModule,
        MatIconModule,
        MatTooltipModule,
        MatButtonModule,
        StoreLocatorPageBlockFormComponent,
        InputTextComponent,
        ReactiveFormsModule,
        MatExpansionModule,
        EditStoreLocatorPageColorSelectorComponent,
        TextAreaComponent,
        ImageUploaderComponent,
    ],
    changeDetection: ChangeDetectionStrategy.OnPush,
})
export class StoreLocatorPageDescriptionBlockFormComponent extends StoreLocatorPageBlockFormComponent {
    private readonly _formBuilder = inject(FormBuilder);

    readonly DescriptionsBlockContentFormInputValidation = DescriptionsBlockContentFormInputValidation;

    contentForm: FormGroup<DescriptionsBlockSectionForm>;
    styleForm: FormGroup<DescriptionsBlockStyleForm>;

    readonly isButtonsPanelExpanded: WritableSignal<boolean> = signal<boolean>(false);

    readonly uploadedMedias: WritableSignal<Media[]> = signal<Media[]>([]);

    readonly descriptionsBlockData = computed(
        () => this.storeLocatorPageState()?.getBlockUpdatedData$(StoreLocatorPageBlockType.DESCRIPTION)?.()?.data
    );

    constructor() {
        super();
        toObservable(this.currentEditingRestaurant)
            .pipe(skip(1), takeUntilDestroyed(this.destroyRef))
            .subscribe(() => {
                this._patchContentForm();
            });
        this._initContentForm();
        this._initStyleForm();
    }

    get items(): DescriptionsBlockSectionForm['items'] {
        return this.contentForm.get('items') as FormArray;
    }

    getSectionContent(index: number): FormArray {
        return this.items.at(index).get('content') as FormArray;
    }

    onMediaSelected(media: Media | null, index: number): void {
        const section = this.items.at(index);
        const imageUrlControl = section.get('imageUrl');
        if (media) {
            imageUrlControl?.setValue(media.getMediaUrl());
        }
    }

    getMedia(index: number): Media | null {
        return this.uploadedMedias()[index] || null;
    }

    setMedia(index: number, media: Media | null): void {
        if (media) {
            this.uploadedMedias.update((medias) => {
                const updatedMedias = [...medias];
                updatedMedias[index] = media;
                return updatedMedias;
            });
            this._updateMedias();
        }
    }

    private _checkIfBlockInError(): void {
        const isAtLeastOneSectionInError = this.items.controls.some((section) => {
            const titleControl = section.get('title');
            const contentControls = section.get('content') as FormArray;
            const isTitleInError = !!titleControl?.errors && titleControl.dirty;
            const isContentInError = contentControls.controls.some((contentControl) => {
                const subtitleControl = contentControl.get('subtitle');
                const textControl = contentControl.get('text');
                return (!!subtitleControl?.errors && subtitleControl.dirty) || (!!textControl?.errors && textControl.dirty);
            });
            return isTitleInError || isContentInError;
        });
        this.editStoreLocatorPageContext.isBlockInError.set({
            blockType: StoreLocatorPageBlockType.DESCRIPTION,
            isError: isAtLeastOneSectionInError,
        });
    }

    private _initContentForm(): void {
        const descriptionBlockData = this.descriptionsBlockData();
        if (!this.contentForm) {
            this.contentForm = this._formBuilder.group({
                items: this._formBuilder.array(
                    descriptionBlockData?.items.map((data) =>
                        this._createDescriptionSectionGroupForm({
                            title: data.title,
                            imageUrl: data.image.url,
                            content: data.blocks.map((contentData) => ({
                                subtitle: contentData.title,
                                text: contentData.text,
                            })),
                        })
                    ) || []
                ),
            });

            this.contentForm.valueChanges.subscribe((value: { items: DescriptionBlockSection[] }) => {
                const blockData = this.descriptionsBlockData();
                this._checkIfBlockInError();

                if (blockData && blockData?.items.length !== 0) {
                    this.storeLocatorPageState()?.updateBlock({
                        blockType: StoreLocatorPageBlockType.DESCRIPTION,
                        blockData: {
                            items: blockData.items.map((item, index) => ({
                                image: {
                                    url: this.uploadedMedias()[index]?.urls?.original ?? item.image.url,
                                    description: blockData.items[index].image.description,
                                    mediaId: this.uploadedMedias()[index]?.id || undefined,
                                },
                                title: value.items[index]?.title ?? item.title,
                                blocks: item.blocks.map((content, subIndex) => ({
                                    title: value.items[index]?.content[subIndex].subtitle ?? content.title,
                                    text: value.items[index]?.content[subIndex].text ?? content.text,
                                })),
                            })),
                        },
                    });
                }
            });
        }
    }

    private _createDescriptionSectionGroupForm(data: DescriptionBlockSection): FormGroup {
        this.uploadedMedias.update((medias) => [
            ...medias,
            new Media({
                urls: { original: data.imageUrl },
                dimensions: {},
            }),
        ]);
        return this._formBuilder.group({
            title: this._formBuilder.control(data.title, {
                validators: [
                    Validators.required,
                    Validators.minLength(DescriptionsBlockContentFormInputValidation.TITLE_MIN_LENGTH),
                    Validators.maxLength(DescriptionsBlockContentFormInputValidation.TITLE_MAX_LENGTH),
                ],
                nonNullable: true,
            }),
            content: this._formBuilder.array(data.content.map((contentData) => this._createDescriptionContentGroupForm(contentData))),
        });
    }

    private _createDescriptionContentGroupForm(data: DescriptionBlockSection['content'][number]): FormGroup {
        return this._formBuilder.group({
            subtitle: this._formBuilder.control(data.subtitle, {
                validators: [
                    Validators.required,
                    Validators.minLength(DescriptionsBlockContentFormInputValidation.SUBTITLE_MIN_LENGTH),
                    Validators.maxLength(DescriptionsBlockContentFormInputValidation.SUBTITLE_MAX_LENGTH),
                ],
                nonNullable: true,
            }),
            text: this._formBuilder.control(data.text, {
                validators: [Validators.required, Validators.minLength(DescriptionsBlockContentFormInputValidation.TEXT_MIN_LENGTH)],
                nonNullable: true,
            }),
        });
    }

    private _patchContentForm(): void {
        const descriptionBlockData = this.descriptionsBlockData();
        if (!descriptionBlockData) {
            return;
        }
        this.items.clear();
        this.uploadedMedias.set([]);

        descriptionBlockData.items.forEach((descriptionSection) => {
            this.items.push(
                this._createDescriptionSectionGroupForm({
                    title: descriptionSection.title,
                    imageUrl: descriptionSection.image.url,
                    content: descriptionSection.blocks.map((block) => ({
                        subtitle: block.title,
                        text: block.text,
                    })),
                })
            );
        });
        this.contentForm.markAsPristine();
    }

    private _updateMedias(): void {
        const descriptionsBlockData = this.descriptionsBlockData();
        const uploadedMedias = this.uploadedMedias();
        if (descriptionsBlockData && uploadedMedias) {
            this.storeLocatorPageState()?.updateBlock({
                blockType: StoreLocatorPageBlockType.DESCRIPTION,
                blockData: {
                    items: descriptionsBlockData.items.map((item, index) => ({
                        ...item,
                        image: {
                            ...item.image,
                            url: uploadedMedias[index]?.urls?.original ?? item.image.url,
                        },
                    })),
                },
            });
        }
    }

    private _initStyleForm(): void {
        const styleMap = this.getStyleMap([
            StoreLocatorRestaurantPageElementIds.DESCRIPTIONS_BLOCK_EVEN,
            StoreLocatorRestaurantPageElementIds.DESCRIPTIONS_BLOCK_UNEVEN,
        ]);

        const descriptionBlockEven = styleMap[StoreLocatorRestaurantPageElementIds.DESCRIPTIONS_BLOCK_EVEN] || {};
        const descriptionBlockUneven = styleMap[StoreLocatorRestaurantPageElementIds.DESCRIPTIONS_BLOCK_UNEVEN] || {};

        this.styleForm = this._formBuilder.group({
            generalEven: this._formBuilder.group({
                textColor: this._formBuilder.control(descriptionBlockEven.color, {
                    validators: [Validators.required],
                    nonNullable: true,
                }),
                backgroundColor: this._formBuilder.control(descriptionBlockEven.backgroundColor, {
                    validators: [Validators.required],
                    nonNullable: true,
                }),
            }),
            generalUneven: this._formBuilder.group({
                textColor: this._formBuilder.control(descriptionBlockUneven.color, {
                    validators: [Validators.required],
                    nonNullable: true,
                }),
                backgroundColor: this._formBuilder.control(descriptionBlockUneven.backgroundColor, {
                    validators: [Validators.required],
                    nonNullable: true,
                }),
            }),
        });

        this.styleForm.valueChanges.subscribe((value) => {
            this._updateStyleConfiguration(value as DescriptionsBlockStyleData);
        });
    }

    private _updateStyleConfiguration(value: DescriptionsBlockStyleData): void {
        const organizationStyleConfiguration = this.organizationStyleConfiguration();

        if (organizationStyleConfiguration) {
            const dataToUpdate = mapUpdateStylesConfiguration(organizationStyleConfiguration, [
                {
                    elementId: StoreLocatorRestaurantPageElementIds.DESCRIPTIONS_BLOCK_EVEN,
                    data: [
                        {
                            value: value.generalEven.backgroundColor,
                            propertyType: PropertyType.BackgroundColor,
                        },
                        {
                            value: value.generalEven.textColor,
                            propertyType: PropertyType.Color,
                        },
                    ],
                },
                {
                    elementId: StoreLocatorRestaurantPageElementIds.DESCRIPTIONS_BLOCK_UNEVEN,
                    data: [
                        {
                            value: value.generalUneven.textColor,
                            propertyType: PropertyType.Color,
                        },
                        {
                            value: value.generalUneven.backgroundColor,
                            propertyType: PropertyType.BackgroundColor,
                        },
                    ],
                },
            ]);
            organizationStyleConfiguration.updateStyle(dataToUpdate);
        }
    }
}
