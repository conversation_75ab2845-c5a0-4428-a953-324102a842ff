import { ChangeDetectionStrategy, ChangeDetectorRef, Component, computed, inject, signal, WritableSignal } from '@angular/core';
import { takeUntilDestroyed, toObservable } from '@angular/core/rxjs-interop';
import { AbstractControl, FormBuilder, FormGroup, ReactiveFormsModule, Validators } from '@angular/forms';
import { MatButtonModule } from '@angular/material/button';
import { MatExpansionModule } from '@angular/material/expansion';
import { MatIconModule } from '@angular/material/icon';
import { MatTabsModule } from '@angular/material/tabs';
import { MatTooltipModule } from '@angular/material/tooltip';
import { TranslateModule } from '@ngx-translate/core';
import { skip } from 'rxjs';

import { StoreLocatorRestaurantPageElementIds } from '@malou-io/package-utils';

import { StoreLocatorPageBlockFormComponent } from ':modules/store-locator/edit-store-locator-page/blocks/edit-store-locator-page-block-form.component';
import { PropertyType } from ':modules/store-locator/edit-store-locator-page/blocks/edit-store-locator-page-blocks.interface';
import { mapUpdateStylesConfiguration } from ':modules/store-locator/edit-store-locator-page/blocks/edit-store-locator-page.utils';
import { EditStoreLocatorPageColorSelectorComponent } from ':modules/store-locator/edit-store-locator-page/blocks/shared/color-selector/color-selector.component';
import {
    SocialNetworksBlockContentForm,
    SocialNetworksBlockContentFormData,
    SocialNetworksBlockContentFormInputValidation,
    SocialNetworksBlockStyleData,
    SocialNetworksBlockStyleForm,
} from ':modules/store-locator/edit-store-locator-page/blocks/social-networks-block/social-networks-block.interface';
import { StoreLocatorPageBlockType } from ':modules/store-locator/edit-store-locator-page/edit-store-locator-page.interface';
import { InputTextComponent } from ':shared/components/input-text/input-text.component';

@Component({
    selector: 'app-store-locator-edit-page-social-networks-block-form',
    templateUrl: './social-networks-block-form.component.html',
    styleUrls: ['./social-networks-block-form.component.scss'],
    imports: [
        TranslateModule,
        MatTabsModule,
        MatIconModule,
        MatTooltipModule,
        MatButtonModule,
        StoreLocatorPageBlockFormComponent,
        InputTextComponent,
        ReactiveFormsModule,
        MatExpansionModule,
        EditStoreLocatorPageColorSelectorComponent,
    ],
    changeDetection: ChangeDetectionStrategy.OnPush,
})
export class StoreLocatorPageSocialNetworksBlockFormComponent extends StoreLocatorPageBlockFormComponent {
    private readonly _formBuilder = inject(FormBuilder);
    private readonly _cdr = inject(ChangeDetectorRef);

    readonly InformationBlockContentFormInputValidation = SocialNetworksBlockContentFormInputValidation;

    contentForm: FormGroup<SocialNetworksBlockContentForm>;
    styleForm: FormGroup<SocialNetworksBlockStyleForm>;

    readonly isButtonsPanelExpanded: WritableSignal<boolean> = signal<boolean>(false);

    readonly socialNetworksBlockData = computed(
        () =>
            this.editStoreLocatorPageContext
                .selectedRestaurantStorePageState()
                ?.getBlockUpdatedData$(StoreLocatorPageBlockType.SOCIAL_NETWORKS)?.()?.data
    );

    readonly title = computed(() => this.socialNetworksBlockData()?.title ?? '');

    constructor() {
        super();
        toObservable(this.currentEditingRestaurant)
            .pipe(skip(1), takeUntilDestroyed(this.destroyRef))
            .subscribe(() => {
                this._patchContentForm();
            });
        this._initContentForm();
        this._initStyleForm();
    }

    get titleControl(): AbstractControl | null {
        return this.contentForm.get('title');
    }

    private _checkIfBlockInError(): void {
        const titleControl = this.titleControl;
        if (!titleControl) {
            return;
        }
        const isTitleInError = !!titleControl?.errors && titleControl.dirty;

        this.editStoreLocatorPageContext.isBlockInError.set({
            blockType: StoreLocatorPageBlockType.SOCIAL_NETWORKS,
            isError: isTitleInError,
        });
    }

    private _initContentForm(): void {
        if (!this.contentForm) {
            this.contentForm = this._formBuilder.group({
                title: this._formBuilder.control(this.socialNetworksBlockData()?.title ?? '', {
                    validators: [
                        Validators.required,
                        Validators.minLength(SocialNetworksBlockContentFormInputValidation.TITLE_MIN_LENGTH),
                        Validators.maxLength(SocialNetworksBlockContentFormInputValidation.TITLE_MAX_LENGTH),
                    ],
                    nonNullable: true,
                }),
            });

            this.contentForm.valueChanges.subscribe((value: SocialNetworksBlockContentFormData) => {
                const socialNetworksBlockData = this.socialNetworksBlockData();
                this._checkIfBlockInError();
                if (socialNetworksBlockData) {
                    this.storeLocatorPageState()?.updateBlock({
                        blockType: StoreLocatorPageBlockType.SOCIAL_NETWORKS,
                        blockData: {
                            ...socialNetworksBlockData,
                            title: value.title,
                        },
                    });
                }
            });
        }
    }

    private _patchContentForm(): void {
        const socialNetworksBlockData = this.socialNetworksBlockData();
        if (!socialNetworksBlockData) {
            return;
        }

        this.contentForm.patchValue({
            title: socialNetworksBlockData.title,
        });
        this.contentForm.markAsPristine();
    }

    private _initStyleForm(): void {
        const styleMap = this.getStyleMap([
            StoreLocatorRestaurantPageElementIds.SOCIAL_NETWORKS_TITLE,
            StoreLocatorRestaurantPageElementIds.SOCIAL_NETWORKS_WRAPPER,
            StoreLocatorRestaurantPageElementIds.SOCIAL_NETWORKS_PROFILE,
            StoreLocatorRestaurantPageElementIds.SOCIAL_NETWORKS_PROFILE_NAME,
        ]);

        const socialNetworksBlockWrapperStyle = styleMap[StoreLocatorRestaurantPageElementIds.SOCIAL_NETWORKS_WRAPPER] || {};
        const socialNetworksBlockTitleStyle = styleMap[StoreLocatorRestaurantPageElementIds.SOCIAL_NETWORKS_TITLE] || {};

        this.styleForm = this._formBuilder.group({
            general: this._formBuilder.group({
                titleColor: this._formBuilder.control(socialNetworksBlockTitleStyle.color, {
                    validators: [Validators.required],
                    nonNullable: true,
                }),
                backgroundColor: this._formBuilder.control(socialNetworksBlockWrapperStyle.backgroundColor, {
                    validators: [Validators.required],
                    nonNullable: true,
                }),
            }),
        });
        this.styleForm.valueChanges.subscribe((value) => {
            this._updateStyleConfiguration(value as SocialNetworksBlockStyleData);
        });
    }

    private _updateStyleConfiguration(value: SocialNetworksBlockStyleData): void {
        const organizationStyleConfiguration = this.organizationStyleConfiguration();

        if (organizationStyleConfiguration) {
            const dataToUpdate = mapUpdateStylesConfiguration(organizationStyleConfiguration, [
                {
                    elementId: StoreLocatorRestaurantPageElementIds.SOCIAL_NETWORKS_WRAPPER,
                    data: [
                        {
                            value: value.general.backgroundColor,
                            propertyType: PropertyType.BackgroundColor,
                        },
                    ],
                },
                {
                    elementId: StoreLocatorRestaurantPageElementIds.SOCIAL_NETWORKS_TITLE,
                    data: [
                        {
                            value: value.general.titleColor,
                            propertyType: PropertyType.Color,
                        },
                    ],
                },
            ]);
            organizationStyleConfiguration.updateStyle(dataToUpdate);
        }
    }
}
