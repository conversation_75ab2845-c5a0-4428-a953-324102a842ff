<div
    class="flex h-fit sm:flex-row"
    [ngStyle]="stylesMap()[StoreLocatorRestaurantPageElementIds.INFORMATION_WRAPPER]"
    [class.cursor-not-allowed]="shouldShowCursorNotAllowed()"
    (click)="handleShowFormBlock(StoreLocatorPageBlockType.INFORMATION)">
    <div class="h-[85vh] w-[50%] overflow-hidden sm:h-[450px] lg:h-auto lg:min-h-0 lg:flex-1">
        <img
            class="h-full w-full object-cover"
            [alt]="image().alt"
            [defaultImage]="Illustration.Default | imagePathResolver"
            [lazyLoad]="image().src || ''" />
    </div>

    <div class="flex min-h-fit w-full flex-1 flex-col gap-3 px-4 py-8 md:px-16">
        @if (isNotOpenedYet()) {
            <p
                class="mb-4 p-6 text-center text-xl font-bold lg:text-3xl"
                [ngStyle]="stylesMap()[StoreLocatorRestaurantPageElementIds.INFORMATION_OPENING_SOON_BANNER]">
                OPENING SOON
            </p>
        }

        <h1 class="text-5xl md:text-6xl" [ngStyle]="stylesMap()[StoreLocatorRestaurantPageElementIds.INFORMATION_TITLE]">
            {{ title() }}
        </h1>

        <div class="mt-6 grid auto-cols-auto grid-cols-[repeat(auto-fit,minmax(50%,1fr))] gap-x-3 gap-y-2 text-[25px] font-normal">
            <div class="mb-0 sm:mb-4">
                <a target="_blank" [href]="itineraryUrl()">
                    <p class="flex items-start gap-6 text-base sm:gap-4">
                        <mat-icon
                            class="!h-5 !w-5"
                            [ngStyle]="stylesMap()[StoreLocatorRestaurantPageElementIds.INFORMATION_ICONS]"
                            [svgIcon]="SvgIcon.PIN"></mat-icon>
                        <span>{{ fullAddress() }}</span>
                    </p>
                </a>
            </div>

            <div class="mb-0">
                @if (phoneNumber()) {
                    <a [href]="'tel:' + phoneNumber()">
                        <p class="flex items-center gap-4 text-base">
                            <mat-icon
                                class="!h-5 !w-5"
                                [ngStyle]="stylesMap()[StoreLocatorRestaurantPageElementIds.INFORMATION_ICONS]"
                                [svgIcon]="SvgIcon.PHONE_FILLED"></mat-icon>

                            {{ phoneNumber() }}
                        </p>
                    </a>
                }
            </div>

            <div class="col-span-2 mb-0">
                <div class="my-4 w-full border-t" [ngStyle]="{ borderColor: primaryColor() }"></div>
            </div>

            @if (informationFormattedHours().length > 0) {
                <div class="row-start-3">
                    <div class="flex gap-4">
                        <mat-icon
                            class="!h-5 !w-5"
                            [ngStyle]="stylesMap()[StoreLocatorRestaurantPageElementIds.INFORMATION_ICONS]"
                            [svgIcon]="SvgIcon.HOUR"></mat-icon>

                        <div class="flex flex-col gap-2">
                            @for (day of informationFormattedHours().slice(0, 4); track day) {
                                <p class="text-base" [ngStyle]="day.extraStyle">
                                    {{ day.value }}
                                </p>
                            }
                        </div>
                    </div>
                </div>

                <div class="row-start-3 mb-4 mt-2 md:mb-0 md:ml-0 md:mt-0">
                    <div class="flex flex-col gap-2">
                        @for (day of informationFormattedHours().slice(4, 8); track day) {
                            <p class="text-base" [ngStyle]="day.extraStyle">
                                {{ day.value }}
                            </p>
                        }
                    </div>
                </div>

                <div class="col-span-2 mb-0">
                    <div class="my-4 w-full border-t" [ngStyle]="{ borderColor: primaryColor() }"></div>
                </div>
            }

            <div class="col-span-2 mb-0">
                <div class="flex items-center gap-4">
                    <mat-icon
                        class="!h-5 !w-5"
                        [ngStyle]="stylesMap()[StoreLocatorRestaurantPageElementIds.INFORMATION_ICONS]"
                        [svgIcon]="SvgIcon.CART"></mat-icon>

                    <p class="text-base">
                        {{ informationAttributes() }}
                    </p>
                </div>
            </div>

            <div class="col-span-2 mb-0">
                <div class="my-4 w-full border-t" [ngStyle]="{ borderColor: primaryColor() }"></div>
            </div>

            <div class="col-span-2 row-start-7">
                <div class="flex items-center gap-4">
                    <mat-icon
                        class="!h-5 !w-5"
                        [ngStyle]="stylesMap()[StoreLocatorRestaurantPageElementIds.INFORMATION_ICONS]"
                        [svgIcon]="SvgIcon.VOUCHER"></mat-icon>

                    <p class="text-base">
                        {{ informationPaymentMethods() }}
                    </p>
                </div>
            </div>
        </div>
        <div class="mt-14 flex w-full justify-center">
            @if (informationCta()) {
                <a
                    class="w-fit items-center border-[1px] border-solid px-7 py-4 text-base font-extralight shadow-md"
                    target="_blank"
                    [href]="informationCta()?.url"
                    [ngStyle]="stylesMap()[StoreLocatorRestaurantPageElementIds.INFORMATION_BANNER_CTA_2]">
                    <span>{{ informationCta()?.text }}</span>
                </a>
            }
        </div>
    </div>
</div>
