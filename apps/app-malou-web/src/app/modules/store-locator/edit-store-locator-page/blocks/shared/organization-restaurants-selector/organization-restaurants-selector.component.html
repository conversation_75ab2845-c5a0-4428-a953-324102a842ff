<div class="mb-2 flex flex-col gap-1">
    <div class="flex items-center gap-x-1">
        <p class="text-[15px] font-extrabold text-malou-color-text-1">
            {{ 'store_locator.edit_modal.location.selected' | translate }}
        </p>
        <mat-icon
            class="!h-4 !w-4"
            [matTooltip]="'store_locator.edit_modal.location.tooltip' | translate"
            [svgIcon]="SvgIcon.INFO_ROUND"></mat-icon>
    </div>
</div>
@if (organizationRestaurants()?.length !== 0 && currentEditingRestaurant() !== null) {
    <app-select [values]="organizationRestaurants()!" [formControl]="control()" [displayWith]="restaurantDisplayWith">
        <ng-template let-value="value" #simpleSelectedValueTemplate>
            <div class="flex items-center gap-x-2 py-1">
                <div class="malou-text-12--semibold">
                    {{ value | applySelfPure: 'getDisplayName' }}
                </div>
            </div>
        </ng-template>
        <ng-template let-value="value" let-isValueSelected="isValueSelected" #optionTemplate>
            <div class="py-1">
                <div class="flex items-center gap-x-2">
                    <div class="malou-text-12--semibold" [class.malou-color-text-1]="isValueSelected">
                        {{ value | applySelfPure: 'getDisplayName' }}
                    </div>
                    <div>
                        <img
                            class="h-4 w-4"
                            [alt]="
                                ((value | applySelfPure: 'isBrandBusiness') ? 'common.brand_account' : 'common.business_account')
                                    | translate
                            "
                            [matTooltip]="
                                ((value | applySelfPure: 'isBrandBusiness') ? 'common.brand_account' : 'common.business_account')
                                    | translate
                            "
                            [matTooltipTouchGestures]="'off'"
                            [src]="((value | applySelfPure: 'isBrandBusiness') ? 'brand' : 'business') | imagePathResolver" />
                    </div>
                </div>
                <div class="malou-text-10--regular font-normal italic text-malou-color-text-2">
                    {{
                        (value | applySelfPure: 'isBrandBusiness') ? ('common.brand_account' | translate) : value?.getFullFormattedAddress()
                    }}
                </div>
            </div>
        </ng-template>
    </app-select>
}
