import { ChangeDetectionStrategy, Component, input } from '@angular/core';
import { MatIconModule } from '@angular/material/icon';
import { MatTooltipModule } from '@angular/material/tooltip';
import { TranslateModule } from '@ngx-translate/core';
import { LazyLoadImageModule } from 'ng-lazyload-image';

import { StoreLocatorOrganizationRestaurant } from ':modules/store-locator/models/store-locator-organization-restaurant';
import { SelectComponent } from ':shared/components/select/select.component';
import { SvgIcon } from ':shared/modules/svg-icon.enum';
import { ApplySelfPurePipe } from ':shared/pipes/apply-fn.pipe';
import { ImagePathResolverPipe } from ':shared/pipes/image-path-resolver.pipe';

@Component({
    selector: 'app-store-locator-edit-page-organization-restaurants-selector',
    templateUrl: './organization-restaurants-selector.component.html',
    styleUrls: ['./organization-restaurants-selector.component.scss'],
    imports: [
        LazyLoadImageModule,
        MatIconModule,
        MatTooltipModule,
        TranslateModule,
        SelectComponent,
        ApplySelfPurePipe,
        ImagePathResolverPipe,
    ],
    changeDetection: ChangeDetectionStrategy.OnPush,
})
export class EditStoreLocatorPageOrganizationRestaurantsSelectorComponent {
    readonly organizationRestaurants = input<StoreLocatorOrganizationRestaurant[]>();
    readonly currentEditingRestaurant = input<StoreLocatorOrganizationRestaurant | null>();
    readonly disabled = input<boolean>(false);
    readonly control = input.required<any>();

    readonly SvgIcon = SvgIcon;

    compareByRestaurantId(restaurant: StoreLocatorOrganizationRestaurant): string {
        if (!restaurant) {
            return '';
        }
        return restaurant.id;
    }

    restaurantDisplayWith(restaurant: StoreLocatorOrganizationRestaurant): string {
        return restaurant.getDisplayName();
    }
}
