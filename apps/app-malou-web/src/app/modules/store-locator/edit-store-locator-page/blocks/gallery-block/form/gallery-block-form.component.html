<app-edit-store-locator-page-block-form-wrapper [contentFormTemplate]="contentFormTemplate" [styleFormTemplate]="styleFormTemplate" />

<ng-template #contentFormTemplate>
    <div class="flex flex-col gap-5" [formGroup]="contentForm">
        <app-input-text
            class="malou-text-14--bold!"
            formControlName="title"
            [defaultValue]="title()"
            [title]="'store_locator.edit_modal.controls.title.name' | translate"
            [disabled]="false"
            [errorMessage]="
                titleControl?.errors && (titleControl?.errors?.minlength || titleControl?.errors?.maxlength)
                    ? ('store_locator.edit_modal.controls.title.length_error'
                      | translate
                          : {
                                minLength: InformationBlockContentFormInputValidation.TITLE_MIN_LENGTH,
                                maxLength: InformationBlockContentFormInputValidation.TITLE_MAX_LENGTH,
                            })
                    : ''
            "
            [isEmojiPickerEnabled]="true"
            [placeholder]="'store_locator.edit_modal.controls.title.placeholder_text' | translate"
            [autocapitalize]="'none'">
        </app-input-text>

        <app-text-area
            class="malou-text-14--bold!"
            formControlName="subtitle"
            [defaultValue]="subtitle()"
            [title]="'store_locator.edit_modal.controls.subtitle.name' | translate"
            [disabled]="false"
            [isEmojiPickerEnabled]="true"
            [rows]="2"
            [placeholder]="'store_locator.edit_modal.controls.subtitle.placeholder' | translate"
            [errorMessage]="
                subtitleControl?.errors && (subtitleControl?.errors?.minlength || subtitleControl?.errors?.maxlength)
                    ? ('store_locator.edit_modal.controls.subtitle.length_error'
                      | translate
                          : {
                                minLength: InformationBlockContentFormInputValidation.SUBTITLE_MIN_LENGTH,
                                maxLength: InformationBlockContentFormInputValidation.SUBTITLE_MAX_LENGTH,
                            })
                    : ''
            "
            [autocapitalize]="'none'">
        </app-text-area>

        <div class="flex w-full items-center justify-between">
            <div class="malou-text-13--bold text-malou-color-text-1">
                {{ 'store_locator.edit_modal.common.photos' | translate }}
            </div>
            <div
                class="malou-text-11--bold text-malou-color-primary"
                [matTooltip]="'store_locator.edit_modal.hints.photos_hint' | translate">
                {{ 'store_locator.edit_modal.hints.title' | translate }}
            </div>
        </div>

        @if (selectedRestaurant()) {
            <div class="flex flex-row gap-x-1">
                <div class="h-[140px] w-[140px]">
                    <app-image-uploader
                        titleClass="!malou-text-14--bold !text-malou-color-text-1"
                        wrapperClass="!h-full"
                        [media]="getMedia(0)"
                        [acceptedMimeTypes]="[MimeType.IMAGE_PNG, MimeType.IMAGE_JPEG]"
                        [restaurant]="selectedRestaurant()!"
                        [imageViewerWrapperCss]="{ width: '120px', height: '140px' }"
                        [uploadViaImagePreview]="true"
                        (mediaChange)="setMedia(0, $event)"></app-image-uploader>
                </div>
                <div class="grid grid-cols-3 grid-rows-2 gap-x-3 gap-y-1">
                    @for (uploadedMedia of uploadedMedias(); track $index) {
                        @if ($index !== 0) {
                            <div class="mr-1 h-[68px] w-[68px]">
                                <app-image-uploader
                                    titleClass="!malou-text-14--bold !text-malou-color-text-1"
                                    [media]="getMedia($index)"
                                    [acceptedMimeTypes]="[MimeType.IMAGE_PNG, MimeType.IMAGE_JPEG]"
                                    [imageViewerWrapperCss]="{ width: '68px', height: '68px' }"
                                    [restaurant]="selectedRestaurant()!"
                                    [uploadViaImagePreview]="true"
                                    (mediaChange)="setMedia($index, $event)"></app-image-uploader>
                            </div>
                        }
                    }
                </div>
            </div>
        }
    </div>
</ng-template>

<ng-template #styleFormTemplate>
    <form class="flex flex-col gap-5" [formGroup]="styleForm">
        <div class="flex flex-col gap-2 rounded-[10px] bg-white p-4" formGroupName="general">
            <div class="malou-text-16--bold text-malou-color-text-1">
                {{ 'store_locator.edit_modal.style.general' | translate }}
            </div>
            <app-store-locator-edit-page-color-selector
                [title]="'store_locator.edit_modal.style.background' | translate"
                [colorOptions]="colorOptions()"
                [control]="styleForm.get('general.backgroundColor')" />

            <app-store-locator-edit-page-color-selector
                [title]="'store_locator.edit_modal.style.title' | translate"
                [colorOptions]="colorOptions()"
                [control]="styleForm.get('general.titleColor')" />

            <app-store-locator-edit-page-color-selector
                [title]="'store_locator.edit_modal.style.text' | translate"
                [colorOptions]="colorOptions()"
                [control]="styleForm.get('general.textColor')" />
        </div>
    </form>
</ng-template>
