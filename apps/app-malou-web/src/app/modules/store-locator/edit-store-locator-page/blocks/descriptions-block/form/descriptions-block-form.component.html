<app-edit-store-locator-page-block-form-wrapper [contentFormTemplate]="contentFormTemplate" [styleFormTemplate]="styleFormTemplate" />

<ng-template #contentFormTemplate>
    <div class="flex flex-col gap-5" [formGroup]="contentForm">
        <div class="flex flex-col gap-5" formArrayName="items">
            @for (item of items.controls; track $index) {
                <div class="expansion-header malou-expansion-panel">
                    <mat-accordion>
                        <mat-expansion-panel class="!border-none" hideToggle [expanded]="false">
                            <mat-expansion-panel-header class="!pl-0" (click)="isButtonsPanelExpanded.set(!isButtonsPanelExpanded())">
                                <div class="flex w-full items-center justify-between">
                                    <div class="malou-text-15--bold text-malou-color-text-1">
                                        {{ 'store_locator.edit_modal.common.section_order' | translate: { order: $index + 1 } }}
                                    </div>
                                    <div class="flex items-center">
                                        <mat-icon
                                            class="!w-3 transition-all"
                                            color="primary"
                                            [svgIcon]="SvgIcon.CHEVRON_DOWN"
                                            [class.rotate-180]="isButtonsPanelExpanded()"></mat-icon>
                                    </div>
                                </div>
                            </mat-expansion-panel-header>

                            <ng-template matExpansionPanelContent>
                                <div class="flex flex-col gap-4 rounded-[10px] !bg-malou-color-background-light" [formGroupName]="$index">
                                    <app-input-text
                                        class="malou-text-14--bold!"
                                        formControlName="title"
                                        [defaultValue]="item.get('title')?.value"
                                        [title]="'store_locator.edit_modal.controls.title.name' | translate"
                                        [disabled]="false"
                                        [errorMessage]="
                                            item.get('title')?.errors &&
                                            (item.get('title')?.errors?.minlength || item.get('title')?.errors?.maxlength)
                                                ? ('store_locator.edit_modal.controls.title.length_error'
                                                  | translate
                                                      : {
                                                            minLength: DescriptionsBlockContentFormInputValidation.TITLE_MIN_LENGTH,
                                                            maxLength: DescriptionsBlockContentFormInputValidation.TITLE_MAX_LENGTH,
                                                        })
                                                : ''
                                        "
                                        [isEmojiPickerEnabled]="true"
                                        [placeholder]="'store_locator.edit_modal.controls.title.placeholder_text' | translate"
                                        [autocapitalize]="'none'">
                                    </app-input-text>

                                    <div class="w-fit">
                                        @if (selectedRestaurant()) {
                                            <app-image-uploader
                                                titleClass="!malou-text-14--bold !text-malou-color-text-1"
                                                [media]="getMedia($index)"
                                                [acceptedMimeTypes]="[MimeType.IMAGE_PNG, MimeType.IMAGE_JPEG]"
                                                [restaurant]="selectedRestaurant()!"
                                                [title]="'store_locator.edit_modal.controls.image_upload.name' | translate"
                                                (mediaChange)="setMedia($index, $event)"
                                                (onMediaSelected)="onMediaSelected($event, $index)"></app-image-uploader>
                                        }
                                    </div>

                                    <div class="flex flex-col gap-4" formArrayName="content">
                                        @for (content of getSectionContent($index).controls; let index2 = $index; track index2) {
                                            <div class="flex flex-col gap-2" [formGroupName]="index2">
                                                <app-input-text
                                                    class="malou-text-14--bold!"
                                                    formControlName="subtitle"
                                                    [defaultValue]="content.get('subtitle')?.value"
                                                    [title]="'store_locator.edit_modal.controls.subtitle.name' | translate"
                                                    [placeholder]="'store_locator.edit_modal.controls.subtitle.placeholder' | translate"
                                                    [disabled]="false"
                                                    [errorMessage]="
                                                        content.get('subtitle')?.errors &&
                                                        (content.get('subtitle')?.errors?.minlength ||
                                                            content.get('subtitle')?.errors?.maxlength)
                                                            ? ('store_locator.edit_modal.controls.subtitle.length_error'
                                                              | translate
                                                                  : {
                                                                        minLength:
                                                                            DescriptionsBlockContentFormInputValidation.SUBTITLE_MIN_LENGTH,
                                                                        maxLength:
                                                                            DescriptionsBlockContentFormInputValidation.SUBTITLE_MAX_LENGTH,
                                                                    })
                                                            : ''
                                                    "
                                                    [isEmojiPickerEnabled]="true"
                                                    [autocapitalize]="'none'">
                                                </app-input-text>
                                                <app-text-area
                                                    class="malou-text-14--bold!"
                                                    formControlName="text"
                                                    [defaultValue]="content.get('text')?.value"
                                                    [title]="'store_locator.edit_modal.controls.description.name' | translate"
                                                    [disabled]="false"
                                                    [errorMessage]="
                                                        content.get('text')?.errors && content.get('text')?.errors?.minlength
                                                            ? ('store_locator.edit_modal.controls.description.length_error'
                                                              | translate
                                                                  : {
                                                                        minLength:
                                                                            DescriptionsBlockContentFormInputValidation.TEXT_MIN_LENGTH,
                                                                    })
                                                            : ''
                                                    "
                                                    [rows]="5"
                                                    [isEmojiPickerEnabled]="true"
                                                    [placeholder]="'store_locator.edit_modal.controls.description.placeholder' | translate"
                                                    [autocapitalize]="'none'"></app-text-area>
                                            </div>
                                        }
                                    </div>
                                </div>
                            </ng-template>
                        </mat-expansion-panel>
                    </mat-accordion>
                </div>
                <div class="w-full border border-malou-color-background-dark p-0"></div>
            }
        </div>
    </div>
</ng-template>

<ng-template #styleFormTemplate>
    <form class="flex flex-col gap-5" [formGroup]="styleForm">
        <div class="flex flex-col gap-2 rounded-[10px] bg-white p-4" formGroupName="generalEven">
            <div class="malou-text-16--bold text-malou-color-text-1">
                {{ 'store_locator.edit_modal.common.section_order' | translate: { order: 1 } }}
            </div>
            <app-store-locator-edit-page-color-selector
                [title]="'store_locator.edit_modal.style.background' | translate"
                [colorOptions]="colorOptions()"
                [control]="styleForm.get('generalEven.backgroundColor')" />

            <app-store-locator-edit-page-color-selector
                [title]="'store_locator.edit_modal.style.text' | translate"
                [colorOptions]="colorOptions()"
                [control]="styleForm.get('generalEven.textColor')" />
        </div>
        <div class="flex flex-col gap-2 rounded-[10px] bg-white p-4" formGroupName="generalUneven">
            <div class="malou-text-16--bold text-malou-color-text-1">
                {{ 'store_locator.edit_modal.common.section_order' | translate: { order: 2 } }}
            </div>
            <app-store-locator-edit-page-color-selector
                [title]="'store_locator.edit_modal.style.background' | translate"
                [colorOptions]="colorOptions()"
                [control]="styleForm.get('generalUneven.backgroundColor')" />

            <app-store-locator-edit-page-color-selector
                [title]="'store_locator.edit_modal.style.text' | translate"
                [colorOptions]="colorOptions()"
                [control]="styleForm.get('generalUneven.textColor')" />
        </div>
    </form>
</ng-template>
