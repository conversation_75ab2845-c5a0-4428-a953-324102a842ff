<div
    class="flex h-fit flex-col"
    [ngStyle]="stylesMap()[StoreLocatorRestaurantPageElementIds.REVIEWS_WRAPPER]"
    [class.cursor-not-allowed]="shouldShowCursorNotAllowed()"
    (click)="handleShowFormBlock(StoreLocatorPageBlockType.REVIEWS)">
    <div class="mx-auto flex w-full flex-col items-center justify-center px-14 py-14">
        <h2
            class="pb-8 text-center text-4xl font-extrabold uppercase"
            [ngStyle]="stylesMap()[StoreLocatorRestaurantPageElementIds.REVIEWS_TITLE]">
            {{ title() }}
        </h2>
        <div class="no-scrollbar mx-auto flex w-full flex-nowrap justify-start overflow-x-auto">
            @for (review of reviews(); track $index) {
                <div class="m-2 min-h-[270px] w-[320px] flex-shrink-0 rounded-xl bg-white px-7 py-3 pb-7">
                    <div class="flex w-full items-center justify-between">
                        @if (getProfilePictureUrl(review)) {
                            <img
                                class="h-[35px] w-[35px] rounded-full object-cover"
                                [alt]="review.userName"
                                [src]="getProfilePictureUrl(review)" />
                        } @else {
                            <div
                                class="text-md flex h-[35px] w-[35px] items-center justify-center rounded-full text-white"
                                [ngStyle]="{ 'background-color': getProfileAvatar(review)?.color }">
                                {{ getProfileAvatar(review)?.initials || (review.userName ? review.userName[0] : '') }}
                            </div>
                        }

                        <div class="ml-2 mr-2 flex min-w-0 flex-1 flex-col items-start overflow-hidden">
                            <p class="w-full truncate text-sm font-semibold text-malou-color-text-1">{{ review.userName }}</p>
                            @if (review.publishedAt) {
                                <p class="text-xs text-gray-500">{{ review.publishedAt }}</p>
                            }
                        </div>
                        <div class="flex flex-shrink-0 rounded-md bg-gray-100 p-2">
                            <img
                                alt="Google"
                                width="24"
                                height="24"
                                style="object-fit: contain"
                                [src]="PlatformKey.GMB | platformLogoPathResolver" />
                        </div>
                    </div>
                    <div class="my-4 flex">
                        @for (media of [].constructor(review.starsCount); track $index) {
                            <mat-icon class="mx-1 text-yellow-500" [svgIcon]="SvgIcon.STAR"></mat-icon>
                        }
                    </div>
                    <p
                        class="line-clamp-8 overflow-hidden text-xs text-gray-700"
                        style="display: -webkit-box; -webkit-box-orient: vertical; line-clamp: 8; -webkit-line-clamp: 8">
                        {{ review.content }}
                    </p>
                </div>
            }
        </div>
        @if (cta()) {
            <div class="mt-8 flex justify-center">
                <a
                    class="my-3 block w-[320px] border p-4 text-center text-sm font-extralight"
                    target="_blank"
                    [attr.aria-label]="cta()?.text"
                    [ngStyle]="stylesMap()[StoreLocatorRestaurantPageElementIds.REVIEWS_CTA]"
                    [href]="cta()?.url">
                    {{ cta()?.text?.toUpperCase() }}
                </a>
            </div>
        }
    </div>
</div>
