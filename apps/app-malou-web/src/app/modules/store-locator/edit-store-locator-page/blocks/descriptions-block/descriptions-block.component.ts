import { NgStyle } from '@angular/common';
import { ChangeDetectionStrategy, Component, computed } from '@angular/core';

import { StoreLocatorPageBlockComponent } from ':modules/store-locator/edit-store-locator-page/blocks/edit-store-locator-page-block.component';
import { StoreLocatorPageBlockType } from ':modules/store-locator/edit-store-locator-page/edit-store-locator-page.interface';

@Component({
    selector: 'app-store-locator-edit-page-descriptions-block',
    templateUrl: './descriptions-block.component.html',
    styleUrls: ['./descriptions-block.component.scss'],
    imports: [NgStyle],
    changeDetection: ChangeDetectionStrategy.OnPush,
})
export class StoreLocatorPageDescriptionBlockComponent extends StoreLocatorPageBlockComponent {
    readonly descriptionsBlockStaticData = computed(() =>
        this.storeLocatorPageState()?.getBlockStaticData(StoreLocatorPageBlockType.DESCRIPTION)
    );

    readonly descriptionsBlockUpdatedData = computed(
        () => this.storeLocatorPageState()?.getBlockUpdatedData$(StoreLocatorPageBlockType.DESCRIPTION)?.()?.data
    );

    readonly items = computed(
        () =>
            this.descriptionsBlockUpdatedData()?.items.map((item) => ({
                title: item.title,
                imageUrl: item.image.url,
                imageDescription: item.image.description,
                blocks: item.blocks.map((block) => ({
                    title: block.title,
                    text: block.text,
                })),
            })) ??
            this.descriptionsBlockStaticData()?.items ??
            []
    );

    constructor() {
        super(StoreLocatorPageBlockType.DESCRIPTION);
    }
}
