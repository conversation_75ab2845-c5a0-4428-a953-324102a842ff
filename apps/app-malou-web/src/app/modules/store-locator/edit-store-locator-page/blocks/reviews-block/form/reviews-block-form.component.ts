import { ChangeDetectionStrategy, Component, computed, inject, signal, WritableSignal } from '@angular/core';
import { takeUntilDestroyed, toObservable } from '@angular/core/rxjs-interop';
import { AbstractControl, FormBuilder, FormGroup, ReactiveFormsModule, Validators } from '@angular/forms';
import { MatButtonModule } from '@angular/material/button';
import { MatExpansionModule } from '@angular/material/expansion';
import { MatIconModule } from '@angular/material/icon';
import { MatTabsModule } from '@angular/material/tabs';
import { MatTooltipModule } from '@angular/material/tooltip';
import { TranslateModule } from '@ngx-translate/core';
import { skip } from 'rxjs';

import { StoreLocatorRestaurantPageElementIds } from '@malou-io/package-utils';

import { StoreLocatorPageBlockFormComponent } from ':modules/store-locator/edit-store-locator-page/blocks/edit-store-locator-page-block-form.component';
import { PropertyType } from ':modules/store-locator/edit-store-locator-page/blocks/edit-store-locator-page-blocks.interface';
import { mapUpdateStylesConfiguration } from ':modules/store-locator/edit-store-locator-page/blocks/edit-store-locator-page.utils';
import {
    ReviewsBlockContent,
    ReviewsBlockContentForm,
    ReviewsBlockContentFormInputValidation,
    ReviewsBlockStyleData,
    ReviewsBlockStyleForm,
} from ':modules/store-locator/edit-store-locator-page/blocks/reviews-block/reviews-block.interface';
import { EditStoreLocatorPageColorSelectorComponent } from ':modules/store-locator/edit-store-locator-page/blocks/shared/color-selector/color-selector.component';
import { StoreLocatorPageBlockType } from ':modules/store-locator/edit-store-locator-page/edit-store-locator-page.interface';
import { SlideToggleComponent } from ':shared/components-v3/slide-toggle/slide-toggle.component';
import { InputTextComponent } from ':shared/components/input-text/input-text.component';
import { UrlValidator } from ':shared/validators/url.validator';

@Component({
    selector: 'app-store-locator-edit-page-reviews-block-form',
    templateUrl: './reviews-block-form.component.html',
    styleUrls: ['./reviews-block-form.component.scss'],
    imports: [
        MatTabsModule,
        TranslateModule,
        MatIconModule,
        MatTooltipModule,
        ReactiveFormsModule,
        MatExpansionModule,
        SlideToggleComponent,
        InputTextComponent,
        MatButtonModule,
        EditStoreLocatorPageColorSelectorComponent,
        StoreLocatorPageBlockFormComponent,
    ],
    changeDetection: ChangeDetectionStrategy.OnPush,
})
export class StoreLocatorPageReviewsBlockFormComponent extends StoreLocatorPageBlockFormComponent {
    private readonly _formBuilder = inject(FormBuilder);

    readonly ReviewsBlockContentFormInputValidation = ReviewsBlockContentFormInputValidation;

    contentForm: FormGroup<ReviewsBlockContentForm>;
    styleForm: FormGroup<ReviewsBlockStyleForm>;

    readonly isCtaEnabled: WritableSignal<boolean> = signal<boolean>(true);
    readonly isButtonsPanelExpanded: WritableSignal<boolean> = signal<boolean>(false);

    readonly reviewsBlockData = computed(
        () => this.storeLocatorPageState()?.getBlockUpdatedData$(StoreLocatorPageBlockType.REVIEWS)?.()?.data
    );

    readonly title = computed(() => this.reviewsBlockData()?.title ?? '');

    constructor() {
        super();
        this._initContentForm();
        this._initStyleForm();
        toObservable(this.currentEditingRestaurant)
            .pipe(skip(1), takeUntilDestroyed(this.destroyRef))
            .subscribe(() => {
                this._patchContentForm();
            });
    }

    onCtaToggle(): void {
        this.isCtaEnabled.set(!this.isCtaEnabled());
        this.contentForm.get('cta.text')?.[this.isCtaEnabled() ? 'enable' : 'disable']();
        this.contentForm.get('cta.url')?.[this.isCtaEnabled() ? 'enable' : 'disable']();
    }

    get titleControl(): AbstractControl | null {
        return this.contentForm.get('title');
    }

    get ctaTextControl(): AbstractControl | null {
        return this.contentForm.get('cta.text');
    }

    private _checkIfBlockInError(): void {
        const titleControl = this.titleControl;
        const ctaTextControl = this.contentForm.get('cta.text');
        const ctaUrlControl = this.contentForm.get('cta.url');
        if (!titleControl || !ctaTextControl || !ctaUrlControl) {
            return;
        }
        const isTitleInError = !!titleControl?.errors && titleControl.dirty;
        const isCtaTextInError = !!ctaTextControl?.errors && ctaTextControl.dirty;
        const isCtaUrlInError = !!ctaUrlControl?.errors && ctaUrlControl.dirty;
        this.editStoreLocatorPageContext.isBlockInError.set({
            blockType: StoreLocatorPageBlockType.REVIEWS,
            isError: isTitleInError || isCtaTextInError || isCtaUrlInError,
        });
    }

    private _initContentForm(): void {
        const cta = this.reviewsBlockData()?.cta;
        if (!cta) {
            this.isCtaEnabled.set(false);
        }

        this.contentForm = this._formBuilder.group({
            title: this._formBuilder.control(this.reviewsBlockData()?.title ?? '', {
                validators: [
                    Validators.required,
                    Validators.minLength(ReviewsBlockContentFormInputValidation.TITLE_MIN_LENGTH),
                    Validators.maxLength(ReviewsBlockContentFormInputValidation.TITLE_MAX_LENGTH),
                ],
                nonNullable: true,
            }),
            cta: this._formBuilder.group({
                text: this._formBuilder.control(
                    {
                        value: cta?.text ?? '',
                        disabled: !this.isCtaEnabled(),
                    },
                    {
                        validators: [
                            Validators.required,
                            Validators.minLength(ReviewsBlockContentFormInputValidation.CTA_TEXT_MIN_LENGTH),
                            Validators.maxLength(ReviewsBlockContentFormInputValidation.CTA_TEXT_MAX_LENGTH),
                        ],
                        nonNullable: true,
                    }
                ),
                url: this._formBuilder.control(
                    { value: cta?.url ?? '', disabled: !this.isCtaEnabled() },
                    {
                        validators: [
                            Validators.required,
                            Validators.minLength(ReviewsBlockContentFormInputValidation.CTA_URL_MIN_LENGTH),
                            UrlValidator(),
                        ],
                        nonNullable: true,
                    }
                ),
            }),
        });

        this.contentForm.valueChanges.pipe(skip(1), takeUntilDestroyed(this.destroyRef)).subscribe((value: ReviewsBlockContent) => {
            const reviewsBlockData = this.reviewsBlockData();
            this._checkIfBlockInError();
            if (reviewsBlockData) {
                this.storeLocatorPageState()?.updateBlock({
                    blockType: StoreLocatorPageBlockType.REVIEWS,
                    blockData: {
                        ...reviewsBlockData,
                        title: value.title,
                        cta: value.cta
                            ? {
                                  text: value.cta.text,
                                  url: value.cta.url,
                              }
                            : undefined,
                    },
                });
            }
        });
    }

    private _patchContentForm(): void {
        const cta = this.reviewsBlockData()?.cta;
        if (!cta) {
            this.isCtaEnabled.set(false);
        }

        this.contentForm.patchValue({
            title: this.reviewsBlockData()?.title,
            cta: {
                text: this.reviewsBlockData()?.cta?.text ?? '',
                url: this.reviewsBlockData()?.cta?.url ?? '',
            },
        });
        this.contentForm.get('cta.text')?.[this.isCtaEnabled() ? 'enable' : 'disable']();
        this.contentForm.get('cta.url')?.[this.isCtaEnabled() ? 'enable' : 'disable']();
        this.contentForm.markAsPristine();
        this.isCtaEnabled.set(!!cta);
    }

    private _initStyleForm(): void {
        const styleMap = this.getStyleMap([
            StoreLocatorRestaurantPageElementIds.REVIEWS_WRAPPER,
            StoreLocatorRestaurantPageElementIds.REVIEWS_TITLE,
            StoreLocatorRestaurantPageElementIds.REVIEWS_CTA,
        ]);

        const reviewsBlockWrapperStyle = styleMap[StoreLocatorRestaurantPageElementIds.REVIEWS_WRAPPER] || {};
        const reviewsBlockTitleStyle = styleMap[StoreLocatorRestaurantPageElementIds.REVIEWS_TITLE] || {};
        const reviewsBlockButtonStyle = styleMap[StoreLocatorRestaurantPageElementIds.REVIEWS_CTA] || {};

        this.styleForm = this._formBuilder.group({
            general: this._formBuilder.group({
                titleColor: this._formBuilder.control(reviewsBlockTitleStyle.color, {
                    validators: [Validators.required],
                    nonNullable: true,
                }),
                backgroundColor: this._formBuilder.control(reviewsBlockWrapperStyle.backgroundColor, {
                    validators: [Validators.required],
                    nonNullable: true,
                }),
                textColor: this._formBuilder.control(reviewsBlockWrapperStyle.color, {
                    validators: [Validators.required],
                    nonNullable: true,
                }),
            }),
            buttons: this._formBuilder.group({
                primaryBackgroundColor: this._formBuilder.control(reviewsBlockButtonStyle.backgroundColor, {
                    validators: [Validators.required],
                    nonNullable: true,
                }),
                primaryBorderColor: this._formBuilder.control(reviewsBlockButtonStyle.borderColor, {
                    validators: [Validators.required],
                    nonNullable: true,
                }),
                primaryTextColor: this._formBuilder.control(reviewsBlockButtonStyle.color, {
                    validators: [Validators.required],
                    nonNullable: true,
                }),
            }),
        });
        this.styleForm.valueChanges.pipe(takeUntilDestroyed(this.destroyRef)).subscribe((value) => {
            this._updateStyleConfiguration(value as ReviewsBlockStyleData);
        });
    }

    private _updateStyleConfiguration(value: ReviewsBlockStyleData): void {
        const organizationStyleConfiguration = this.organizationStyleConfiguration();

        if (organizationStyleConfiguration) {
            const dataToUpdate = mapUpdateStylesConfiguration(organizationStyleConfiguration, [
                {
                    elementId: StoreLocatorRestaurantPageElementIds.REVIEWS_WRAPPER,
                    data: [
                        {
                            propertyType: PropertyType.BackgroundColor,
                            value: value.general.backgroundColor,
                        },
                        {
                            propertyType: PropertyType.Color,
                            value: value.general.textColor,
                        },
                    ],
                },
                {
                    elementId: StoreLocatorRestaurantPageElementIds.REVIEWS_TITLE,
                    data: [
                        {
                            propertyType: PropertyType.Color,
                            value: value.general.titleColor,
                        },
                    ],
                },
                {
                    elementId: StoreLocatorRestaurantPageElementIds.REVIEWS_CTA,
                    data: [
                        {
                            propertyType: PropertyType.BackgroundColor,
                            value: value.buttons.primaryBackgroundColor,
                        },
                        {
                            propertyType: PropertyType.BorderColor,
                            value: value.buttons.primaryBorderColor,
                        },
                        {
                            propertyType: PropertyType.Color,
                            value: value.buttons.primaryTextColor,
                        },
                    ],
                },
            ]);
            organizationStyleConfiguration.updateStyle(dataToUpdate);
        }
    }
}
