import { ChangeDetectionStrategy, ChangeDetectorRef, Component, computed, inject, signal, WritableSignal } from '@angular/core';
import { takeUntilDestroyed, toObservable } from '@angular/core/rxjs-interop';
import { AbstractControl, FormArray, FormBuilder, FormGroup, ReactiveFormsModule, Validators } from '@angular/forms';
import { MatButtonModule } from '@angular/material/button';
import { MatExpansionModule } from '@angular/material/expansion';
import { MatIconModule } from '@angular/material/icon';
import { MatTabsModule } from '@angular/material/tabs';
import { MatTooltipModule } from '@angular/material/tooltip';
import { TranslateModule } from '@ngx-translate/core';
import { skip } from 'rxjs';

import { StoreLocatorRestaurantPageElementIds } from '@malou-io/package-utils';

import {
    CtaBlockContentForm,
    CtaBlockContentFormData,
    CtaBlockContentFormInputValidation,
    CtaBlockStyleData,
    CtaBlockStyleForm,
    CtaButtonsFormGroup,
} from ':modules/store-locator/edit-store-locator-page/blocks/cta-block/cta-block.interface';
import { StoreLocatorPageBlockFormComponent } from ':modules/store-locator/edit-store-locator-page/blocks/edit-store-locator-page-block-form.component';
import { PropertyType } from ':modules/store-locator/edit-store-locator-page/blocks/edit-store-locator-page-blocks.interface';
import { mapUpdateStylesConfiguration } from ':modules/store-locator/edit-store-locator-page/blocks/edit-store-locator-page.utils';
import { EditStoreLocatorPageColorSelectorComponent } from ':modules/store-locator/edit-store-locator-page/blocks/shared/color-selector/color-selector.component';
import { StoreLocatorPageBlockType } from ':modules/store-locator/edit-store-locator-page/edit-store-locator-page.interface';
import { InputTextComponent } from ':shared/components/input-text/input-text.component';
import { UrlValidator } from ':shared/validators/url.validator';

@Component({
    selector: 'app-store-locator-edit-page-cta-block-form',
    templateUrl: './cta-block-form.component.html',
    styleUrls: ['./cta-block-form.component.scss'],
    imports: [
        TranslateModule,
        MatTabsModule,
        MatIconModule,
        MatTooltipModule,
        MatButtonModule,
        StoreLocatorPageBlockFormComponent,
        InputTextComponent,
        ReactiveFormsModule,
        MatExpansionModule,
        EditStoreLocatorPageColorSelectorComponent,
    ],
    changeDetection: ChangeDetectionStrategy.OnPush,
})
export class StoreLocatorPageCtaBlockFormComponent extends StoreLocatorPageBlockFormComponent {
    private readonly _formBuilder = inject(FormBuilder);
    private readonly _cdr = inject(ChangeDetectorRef);

    readonly CtaBlockContentFormInputValidation = CtaBlockContentFormInputValidation;

    contentForm: FormGroup<CtaBlockContentForm>;
    styleForm: FormGroup<CtaBlockStyleForm>;

    readonly isButtonsPanelExpanded: WritableSignal<boolean> = signal<boolean>(false);

    readonly ctaBlockData = computed(
        () => this.storeLocatorPageState()?.getBlockUpdatedData$(StoreLocatorPageBlockType.CALL_TO_ACTION)?.()?.data
    );

    readonly title = computed(() => this.ctaBlockData()?.title ?? '');

    constructor() {
        super();
        toObservable(this.currentEditingRestaurant)
            .pipe(skip(1), takeUntilDestroyed(this.destroyRef))
            .subscribe(() => {
                this._patchContentForm();
            });
        this._initContentForm();
        this._initStyleForm();
    }

    get titleControl(): AbstractControl | null {
        return this.contentForm.get('title');
    }

    get ctaButtons(): FormArray<CtaButtonsFormGroup> {
        return this.contentForm.get('ctaButtons') as FormArray<CtaButtonsFormGroup>;
    }

    addCtaButton(index: number) {
        const newCtaButton = this._createCtaGroupForm();
        this.ctaButtons.insert(index + 1, newCtaButton);
        this._cdr.detectChanges();
    }

    removeCtaButton(index: number) {
        if (index > 0) {
            this.ctaButtons.removeAt(index);
        }
    }

    private _checkIfBlockInError(): void {
        const titleControl = this.titleControl;
        if (!titleControl) {
            return;
        }
        const isTitleInError = !!titleControl?.errors && titleControl.dirty;
        const isAtLeastOneCtaButtonInError = this.ctaButtons.controls.some((control) => {
            const textControl = control.get('text');
            const urlControl = control.get('url');
            return (!!textControl?.errors && textControl.dirty) || (!!urlControl?.errors && urlControl.dirty);
        });

        this.editStoreLocatorPageContext.isBlockInError.set({
            blockType: StoreLocatorPageBlockType.CALL_TO_ACTION,
            isError: isTitleInError || isAtLeastOneCtaButtonInError,
        });
    }

    private _initContentForm(): void {
        if (!this.contentForm) {
            this.contentForm = this._formBuilder.group({
                title: this._formBuilder.control(this.ctaBlockData()?.title ?? '', {
                    validators: [
                        Validators.required,
                        Validators.minLength(CtaBlockContentFormInputValidation.TITLE_MIN_LENGTH),
                        Validators.maxLength(CtaBlockContentFormInputValidation.TITLE_MAX_LENGTH),
                    ],
                    nonNullable: true,
                }),
                ctaButtons: this._formBuilder.array<CtaButtonsFormGroup>(
                    (this.ctaBlockData()?.links ?? []).map((ctaButton) =>
                        this._createCtaGroupForm(ctaButton.text ?? '', ctaButton.url ?? '')
                    )
                ),
            });

            this.contentForm.valueChanges.subscribe((value: CtaBlockContentFormData) => {
                const ctaBlockData = this.ctaBlockData();
                this._checkIfBlockInError();
                if (ctaBlockData) {
                    this.storeLocatorPageState()?.updateBlock({
                        blockType: StoreLocatorPageBlockType.CALL_TO_ACTION,
                        blockData: {
                            ...ctaBlockData,
                            title: value.title,
                            links: value.ctaButtons.map((ctaButton) => ({
                                text: ctaButton.text,
                                url: ctaButton.url,
                            })),
                        },
                    });
                }
            });
        }
    }

    private _createCtaGroupForm(value: string = '', url: string = ''): FormGroup {
        return this._formBuilder.group({
            text: this._formBuilder.control(value, {
                validators: [
                    Validators.required,
                    Validators.minLength(CtaBlockContentFormInputValidation.CTA_TEXT_MIN_LENGTH),
                    Validators.maxLength(CtaBlockContentFormInputValidation.CTA_TEXT_MAX_LENGTH),
                ],
                nonNullable: true,
            }),
            url: this._formBuilder.control(url, {
                validators: [
                    Validators.required,
                    Validators.minLength(CtaBlockContentFormInputValidation.CTA_URL_MIN_LENGTH),
                    UrlValidator(),
                ],
                nonNullable: true,
            }),
        });
    }

    private _patchContentForm(): void {
        const ctaBlockData = this.ctaBlockData();
        if (!ctaBlockData) {
            return;
        }
        this.ctaButtons.clear();

        ctaBlockData.links.forEach((ctaButton) => {
            this.ctaButtons.push(this._createCtaGroupForm(ctaButton.text, ctaButton.url));
        });
        this.contentForm.patchValue({
            title: ctaBlockData.title,
        });
        this.contentForm.markAsPristine();
    }

    private _initStyleForm(): void {
        const styleMap = this.getStyleMap([
            StoreLocatorRestaurantPageElementIds.CALL_TO_ACTIONS_TITLE,
            StoreLocatorRestaurantPageElementIds.CALL_TO_ACTIONS_WRAPPER,
            StoreLocatorRestaurantPageElementIds.CALL_TO_ACTIONS_CTA,
        ]);

        const ctaBlockWrapperStyle = styleMap[StoreLocatorRestaurantPageElementIds.CALL_TO_ACTIONS_WRAPPER] || {};
        const ctaBlockTitleStyle = styleMap[StoreLocatorRestaurantPageElementIds.CALL_TO_ACTIONS_TITLE] || {};
        const ctaBlockCtaStyle = styleMap[StoreLocatorRestaurantPageElementIds.CALL_TO_ACTIONS_CTA] || {};

        this.styleForm = this._formBuilder.group({
            general: this._formBuilder.group({
                titleColor: this._formBuilder.control(ctaBlockTitleStyle.color, {
                    validators: [Validators.required],
                    nonNullable: true,
                }),
                backgroundColor: this._formBuilder.control(ctaBlockWrapperStyle.backgroundColor, {
                    validators: [Validators.required],
                    nonNullable: true,
                }),
            }),
            buttons: this._formBuilder.group({
                textColor: this._formBuilder.control(ctaBlockCtaStyle.color, {
                    validators: [Validators.required],
                    nonNullable: true,
                }),
                backgroundColor: this._formBuilder.control(ctaBlockCtaStyle.backgroundColor, {
                    validators: [Validators.required],
                    nonNullable: true,
                }),
                borderColor: this._formBuilder.control(ctaBlockCtaStyle.borderColor, {
                    validators: [Validators.required],
                    nonNullable: true,
                }),
            }),
        });
        this.styleForm.valueChanges.subscribe((value) => {
            this._updateStyleConfiguration(value as CtaBlockStyleData);
        });
    }

    private _updateStyleConfiguration(value: CtaBlockStyleData): void {
        const organizationStyleConfiguration = this.organizationStyleConfiguration();

        if (organizationStyleConfiguration) {
            const dataToUpdate = mapUpdateStylesConfiguration(organizationStyleConfiguration, [
                {
                    elementId: StoreLocatorRestaurantPageElementIds.CALL_TO_ACTIONS_WRAPPER,
                    data: [
                        {
                            value: value.general.backgroundColor,
                            propertyType: PropertyType.BackgroundColor,
                        },
                    ],
                },
                {
                    elementId: StoreLocatorRestaurantPageElementIds.CALL_TO_ACTIONS_TITLE,
                    data: [
                        {
                            value: value.general.titleColor,
                            propertyType: PropertyType.Color,
                        },
                    ],
                },
                {
                    elementId: StoreLocatorRestaurantPageElementIds.CALL_TO_ACTIONS_CTA,
                    data: [
                        {
                            value: value.buttons.textColor,
                            propertyType: PropertyType.Color,
                        },
                        {
                            value: value.buttons.backgroundColor,
                            propertyType: PropertyType.BackgroundColor,
                        },
                        {
                            value: value.buttons.borderColor,
                            propertyType: PropertyType.BorderColor,
                        },
                    ],
                },
            ]);
            organizationStyleConfiguration.updateStyle(dataToUpdate);
        }
    }
}
