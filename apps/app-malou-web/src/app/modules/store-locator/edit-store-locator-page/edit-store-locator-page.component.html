@if (isLoading()) {
    <div class="flex h-full items-center justify-center">
        <mat-spinner diameter="60"></mat-spinner>
    </div>
} @else {
    <div class="flex h-full">
        <div class="h-full w-1/4 border-r border-malou-color-background-dark bg-malou-color-background-light px-5 py-3">
            <ng-container [ngTemplateOutlet]="editBlockFormTemplate"></ng-container>
        </div>
        <div class="flex w-3/4 flex-col">
            <div class="flex w-full items-center border-b border-malou-color-background-dark px-5 py-3">
                <div class="flex w-1/2 justify-end gap-x-1">
                    <!-- TODO: V2 to do later [@hamza] -->
                    <!-- <button class="malou-btn-icon--secondary btn-sm" mat-icon-button>
                        <mat-icon color="primary" [svgIcon]="SvgIcon.DESKTOP_DISPLAY"></mat-icon>
                    </button>
                    <button class="malou-btn-icon--secondary btn-sm" mat-icon-button>
                        <mat-icon color="primary" [svgIcon]="SvgIcon.PHONE_DISPLAY"></mat-icon>
                    </button>-->
                </div>
                <div class="flex w-1/2 items-center justify-end">
                    <button
                        class="malou-btn-flat !text-[14px] font-extrabold !text-malou-color-text-1"
                        mat-button
                        [disabled]="isLoading()"
                        [ngClass]="{ 'opacity-50': shouldDisableModal() }"
                        (click)="closeEditModal()">
                        {{ 'store_locator.edit_modal.cancel' | translate }}
                    </button>
                    <app-menu-button-v3
                        [text]="'store_locator.edit_modal.draft' | translate"
                        [size]="MenuButtonSize.LARGE"
                        [disabled]="isBlockInError() || !shouldAllowToSaveAsDraftOrPublish()"
                        [loading]="shouldDisableModal()"
                        (onMainButtonClick)="onSaveAsDraft()">
                        <button
                            class="flex min-w-[120px] !px-5"
                            mat-menu-item
                            [disabled]="shouldDisableModal() || isBlockInError() || !shouldAllowToSaveAsDraftOrPublish()"
                            (click)="onPublish()">
                            <span class="malou-text-14--regular p-2 text-malou-color-text-2">
                                @if (shouldDisableModal()) {
                                    <mat-spinner class="mr-2" diameter="20"></mat-spinner>
                                } @else {
                                    {{ 'store_locator.edit_modal.publish' | translate }}
                                }
                            </span>
                        </button>
                    </app-menu-button-v3>
                </div>
            </div>
            <div
                class="flex h-[100vh] w-full flex-col overflow-scroll px-3"
                [ngStyle]="{ fontFamily: textFontFamilyClass() }"
                [ngClass]="{ 'pointer-events-none opacity-50': shouldDisableModal() }">
                <app-store-locator-edit-page-header-block />
                <app-store-locator-edit-page-information-block (emitChangeFormBlock)="handleShowFormBlock($event)" />
                <app-store-locator-edit-page-gallery-block (emitChangeFormBlock)="handleShowFormBlock($event)" />
                <app-store-locator-edit-page-reviews-block (emitChangeFormBlock)="handleShowFormBlock($event)" />
                <app-store-locator-edit-page-cta-block (emitChangeFormBlock)="handleShowFormBlock($event)" />
                <app-store-locator-edit-page-social-networks-block (emitChangeFormBlock)="handleShowFormBlock($event)" />
                <app-store-locator-edit-page-descriptions-block (emitChangeFormBlock)="handleShowFormBlock($event)" />
                <app-store-locator-edit-page-footer-block />
            </div>
        </div>
    </div>
}

<ng-template #editBlockFormTemplate>
    @switch (selectedBlock()) {
        @case (StoreLocatorPageBlockType.INFORMATION) {
            <app-store-locator-edit-page-information-block-form />
        }
        @case (StoreLocatorPageBlockType.GALLERY) {
            <app-store-locator-edit-page-gallery-block-form />
        }
        @case (StoreLocatorPageBlockType.REVIEWS) {
            <app-store-locator-edit-page-reviews-block-form />
        }
        @case (StoreLocatorPageBlockType.CALL_TO_ACTION) {
            <app-store-locator-edit-page-cta-block-form />
        }
        @case (StoreLocatorPageBlockType.SOCIAL_NETWORKS) {
            <app-store-locator-edit-page-social-networks-block-form />
        }
        @case (StoreLocatorPageBlockType.DESCRIPTION) {
            <app-store-locator-edit-page-descriptions-block-form />
        }
    }
</ng-template>
