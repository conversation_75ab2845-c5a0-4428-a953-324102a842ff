import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, NgTemplateOutlet } from '@angular/common';
import { ChangeDetectionStrategy, Component, computed, inject, OnInit, signal, WritableSignal } from '@angular/core';
import { MatButtonModule } from '@angular/material/button';
import { MAT_DIALOG_DATA, MatDialogRef } from '@angular/material/dialog';
import { MatIconModule } from '@angular/material/icon';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';
import { Store } from '@ngrx/store';
import { TranslateModule } from '@ngx-translate/core';
import { Observable } from 'rxjs';

import { selectOwnRestaurants } from ':modules/restaurant-list/restaurant-list.reducer';
import { StoreLocatorPageCtaBlockComponent } from ':modules/store-locator/edit-store-locator-page/blocks/cta-block/cta-block.component';
import { StoreLocatorPageCtaBlockFormComponent } from ':modules/store-locator/edit-store-locator-page/blocks/cta-block/form/cta-block-form.component';
import { StoreLocatorPageDescriptionBlockComponent } from ':modules/store-locator/edit-store-locator-page/blocks/descriptions-block/descriptions-block.component';
import { StoreLocatorPageDescriptionBlockFormComponent } from ':modules/store-locator/edit-store-locator-page/blocks/descriptions-block/form/descriptions-block-form.component';
import { StoreLocatorPageFooterBlockComponent } from ':modules/store-locator/edit-store-locator-page/blocks/footer-block/footer-block.component';
import { StoreLocatorPageGalleryBlockFormComponent } from ':modules/store-locator/edit-store-locator-page/blocks/gallery-block/form/gallery-block-form.component';
import { StoreLocatorPageGalleryBlockComponent } from ':modules/store-locator/edit-store-locator-page/blocks/gallery-block/gallery-block.component';
import { StoreLocatorPageHeaderBlockComponent } from ':modules/store-locator/edit-store-locator-page/blocks/header-block/header-block.component';
import { StoreLocatorPageInformationBlockFormComponent } from ':modules/store-locator/edit-store-locator-page/blocks/information-block/form/information-block-form.component';
import { StoreLocatorPageInformationBlockComponent } from ':modules/store-locator/edit-store-locator-page/blocks/information-block/information-block.component';
import { StoreLocatorPageReviewsBlockFormComponent } from ':modules/store-locator/edit-store-locator-page/blocks/reviews-block/form/reviews-block-form.component';
import { StoreLocatorPageReviewsBlockComponent } from ':modules/store-locator/edit-store-locator-page/blocks/reviews-block/reviews-block.component';
import { StoreLocatorPageSocialNetworksBlockFormComponent } from ':modules/store-locator/edit-store-locator-page/blocks/social-networks-block/form/social-networks-block-form.component';
import { StoreLocatorPageSocialNetworksBlockComponent } from ':modules/store-locator/edit-store-locator-page/blocks/social-networks-block/social-networks-block.component';
import { EditStoreLocatorPageContext } from ':modules/store-locator/edit-store-locator-page/edit-store-locator-page.context';
import {
    EditStoreLocatorPageModalInputData,
    StoreLocatorPageBlockType,
} from ':modules/store-locator/edit-store-locator-page/edit-store-locator-page.interface';
import { StoreLocatorOrganizationStylesConfiguration } from ':modules/store-locator/edit-store-locator-page/models/store-locator-organization-styles-configuration';
import { getFontFamily, loadDynamicFont } from ':modules/store-locator/edit-store-locator-page/utils/inject-font-family';
import { MenuButtonV2Component } from ':shared/components/menu-button-v3/menu-button-v3.component';
import { MenuButtonSize } from ':shared/components/menu-button-v3/menu-button-v3.interface';
import { Restaurant } from ':shared/models';
import { SvgIcon } from ':shared/modules/svg-icon.enum';

@Component({
    selector: 'app-store-locator-edit-page',
    templateUrl: './edit-store-locator-page.component.html',
    styleUrls: ['./edit-store-locator-page.component.scss'],
    imports: [
        MatButtonModule,
        MatProgressSpinnerModule,
        NgTemplateOutlet,
        MatIconModule,
        NgStyle,
        StoreLocatorPageInformationBlockComponent,
        StoreLocatorPageInformationBlockFormComponent,
        StoreLocatorPageGalleryBlockFormComponent,
        StoreLocatorPageGalleryBlockComponent,
        StoreLocatorPageReviewsBlockFormComponent,
        StoreLocatorPageReviewsBlockComponent,
        StoreLocatorPageCtaBlockFormComponent,
        StoreLocatorPageCtaBlockComponent,
        StoreLocatorPageSocialNetworksBlockFormComponent,
        StoreLocatorPageSocialNetworksBlockComponent,
        StoreLocatorPageDescriptionBlockComponent,
        StoreLocatorPageDescriptionBlockFormComponent,
        StoreLocatorPageFooterBlockComponent,
        StoreLocatorPageHeaderBlockComponent,
        TranslateModule,
        MenuButtonV2Component,
        NgClass,
    ],
    changeDetection: ChangeDetectionStrategy.OnPush,
})
export class EditStoreLocatorPageModalComponent implements OnInit {
    private readonly _store = inject(Store);
    private readonly _editStoreLocatorPageContext = inject(EditStoreLocatorPageContext);
    private readonly _dialogRef = inject(MatDialogRef<EditStoreLocatorPageModalComponent>);

    readonly data: EditStoreLocatorPageModalInputData = inject(MAT_DIALOG_DATA);

    readonly MenuButtonSize = MenuButtonSize;
    readonly SvgIcon = SvgIcon;
    readonly StoreLocatorPageBlockType = StoreLocatorPageBlockType;

    readonly ownedRestaurant$: Observable<Restaurant[]> = this._store.select(selectOwnRestaurants);

    readonly selectedBlock: WritableSignal<StoreLocatorPageBlockType> = signal(StoreLocatorPageBlockType.INFORMATION);

    readonly isLoading: WritableSignal<boolean> = signal(true);

    readonly textFontFamilyClass = computed(() => getFontFamily('primary'));

    readonly isBlockInError = computed(() => this._editStoreLocatorPageContext.isBlockInError().isError);

    readonly shouldDisableModal = computed(() => this._editStoreLocatorPageContext.shouldDisableModal());

    readonly shouldAllowToSaveAsDraftOrPublish = computed(() => {
        const storesPagesStats = this._editStoreLocatorPageContext.storesPagesStates();
        const organizationStyleConfiguration = this._editStoreLocatorPageContext.organizationStyleConfiguration();

        const isAtLeastOneStorePageDirty = Array.from(storesPagesStats.values()).some((storePageState) => storePageState.isDirty());
        const isOrganizationStyleConfigurationDirty = organizationStyleConfiguration?.isDirty() ?? false;

        return isAtLeastOneStorePageDirty || isOrganizationStyleConfigurationDirty;
    });

    ngOnInit(): void {
        this.isLoading.set(true);
        this._editStoreLocatorPageContext.dialogRef.set(this._dialogRef);
        this._editStoreLocatorPageContext.organizationStyleConfiguration.set(
            new StoreLocatorOrganizationStylesConfiguration(this.data.organizationConfiguration.styles)
        );
        loadDynamicFont(this.data.organizationConfiguration.styles.fonts);
        this._editStoreLocatorPageContext.organizationRestaurants.set(this.data.organizationRestaurants);

        this.isLoading.set(false);
    }

    closeEditModal(): void {
        this._dialogRef.close();
    }

    handleShowFormBlock(blockType: StoreLocatorPageBlockType): void {
        this.selectedBlock.set(blockType);
    }

    onSaveAsDraft(): void {
        this._editStoreLocatorPageContext.saveAsDraft();
    }

    onPublish(): void {
        this._editStoreLocatorPageContext.publish();
    }
}
