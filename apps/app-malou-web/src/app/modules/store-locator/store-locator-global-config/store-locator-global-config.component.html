<div class="flex flex-col">
    <ng-container [ngTemplateOutlet]="editStoreLocatorPagesTemplate"></ng-container>
    <ng-container [ngTemplateOutlet]="editOrganizationConfigurationTemplate"></ng-container>
</div>

<ng-template #editStoreLocatorPagesTemplate>
    <div class="malou-card max-h-fit">
        <div class="flex flex-col gap-4">
            <div class="flex items-center justify-between">
                <div class="flex flex-col gap-1">
                    <div class="malou-text-20--semibold malou-color-text-1">{{ 'store_locator.title' | translate }}</div>
                    <div class="malou-text-10--regular malou-color-text-2 italic">
                        {{ 'store_locator.global_config.description' | translate }}
                    </div>
                </div>
                <ng-container [ngTemplateOutlet]="liveBadgeTemplate"></ng-container>
            </div>

            <div class="flex justify-between rounded-lg bg-malou-color-background-light p-6">
                <div class="flex flex-col gap-2">
                    <span class="malou-color-text-1 malou-text-14--semibold">{{
                        'store_locator.global_config.local_pages.title' | translate
                    }}</span>
                    <span class="malou-color-text-2 malou-text-12--regular italic">{{
                        'store_locator.global_config.local_pages.draft_warning' | translate
                    }}</span>
                </div>

                <app-button
                    class="malou-btn-raised--primary btn-xl !h-12.5 !min-w-fit"
                    mat-raised-button
                    [loading]="isFetchingRestaurantPages()"
                    [text]="'common.edit' | translate"
                    (buttonClick)="editStoreLocatorPage()">
                </app-button>
            </div>
        </div>
    </div>
</ng-template>

<ng-template #liveBadgeTemplate>
    @if (storeLocatorOrganizationConfiguration().isLive) {
        <div class="malou-text-12 rounded-lg bg-malou-color-background-success p-3 font-[500] text-malou-color-text-green">
            {{ 'store_locator.global_config.status.live' | translate }}
        </div>
    } @else {
        <div class="malou-text-12 rounded-lg bg-malou-color-background-pending p-3 font-[500] text-malou-color-state-warn">
            {{ 'store_locator.global_config.status.pending' | translate }}
        </div>
    }
</ng-template>

<ng-template #editOrganizationConfigurationTemplate>
    <div class="malou-card max-h-fit bg-malou-color-background-dark">
        <div class="flex flex-col gap-4">
            <div class="flex items-center justify-between">
                <div class="flex flex-col gap-1">
                    <div class="malou-text-20--semibold malou-color-text-1">
                        {{ 'store_locator.global_config.global_configuration.title' | translate }}
                    </div>
                    <div class="malou-text-10--regular malou-color-text-2 italic">
                        {{ 'store_locator.global_config.global_configuration.description' | translate }}
                    </div>
                </div>
            </div>

            <div class="malou-card group max-h-fit bg-malou-color-background-light">
                <div class="flex justify-between">
                    <div class="flex items-center gap-4">
                        <div class="flex h-8 w-8 items-center justify-center rounded-full bg-malou-color-text-white">
                            <mat-icon class="!h-4 !w-4" color="primary" [svgIcon]="SvgIcon.MAGIC_WAND"></mat-icon>
                        </div>
                        <div class="flex flex-col gap-1">
                            <span class="malou-text-14--semibold malou-color-text-1">{{
                                'store_locator.configuration.ai_assistant.title' | translate
                            }}</span>
                            <span class="malou-text-10--regular malou-color-text-2 italic">
                                {{ 'store_locator.configuration.ai_assistant.description' | translate }}
                            </span>
                        </div>
                    </div>
                    <div class="invisible flex group-hover:visible md:visible">
                        <button class="malou-btn-icon" mat-icon-button (click)="openEditAiSettingsModal()">
                            <mat-icon color="primary" [svgIcon]="SvgIcon.EDIT"></mat-icon>
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</ng-template>
