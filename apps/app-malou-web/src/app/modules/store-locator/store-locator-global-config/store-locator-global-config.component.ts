import { NgTemplateOutlet } from '@angular/common';
import { Component, computed, DestroyRef, inject, signal, WritableSignal } from '@angular/core';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { TranslateModule, TranslateService } from '@ngx-translate/core';
import { combineLatest, filter, of, switchMap, throwError } from 'rxjs';

import { isNotNil } from '@malou-io/package-utils';

import { ToastService } from ':core/services/toast.service';
import { EditAiSettingsModalComponent } from ':modules/store-locator/edit-ai-settings-modal/edit-ai-settings-modal.component';
import { EditAiSettingsModalInputData } from ':modules/store-locator/edit-ai-settings-modal/edit-ai-settings-modal.interface';
import { EditStoreLocatorPageModalComponent } from ':modules/store-locator/edit-store-locator-page/edit-store-locator-page.component';
import { EditStoreLocatorPageContext } from ':modules/store-locator/edit-store-locator-page/edit-store-locator-page.context';
import { EditStoreLocatorPageModalInputData } from ':modules/store-locator/edit-store-locator-page/edit-store-locator-page.interface';
import { StoreLocatorOrganizationConfiguration } from ':modules/store-locator/models/store-locator-organization-config';
import { StoreLocatorContext } from ':modules/store-locator/store-locator.context';
import { ButtonComponent } from ':shared/components/button/button.component';
import { mapRestaurantToStoreLocatorOrganizationRestaurant } from ':shared/models';
import { SvgIcon } from ':shared/modules/svg-icon.enum';
import { CustomDialogService, DialogScreenSize } from ':shared/services/custom-dialog.service';

@Component({
    selector: 'app-store-locator-global-config',
    imports: [MatButtonModule, TranslateModule, NgTemplateOutlet, MatIconModule, ButtonComponent],
    templateUrl: './store-locator-global-config.component.html',
    styleUrl: './store-locator-global-config.component.scss',
})
export class StoreLocatorGlobalConfigComponent {
    private readonly _storeLocatorContext = inject(StoreLocatorContext);
    private readonly _customDialogService = inject(CustomDialogService);
    private readonly _toastService = inject(ToastService);
    private readonly _translate = inject(TranslateService);
    private readonly _editStoreLocatorPageContext = inject(EditStoreLocatorPageContext);
    private readonly _destroyRef = inject(DestroyRef);

    readonly SvgIcon = SvgIcon;

    readonly storeLocatorOrganizationConfiguration = computed(() => this._storeLocatorContext.storeLocatorOrganizationConfiguration());

    readonly isFetchingRestaurantPages: WritableSignal<boolean> = signal(false);

    editStoreLocatorPage(): void {
        this._editStoreLocatorPageContext.selectedRestaurant$
            .pipe(
                filter(isNotNil),
                switchMap((selectedRestaurant) => {
                    this.isFetchingRestaurantPages.set(true);

                    if (!selectedRestaurant.organizationId) {
                        return throwError(() => new Error('Selected restaurant does not have an organization ID'));
                    }
                    return combineLatest([
                        of(selectedRestaurant),
                        this._editStoreLocatorPageContext.getRestaurantPages(selectedRestaurant.organizationId),
                    ]);
                }),
                takeUntilDestroyed(this._destroyRef)
            )
            .subscribe({
                next: ([selectedRestaurant, stores]) => {
                    if (!stores || stores.length === 0 || !selectedRestaurant.organization) {
                        this._toastService.openErrorToast(this._translate.instant('store_locator.edit_modal.init.error_no_stores'));
                        this.isFetchingRestaurantPages.set(false);
                        return;
                    }

                    this._editStoreLocatorPageContext.currentEditingRestaurant.set(
                        mapRestaurantToStoreLocatorOrganizationRestaurant(selectedRestaurant)
                    );

                    this._editStoreLocatorPageContext.organizationData.set({
                        id: selectedRestaurant.organization._id,
                        name: selectedRestaurant.organization.name,
                    });

                    const storesPagesMap = new Map(stores.map((store) => [store.id, store]));
                    this._editStoreLocatorPageContext.storesPagesStates.set(storesPagesMap);

                    const selectedRestaurantStorePage = storesPagesMap.get(selectedRestaurant.id);
                    if (selectedRestaurantStorePage) {
                        this._editStoreLocatorPageContext.selectedRestaurantStorePageState.set(selectedRestaurantStorePage);
                    }

                    const storeLocatorOrganizationConfiguration: StoreLocatorOrganizationConfiguration | null =
                        this._storeLocatorContext.storeLocatorOrganizationConfiguration();

                    if (!storeLocatorOrganizationConfiguration) {
                        return;
                    }

                    const data: EditStoreLocatorPageModalInputData = {
                        organizationConfiguration: storeLocatorOrganizationConfiguration,
                        organizationRestaurants: this._storeLocatorContext.storeLocatorOrganizationRestaurants(),
                    };

                    this.isFetchingRestaurantPages.set(false);

                    this._customDialogService
                        .open<EditStoreLocatorPageModalComponent, EditStoreLocatorPageModalInputData>(
                            EditStoreLocatorPageModalComponent,
                            {
                                width: '100%',
                                height: '100%',
                                panelClass: 'malou-dialog-panel--without-border-radius',
                                disableClose: true,
                                data,
                            },
                            { animateScreenSize: DialogScreenSize.ALL }
                        )
                        .afterClosed()
                        .subscribe();
                },
                error: (error) => {
                    console.error('Error init:', error);
                    this._toastService.openErrorToast(this._translate.instant('store_locator.edit_modal.init.error'));
                    this.isFetchingRestaurantPages.set(false);
                },
            });
    }

    openEditAiSettingsModal(): void {
        const storeLocatorOrganizationConfiguration: StoreLocatorOrganizationConfiguration | null =
            this._storeLocatorContext.storeLocatorOrganizationConfiguration();

        if (!storeLocatorOrganizationConfiguration) {
            return;
        }

        const data: EditAiSettingsModalInputData = {
            organizationId: storeLocatorOrganizationConfiguration.organizationId,
            aiSettings: storeLocatorOrganizationConfiguration.aiSettings,
            organizationRestaurants: this._storeLocatorContext.storeLocatorOrganizationRestaurants(),
            organizationRestaurantKeywords: this._storeLocatorContext.organizationRestaurantKeywords(),
        };

        this._customDialogService
            .open<EditAiSettingsModalComponent, EditAiSettingsModalInputData>(EditAiSettingsModalComponent, {
                height: 'unset',
                maxHeight: '90vh',
                width: '90vw',
                maxWidth: '80vw',
                data,
            })
            .afterClosed()
            .subscribe({
                next: (result: StoreLocatorOrganizationConfiguration | undefined) => {
                    if (result) {
                        this._storeLocatorContext.updateStoreLocatorOrganizationConfiguration(result);
                        this._toastService.openSuccessToast(
                            this._translate.instant('store_locator.edit_ai_settings_modal.success_message')
                        );
                    }
                },
                error: (error) => {
                    console.error('Error opening EditAiSettingsModal:', error);
                },
            });
    }
}
