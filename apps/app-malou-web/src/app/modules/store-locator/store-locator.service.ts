/* eslint-disable @typescript-eslint/no-unused-vars */
import { HttpClient } from '@angular/common/http';
import { inject, Injectable } from '@angular/core';
import { map, Observable } from 'rxjs';

import {
    GetStoreLocatorPagesDto,
    StartStoreLocatorPagesGenerationParamsDto,
    StartStoreLocatorPagesGenerationResponseDto,
    StoreLocatorOrganizationConfigurationResponseDto,
    SuccessResponse,
    UpdateOrganizationConfigurationAiSettingsBodyDto,
    UpdateOrganizationConfigurationStorePagesBodyDto,
    UpdateStoreLocatorStorePagesBodyDto,
    WatchStoreLocatorPagesGenerationParamsDto,
    WatchStoreLocatorPagesGenerationResponseDto,
} from '@malou-io/package-dto';
import { ApiResultV2 } from '@malou-io/package-utils';

import { environment } from ':environments/environment';
import { StoreLocatorOrganizationConfiguration } from ':modules/store-locator/models/store-locator-organization-config';

export interface RoiInsightsCreationState {
    wasLastResultSeen: boolean;
    creationStartDate: Date | null;
    creationEstimatedTime: number;
}

@Injectable({
    providedIn: 'root',
})
export class StoreLocatorService {
    readonly API_BASE_URL = `${environment.APP_MALOU_API_URL}/api/v1/store-locator`;

    private readonly _http = inject(HttpClient);

    getPages(organizationId: string): Observable<ApiResultV2<GetStoreLocatorPagesDto>> {
        return this._http.get<ApiResultV2<GetStoreLocatorPagesDto>>(`${this.API_BASE_URL}/${organizationId}/all-pages`);
    }

    getOrganizationConfiguration(organizationId: string): Observable<StoreLocatorOrganizationConfiguration> {
        return this._http
            .get<
                ApiResultV2<StoreLocatorOrganizationConfigurationResponseDto>
            >(`${this.API_BASE_URL}/${organizationId}/organization-configuration`)
            .pipe(map((data) => StoreLocatorOrganizationConfiguration.fromDto(data.data)));
    }

    updateOrganizationConfigurationAiSettings(
        organizationId: string,
        params: UpdateOrganizationConfigurationAiSettingsBodyDto
    ): Observable<StoreLocatorOrganizationConfiguration> {
        return this._http
            .put<
                ApiResultV2<StoreLocatorOrganizationConfigurationResponseDto>
            >(`${this.API_BASE_URL}/${organizationId}/organization-configuration/ai-settings`, params)
            .pipe(map((data) => StoreLocatorOrganizationConfiguration.fromDto(data.data)));
    }

    updateStoreLocatorStorePages(organizationId: string, pages: UpdateStoreLocatorStorePagesBodyDto): Observable<boolean> {
        return this._http
            .put<ApiResultV2<SuccessResponse>>(`${this.API_BASE_URL}/${organizationId}/all-pages`, pages)
            .pipe(map((result) => result.data.success));
    }

    startStoreLocatorPagesGeneration(
        params: StartStoreLocatorPagesGenerationParamsDto
    ): Observable<StartStoreLocatorPagesGenerationResponseDto> {
        const { organizationId } = params;
        return this._http
            .get<ApiResultV2<StartStoreLocatorPagesGenerationResponseDto>>(`${this.API_BASE_URL}/${organizationId}/generate/start`)
            .pipe(map((result) => result.data));
    }

    watchStoreLocatorPagesGeneration(
        params: WatchStoreLocatorPagesGenerationParamsDto
    ): Observable<WatchStoreLocatorPagesGenerationResponseDto> {
        const { organizationId } = params;
        return this._http
            .get<ApiResultV2<WatchStoreLocatorPagesGenerationResponseDto>>(`${this.API_BASE_URL}/${organizationId}/generate/watch`)
            .pipe(map((result) => result.data));
    }

    updateOrganizationConfigurationStorePages(
        organizationId: string,
        storePagesStyle: UpdateOrganizationConfigurationStorePagesBodyDto
    ): Observable<boolean> {
        return this._http
            .put<
                ApiResultV2<SuccessResponse>
            >(`${this.API_BASE_URL}/${organizationId}/organization-configuration/store-pages`, storePagesStyle)
            .pipe(map((result) => result.data.success));
    }
}
