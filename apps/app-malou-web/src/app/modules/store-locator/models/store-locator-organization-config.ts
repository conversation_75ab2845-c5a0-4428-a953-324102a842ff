import { StoreLocatorOrganizationConfigurationResponseDto } from '@malou-io/package-dto';
import {
    EntityConstructor,
    PlatformKey,
    StoreLocatorAiSettingsLanguageStyle,
    StoreLocatorRestaurantPageElementIds,
} from '@malou-io/package-utils';

type StoreLocatorOrganizationConfigurationProps = EntityConstructor<StoreLocatorOrganizationConfiguration> & {
    id: string;
};

export class StoreLocatorOrganizationConfiguration {
    id: string;
    organizationId: string;
    cloudfrontDistributionId: string;
    organization?: {
        id: string;
        name: string;
    };
    baseUrl: string;
    isLive: boolean;
    styles: {
        fonts: Array<{
            class: string;
            src: string;
            weight?: string;
            style?: string;
        }>;
        colors: Array<{
            class: string;
            value: string;
        }>;
        pages: {
            store: Record<string, string[]>;
            map: Record<string, string[]>;
            mapDraft: Record<string, string[]>;
            storeDraft: Record<string, string[]>;
        };
    };
    plugins?: {
        googleAnalytics?: {
            trackingId: string;
        };
    };
    aiSettings: {
        tone: string[];
        languageStyle: StoreLocatorAiSettingsLanguageStyle;
        attributeIds: string[];
        restaurantKeywordIds: string[];
        specialAttributes: Array<{
            restaurantId: string;
            text: string;
        }>;
        attributes: Array<{
            id: string;
            attributeId: string;
            platformKey: PlatformKey;
            attributeName: {
                fr: string;
                en?: string;
                es?: string;
                it?: string;
            };
        }>;
        keywords: {
            restaurantKeywordId: string;
            text: string;
            restaurantId: string;
            keywordId: string;
        }[];
    };

    constructor(props: StoreLocatorOrganizationConfigurationProps) {
        this.id = props.id;
        this.organizationId = props.organizationId;
        // TODO: replace with actual organization data when available
        this.organization = {
            id: props.organizationId,
            name: 'Default Organization Name',
        };
        this.cloudfrontDistributionId = props.cloudfrontDistributionId;
        this.baseUrl = props.baseUrl;
        this.isLive = props.isLive;
        this.styles = props.styles;
        this.plugins = props.plugins;
        this.aiSettings = props.aiSettings;
    }

    static fromDto(dto: StoreLocatorOrganizationConfigurationResponseDto): StoreLocatorOrganizationConfiguration {
        return new StoreLocatorOrganizationConfiguration({
            id: dto.id,
            organizationId: dto.organizationId,
            cloudfrontDistributionId: dto.cloudfrontDistributionId,
            baseUrl: dto.baseUrl,
            isLive: dto.isLive,
            styles: dto.styles,
            plugins: dto.plugins,
            aiSettings: dto.aiSettings,
        });
    }

    static factory(): StoreLocatorOrganizationConfiguration {
        return new StoreLocatorOrganizationConfiguration({
            id: '789b0c1d-2e3f-4a5b-6c7d-8e9f0a1b2c3d',
            organizationId: '12345678-1234-1234-1234-123456789012',
            organization: {
                id: '12345678-1234-1234-1234-123456789012',
                name: 'Example Organization',
            },
            cloudfrontDistributionId: 'GH1234567890',
            baseUrl: 'https://example.com',
            isLive: false,
            styles: {
                fonts: [],
                colors: [],
                pages: {
                    store: {},
                    map: {},
                    storeDraft: {},
                    mapDraft: {},
                },
            },
            plugins: undefined,
            aiSettings: {
                tone: ['inspiring', 'friendly', 'custom tone 1', 'custom tone 2'],
                languageStyle: StoreLocatorAiSettingsLanguageStyle.FORMAL,
                attributeIds: ['attribute-id-1', 'attribute-id-2'],
                restaurantKeywordIds: ['keyword1-rk-id', 'keyword11-rk-id'],
                specialAttributes: [],
                attributes: [
                    {
                        id: 'db-attribute1-id',
                        attributeId: 'attribute-id-1',
                        platformKey: PlatformKey.GMB,
                        attributeName: {
                            fr: 'Attribute 1',
                            en: 'Attribute 1 EN',
                            es: 'Attribute 1 ES',
                            it: 'Attribute 1 IT',
                        },
                    },
                    {
                        id: 'db-attribute2-id',
                        attributeId: 'attribute-id-2',
                        platformKey: PlatformKey.GMB,
                        attributeName: {
                            fr: 'Attribute 2',
                            en: 'Attribute 2 EN',
                            es: 'Attribute 2 ES',
                            it: 'Attribute 2 IT',
                        },
                    },
                ],
                keywords: [
                    {
                        restaurantKeywordId: 'keyword1-rk-id',
                        text: 'Keyword1',
                        restaurantId: 'keyword1-r-id',
                        keywordId: 'keyword1-id',
                    },
                    {
                        restaurantKeywordId: 'keyword11-rk-id',
                        text: 'Keyword11',
                        restaurantId: 'keyword11-r-id',
                        keywordId: 'keyword11-id',
                    },
                ],
            },
        });
    }

    isAiSettingsConfigured(): boolean {
        return (
            this.aiSettings.tone.length > 0 &&
            this.aiSettings.restaurantKeywordIds.length > 0 &&
            this.aiSettings.specialAttributes.length > 0
        );
    }

    isStylesConfigured(): boolean {
        // TODO: complete the logic to determine if the styles are fully configured
        return true;
    }

    isConfigured(): boolean {
        return this.isAiSettingsConfigured() && this.isStylesConfigured();
    }

    updateStyles(storePageStyles: Partial<Record<StoreLocatorRestaurantPageElementIds, string[]>>): void {
        this.styles.pages.storeDraft = {
            ...this.styles.pages.storeDraft,
            ...storePageStyles,
        };
    }
}
