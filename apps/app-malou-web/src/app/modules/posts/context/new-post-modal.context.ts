import { inject, Injectable, signal } from '@angular/core';
import { toSignal } from '@angular/core/rxjs-interop';
import { UntypedFormBuilder, Validators } from '@angular/forms';
import { TranslateService } from '@ngx-translate/core';
import { shuffle } from 'lodash';
import { DateTime } from 'luxon';

import {
    CallToActionType,
    getSeoPlatformKeysWithPost,
    isLangInApplicationLanguages,
    PostPublicationStatus,
    roundUpToTheNext15MinuteInterval,
    SeoPostTopic,
} from '@malou-io/package-utils';

import { RestaurantsService } from ':core/services/restaurants.service';
import { LocalStorage } from ':core/storage/local-storage';
import { getTimeStringFromDate, isControlValueInTimeFormat, IsUrl } from ':shared/helpers';
import { Keyword, Media, Post, Restaurant } from ':shared/models';

import { PostDateStatus, PostForm } from '../new-post-modal/types';
import { CrossCallToActionValidator, EventDatesValidator, isNotNull } from '../new-post-modal/utils';

@Injectable({
    providedIn: 'any',
})
export class NewPostModalContext {
    private readonly _restaurantsService = inject(RestaurantsService);
    private readonly _fb = inject(UntypedFormBuilder);
    private readonly _translate = inject(TranslateService);

    postForm: PostForm;
    readonly postLang = signal<string | null>(null);
    readonly postMedias = signal<Media[]>([]);
    readonly currentPost = signal<Post | null>(null);
    readonly currentRestaurant = toSignal<Restaurant | null>(this._restaurantsService.restaurantSelected$);
    readonly isSmallScreen = signal(false);
    get postTopic(): { type: SeoPostTopic; text: string } | null {
        return this.postForm?.get('post.postTopic')?.value ?? null;
    }
    onChangePostLang(lang: string): void {
        this.postForm.get('post.language')?.setValue(lang);
        this.postLang.set(lang);
    }

    removeMedia(): void {
        this.postForm.get('post.attachments')?.patchValue([]);
        this.postMedias.set([]);
    }

    setMedias(medias: Media[]): void {
        this.postForm.get('post.attachments')?.patchValue(medias);
        this.postMedias.set(medias);
    }

    setAttachmentsName(name: string): void {
        this.postForm.get('post.attachmentsName')?.patchValue(name);
    }

    initPostForm(params: {
        restaurant: Restaurant;
        restaurantKeywords: Keyword[];
        postTopics: { type: SeoPostTopic; text: string }[];
        postCaptionTextLimit: number;
    }): void {
        const currentPost = this.currentPost();
        if (!currentPost) {
            return;
        }

        const { restaurant, restaurantKeywords, postTopics, postCaptionTextLimit } = params;
        const cleanRestaurantName = restaurant.name?.normalize('NFD').trim().toLowerCase();

        const eventStartDate = currentPost.event?.startDate;
        const eventEndDate = currentPost.event?.endDate;
        const publicationDate = this._computePostPublicationDate(currentPost.plannedPublicationDate);

        this.postForm = this._fb.group({
            post: this._fb.group({
                text: [currentPost.text ?? '', [Validators.required, Validators.maxLength(postCaptionTextLimit)]],
                language: [currentPost.language || LocalStorage.getLang(), Validators.required],
                attachments: [currentPost.attachments?.filter((v) => !!v) || []],
                attachmentsName: [
                    currentPost.attachmentsName ||
                        shuffle(restaurantKeywords.filter((k) => k.selected) || [])
                            .slice(0, 3)
                            .map((k) => k.text)
                            .concat(cleanRestaurantName)
                            .join(' - ') ||
                        cleanRestaurantName,
                ],
                plannedPublicationDate: [publicationDate],
                postTopic: [currentPost.postTopic ? postTopics.find((topic) => topic.type === currentPost.postTopic) : postTopics[0]],
                event: this._fb.group(
                    {
                        title: [currentPost.event?.title ?? null],
                        startDate: [eventStartDate ? new Date(eventStartDate) : null],
                        endDate: [eventEndDate ? new Date(eventEndDate) : null],
                    },
                    { validators: EventDatesValidator }
                ),
                offer: this._fb.group({
                    couponCode: [currentPost.offer?.couponCode ?? null],
                    onlineUrl: [currentPost.offer?.onlineUrl ?? null, [IsUrl()]],
                    termsConditions: [currentPost.offer?.termsConditions ?? null],
                }),
                callToAction: this._fb.group(
                    {
                        actionType: [null],
                        url: [currentPost.callToAction?.url ?? null, [IsUrl(), isNotNull]],
                    },
                    { validators: CrossCallToActionValidator }
                ),
            }),
            keys: this._fb.array(getSeoPlatformKeysWithPost()),
            date: this._fb.group({
                postDateStatus: [this.getPostDateStatus(currentPost)],
                postDate: [publicationDate],
                postTime: [getTimeStringFromDate(publicationDate), isControlValueInTimeFormat(this._translate)],
            }),
        });
    }

    getPostDateStatus(post: Post): PostDateStatus {
        switch (post.published) {
            case PostPublicationStatus.PUBLISHED:
            case PostPublicationStatus.ERROR:
            case PostPublicationStatus.REJECTED:
                return PostDateStatus.NOW;
            case PostPublicationStatus.PENDING:
                return PostDateStatus.LATER;
            case PostPublicationStatus.DRAFT:
                if (this._isPostEmpty()) {
                    return PostDateStatus.LATER;
                } else {
                    return PostDateStatus.DRAFT;
                }
            default:
                return PostDateStatus.DRAFT;
        }
    }

    mapFormValueToPostValue(field: string, value: any): any {
        switch (field) {
            case 'postTopic':
                return (value as { type: SeoPostTopic; text: string }).type;
            case 'callToAction.actionType':
                const callToActionValueType = value as { type?: CallToActionType; text: string };
                if (!callToActionValueType?.type || callToActionValueType?.type === CallToActionType.NONE) {
                    return CallToActionType.NONE;
                }
                return callToActionValueType.type;
            default:
                return value;
        }
    }

    mapFormToPost(formData: PostForm): Post {
        const language = formData.get('post.language')?.value;
        return new Post({
            text: this._formatPostTextBeforeSend(formData.get('post.text')?.value),
            language: isLangInApplicationLanguages(language ?? '') ? language : this.currentPost()?.language,
            plannedPublicationDate: formData.get('date.postDate')?.value ?? undefined,
            attachments: formData.get('post.attachments')?.value,
            attachmentsName: formData.get('post.attachmentsName')?.value ?? undefined,
            callToAction: formData.get('post.callToAction.actionType')?.value
                ? {
                      actionType: this.mapFormValueToPostValue(
                          'callToAction.actionType',
                          formData.get('post.callToAction.actionType')?.value
                      ),
                      url: formData.get('post.callToAction.url')?.value ?? '',
                  }
                : null,
            postTopic: this.mapFormValueToPostValue('postTopic', formData.get('post.postTopic')?.value),
            event: {
                title: formData.get('post.event.title')?.value ?? '',
                startDate: formData.get('post.event.startDate')?.value ?? new Date(),
                endDate: formData.get('post.event.endDate')?.value ?? DateTime.now().plus({ days: 1 }).toJSDate(),
            },
            offer: {
                couponCode: formData.get('post.offer.couponCode')?.value ?? '',
                onlineUrl: (formData.get('post.offer.onlineUrl')?.valid && formData.get('post.offer.onlineUrl')?.value) || '',
                termsConditions: formData.get('post.offer.termsConditions')?.value ?? '',
            },
        });
    }

    private _formatPostTextBeforeSend(postText: string | undefined): string | undefined {
        if (!postText) {
            return undefined;
        }
        let result = postText;
        const replacementCharacterRegex = /�/g;
        result = result.replace(replacementCharacterRegex, '');
        return result;
    }

    private _computePostPublicationDate(plannedPublicationDate: Date | null): Date {
        const IN_ONE_HOUR = DateTime.now().plus({ hours: 1 }).toJSDate();
        const IN_ONE_HOUR_ROUNDED_UP = roundUpToTheNext15MinuteInterval(IN_ONE_HOUR);
        if (!plannedPublicationDate) {
            return IN_ONE_HOUR_ROUNDED_UP;
        }
        const isPassed = Number(plannedPublicationDate) - Number(new Date()) < 0;
        if (!isPassed) {
            return plannedPublicationDate;
        }
        return IN_ONE_HOUR_ROUNDED_UP;
    }

    private _isPostEmpty(): boolean {
        const post = this.currentPost()!;
        const hasAttachments = (post.attachments?.length ?? 0) > 0;
        const hasText = post.text?.length > 0;
        const isDraft = post.published === PostPublicationStatus.DRAFT;
        const hasFeedback = !!post.feedbackId;
        return !hasAttachments && !hasText && isDraft && !hasFeedback;
    }
}
