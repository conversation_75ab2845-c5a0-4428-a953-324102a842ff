import { inject, Injectable } from '@angular/core';
import { Observable } from 'rxjs';
import { map } from 'rxjs/operators';

import { PlatformKey } from '@malou-io/package-utils';

import { PlatformsService } from ':core/services/platforms.service';
import { RestaurantsService } from ':core/services/restaurants.service';

type AuthorizedPlatformKeys =
    | PlatformKey.TRIPADVISOR
    | PlatformKey.FOURSQUARE
    | PlatformKey.YELP
    | PlatformKey.LAFOURCHETTE
    | PlatformKey.OPENTABLE
    | PlatformKey.RESY
    | PlatformKey.SEVENROOMS;

@Injectable({
    providedIn: 'root',
})
export class GetFallbackUrlService {
    private readonly _platformsService = inject(PlatformsService);
    private readonly _restaurantsService = inject(RestaurantsService);

    execute(platformKey: AuthorizedPlatformKeys): Observable<string> {
        const restaurantId = this._restaurantsService.currentRestaurant._id;
        return this._platformsService.getFallbackUrl(platformKey, restaurantId).pipe(map((res) => res.data));
    }
}
