@let progress = loadingProgress();

<div class="relative w-full select-none" #slideBar>
    <div class="flex h-[75px] w-full overflow-hidden rounded-md" (click)="onTimelineClick($event)">
        @if (progress === undefined) {
            @if (this.timelinePreviewUrls()) {
                @for (url of this.timelinePreviewUrls(); track url) {
                    <img class="grow object-cover" draggable="false" [src]="url" />
                }
            }
        } @else {
            <app-loader-progress class="grow self-center" [progress]="loadingProgressPercentage" [showPercentage]="false" />
        }
    </div>

    <div
        class="absolute top-[-5px] flex h-[85px] w-[47px] cursor-grab items-center justify-center overflow-hidden rounded-md border-[1px] border-solid border-malou-color-primary bg-malou-color-background-light"
        [ngClass]="{ invisible: progress !== undefined || position() === undefined }"
        (mousedown)="sliderOnMouseDown($event)"
        #slider>
        @if (sliderThumbnailUrl()) {
            <img class="h-full w-full object-cover" draggable="false" [src]="sliderThumbnailUrl()" />
        } @else {
            @if (previousSliderThumbnailUrl()) {
                <img class="h-full w-full object-cover" draggable="false" [src]="previousSliderThumbnailUrl()" />
            }
            <app-malou-spinner class="absolute left-1/2 top-1/2 -translate-x-1/2 -translate-y-1/2" size="small" />
        }
    </div>

    @if (progress === undefined) {
        <div
            class="absolute right-2 top-2 flex h-[24px] w-[24px] items-center justify-center rounded-md bg-white leading-none"
            [ngClass]="{ 'cursor-pointer': !isReadonly() }"
            [matTooltip]="'Remplacer la vidéo'"
            (click)="!isReadonly() && onMediaDeleted.emit()">
            <mat-icon class="!h-[16px] !w-[16px] text-malou-color-chart-pink--accent" [svgIcon]="SvgIcon.TRASH"></mat-icon>
        </div>
    }
</div>
