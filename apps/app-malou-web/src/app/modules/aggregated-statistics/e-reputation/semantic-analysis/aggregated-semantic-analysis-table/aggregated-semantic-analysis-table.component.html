<div
    class="malou-simple-card mb-4 flex w-full break-inside-avoid flex-col gap-3 px-6 py-3 pb-6 md:flex-col md:px-2"
    [ngClass]="{
        'max-h-[470px]': !isPdfDownload(),
    }">
    <div class="flex flex-col gap-1">
        <div class="malou-text-section-title malou-color-text-1">
            {{ 'aggregated_statistics.e_reputation.reviews_analysis.positive_sentiment_percentage' | translate }}
        </div>
    </div>
    <div class="justify-content-between flex overflow-auto lg:flex-col-reverse">
        <div class="min-w-0 flex-1"><ng-container [ngTemplateOutlet]="tableTemplate"></ng-container></div>
    </div>
</div>

<ng-template #tableTemplate>
    <mat-table
        class="malou-mat-table w-full"
        matSort
        multiTemplateDataRows
        [dataSource]="dataSource()"
        [matSortActive]="sort.active"
        [matSortDirection]="sort.direction"
        #table="matTable">
        <ng-container [matColumnDef]="SemanticAnalysisColumns.RESTAURANT">
            <mat-header-cell *matHeaderCellDef class="!bg-white" mat-sort-header>
                {{ 'aggregated_statistics.e_reputation.reviews_analysis.positive_sentiments_table.restaurant' | translate }}
            </mat-header-cell>
            <mat-cell *matCellDef="let element; table: table">
                <div class="hidden lg:flex"></div>
                <ng-container
                    [ngTemplateOutlet]="restaurantCellTemplate"
                    [ngTemplateOutletContext]="{
                        cellData: {
                            restaurantId: element.restaurantId,
                            restaurantName: element.restaurantName,
                            restaurantLogo: element.restaurantLogo,
                        },
                    }">
                </ng-container>
            </mat-cell>
        </ng-container>

        <ng-container [matColumnDef]="SemanticAnalysisColumns.SENTIMENT_NUMBER">
            <mat-header-cell *matHeaderCellDef class="flex justify-center !bg-white" mat-sort-header>
                {{ 'aggregated_statistics.e_reputation.reviews_analysis.positive_sentiments_table.total_sentiments' | translate }}
            </mat-header-cell>
            <mat-cell *matCellDef="let element; table: table" class="flex items-center justify-center">
                <div class="hidden lg:flex">
                    <span>
                        {{ 'aggregated_statistics.e_reputation.reviews_analysis.positive_sentiments_table.total_sentiments' | translate }}
                    </span>
                </div>
                <ng-container
                    [ngTemplateOutlet]="valueWithEvolutionCellTemplate"
                    [ngTemplateOutletContext]="{
                        cellData: element.sentimentNumber,
                        unit: '',
                    }">
                </ng-container>
            </mat-cell>
        </ng-container>

        <ng-container [matColumnDef]="SemanticAnalysisColumns.SENTIMENT">
            <mat-header-cell *matHeaderCellDef class="flex justify-center !bg-white" mat-sort-header>
                {{ 'aggregated_statistics.e_reputation.reviews_analysis.positive_sentiments_table.global' | translate }}
            </mat-header-cell>
            <mat-cell *matCellDef="let element; table: table" class="flex items-center justify-center">
                <div class="hidden lg:flex">
                    <span>
                        {{ 'aggregated_statistics.e_reputation.reviews_analysis.positive_sentiments_table.global' | translate }}
                    </span>
                </div>
                <ng-container
                    [ngTemplateOutlet]="valueWithEvolutionCellTemplate"
                    [ngTemplateOutletContext]="{
                        cellData: element.positiveSentimentPercentage,
                        unit: '%',
                        displayAsChip: isNumber(element.positiveSentimentPercentage),
                    }">
                </ng-container>
            </mat-cell>
        </ng-container>

        <ng-container [matColumnDef]="SemanticAnalysisColumns.FOOD">
            <mat-header-cell *matHeaderCellDef class="flex justify-center !bg-white" mat-sort-header
                >{{ ReviewAnalysisTag.FOOD | enumTranslate: 'review_analysis_tags' }}
            </mat-header-cell>
            <mat-cell *matCellDef="let element; table: table" class="flex items-center justify-center">
                <div class="hidden lg:flex">
                    <span> {{ ReviewAnalysisTag.FOOD | enumTranslate: 'review_analysis_tags' }} </span>
                </div>
                <ng-container
                    [ngTemplateOutlet]="simpleValueCellTemplate"
                    [ngTemplateOutletContext]="{
                        cellValue: element.foodPositiveSentimentPercentage,
                    }">
                </ng-container>
            </mat-cell>
        </ng-container>

        <ng-container [matColumnDef]="SemanticAnalysisColumns.SERVICE">
            <mat-header-cell *matHeaderCellDef class="flex justify-center !bg-white" mat-sort-header>
                {{ ReviewAnalysisTag.SERVICE | enumTranslate: 'review_analysis_tags' }}
            </mat-header-cell>
            <mat-cell *matCellDef="let element; table: table" class="flex items-center justify-center">
                <div class="hidden lg:flex">
                    <span> {{ ReviewAnalysisTag.SERVICE | enumTranslate: 'review_analysis_tags' }} </span>
                </div>
                <ng-container
                    [ngTemplateOutlet]="simpleValueCellTemplate"
                    [ngTemplateOutletContext]="{
                        cellValue: element.servicePositiveSentimentPercentage,
                    }">
                </ng-container>
            </mat-cell>
        </ng-container>

        <ng-container [matColumnDef]="SemanticAnalysisColumns.ATMOSPHERE">
            <mat-header-cell *matHeaderCellDef class="flex justify-center !bg-white" mat-sort-header>
                {{ ReviewAnalysisTag.ATMOSPHERE | enumTranslate: 'review_analysis_tags' }}
            </mat-header-cell>
            <mat-cell *matCellDef="let element; table: table" class="flex items-center justify-center">
                <div class="hidden lg:flex">
                    <span> {{ ReviewAnalysisTag.ATMOSPHERE | enumTranslate: 'review_analysis_tags' }} </span>
                </div>
                <ng-container
                    [ngTemplateOutlet]="simpleValueCellTemplate"
                    [ngTemplateOutletContext]="{
                        cellValue: element.atmospherePositiveSentimentPercentage,
                    }">
                </ng-container>
            </mat-cell>
        </ng-container>

        <ng-container [matColumnDef]="SemanticAnalysisColumns.PRICE">
            <mat-header-cell *matHeaderCellDef class="flex justify-center !bg-white" mat-sort-header>
                {{ ReviewAnalysisTag.PRICE | enumTranslate: 'review_analysis_tags' }}
            </mat-header-cell>
            <mat-cell *matCellDef="let element; table: table" class="flex items-center justify-center">
                <div class="hidden lg:flex">
                    <span> {{ ReviewAnalysisTag.PRICE | enumTranslate: 'review_analysis_tags' }} </span>
                </div>
                <ng-container
                    [ngTemplateOutlet]="simpleValueCellTemplate"
                    [ngTemplateOutletContext]="{
                        cellValue: element.pricePositiveSentimentPercentage,
                    }">
                </ng-container>
            </mat-cell>
        </ng-container>

        <ng-container [matColumnDef]="SemanticAnalysisColumns.HYGIENE">
            <mat-header-cell *matHeaderCellDef class="flex justify-center !bg-white" mat-sort-header>
                {{ ReviewAnalysisTag.HYGIENE | enumTranslate: 'review_analysis_tags' }}
            </mat-header-cell>
            <mat-cell *matCellDef="let element; table: table" class="flex items-center justify-center">
                <div class="hidden lg:flex">
                    <span> {{ ReviewAnalysisTag.HYGIENE | enumTranslate: 'review_analysis_tags' }} </span>
                </div>
                <ng-container
                    [ngTemplateOutlet]="simpleValueCellTemplate"
                    [ngTemplateOutletContext]="{
                        cellValue: element.hygienePositiveSentimentPercentage,
                    }">
                </ng-container>
            </mat-cell>
        </ng-container>

        <ng-container [matColumnDef]="SemanticAnalysisColumns.TOP_TOPICS">
            <mat-header-cell *matHeaderCellDef class="malou-text-15--regular flex justify-center !bg-white"> </mat-header-cell>
            <mat-cell *matCellDef="let element; table: table" class="flex items-center justify-center">
                <div class="hidden lg:flex"></div>
                @if (element.restaurantId !== ALL_RESTAURANTS_ID && !isPdfDownload()) {
                    <mat-icon
                        color="primary"
                        [svgIcon]="
                            expandedRow()?.restaurantId === element.restaurantId ? SvgIcon.CHEVRON_UP : SvgIcon.CHEVRON_DOWN
                        "></mat-icon>
                }
            </mat-cell>
        </ng-container>

        <ng-container [matColumnDef]="SemanticAnalysisColumns.EXPANDED_DETAIL">
            <td
                *matCellDef="let row"
                mat-cell
                [ngClass]="{
                    '!mt-0 w-full !rounded-t-none !border !border-malou-color-border-primary':
                        row.restaurantId == expandedRow()?.restaurantId,
                    'h-0': !expandedRow() || row.restaurantId !== expandedRow()?.restaurantId,
                }"
                [attr.colspan]="displayedColumns().length">
                <div class="py-2 sm:w-full">
                    @if (expandedRow()) {
                        <div class="flex justify-between px-3 sm:flex-col sm:items-center">
                            @if (!row.topTopics.positiveTopics.length && !row.topTopics.negativeTopics.length) {
                                <div class="ml-20 flex w-full items-center justify-start">
                                    <span class="malou-color-text-1 malou-text-10--regular">
                                        {{
                                            'aggregated_statistics.e_reputation.reviews_analysis.positive_sentiments_table.no_top_topics'
                                                | translate
                                        }}
                                    </span>
                                </div>
                            } @else {
                                @if (row.topTopics.positiveTopics[0]?.length) {
                                    <img class="mr-1 mt-1 h-5 w-4 pt-1" [src]="Emoji.HEART | emojiPathResolver" />
                                }
                                <ng-container
                                    [ngTemplateOutlet]="topicListTemplate"
                                    [ngTemplateOutletContext]="{
                                        topics: row.topTopics.positiveTopics[0],
                                        sentiment: ReviewAnalysisSentiment.POSITIVE,
                                        restaurantId: row.restaurantId,
                                    }"></ng-container>
                                <ng-container
                                    [ngTemplateOutlet]="topicListTemplate"
                                    [ngTemplateOutletContext]="{
                                        topics: row.topTopics.positiveTopics[1],
                                        sentiment: ReviewAnalysisSentiment.POSITIVE,
                                        restaurantId: row.restaurantId,
                                    }"></ng-container>
                                @if (row.topTopics.negativeTopics[0]?.length) {
                                    <img class="mr-1 mt-1 h-5 w-4 pt-1" [src]="Emoji.MONOCLE | emojiPathResolver" />
                                }
                                <ng-container
                                    [ngTemplateOutlet]="topicListTemplate"
                                    [ngTemplateOutletContext]="{
                                        topics: row.topTopics.negativeTopics[0],
                                        sentiment: ReviewAnalysisSentiment.NEGATIVE,
                                        restaurantId: row.restaurantId,
                                    }"></ng-container>
                                <ng-container
                                    [ngTemplateOutlet]="topicListTemplate"
                                    [ngTemplateOutletContext]="{
                                        topics: row.topTopics.negativeTopics[1],
                                        sentiment: ReviewAnalysisSentiment.NEGATIVE,
                                        restaurantId: row.restaurantId,
                                    }"></ng-container>
                            }
                        </div>
                    }
                </div>
            </td>
        </ng-container>

        <mat-header-row *matHeaderRowDef="displayedColumns(); sticky: true" class="!bg-white"></mat-header-row>
        <mat-row
            *matRowDef="let row; columns: displayedColumns(); table: table"
            class="!mb-0"
            [ngClass]="{
                '!rounded-b-none !bg-malou-color-background-dark': expandedRow()?.restaurantId === row.restaurantId,
                '!cursor-default !bg-malou-color-background-light': row.restaurantId === ALL_RESTAURANTS_ID,
            }"
            [id]="'row_' + row.restaurantId"
            (click)="toggleExpandedRow(row)"></mat-row>
        <mat-row
            *matRowDef="let row; columns: [SemanticAnalysisColumns.EXPANDED_DETAIL]"
            class="!m-0 h-0 min-h-0 w-full !border-none !bg-malou-color-text-white"></mat-row>
    </mat-table>
</ng-template>

<ng-template let-cellData="cellData" #restaurantCellTemplate>
    <div class="flex w-full items-center" (click)="redirectToRestaurantEReputationStatsPage(cellData.restaurantId)">
        <div class="flex w-[90%] items-center">
            @if (cellData.restaurantId !== ALL_RESTAURANTS_ID) {
                <img
                    class="malou-avatar--medium mr-2 h-[45px] w-[45px] !rounded-[5px]"
                    [src]="cellData.restaurantLogo ?? ('default-picture-grey' | imagePathResolver)" />
            }
            <span
                class="truncate-two-lines malou-color-text-1 malou-text-13--semibold break-words"
                [matTooltip]="textContainer.scrollHeight > textContainer.clientHeight ? cellData.restaurantName : null"
                #textContainer
                >{{ cellData.restaurantName }}</span
            >
        </div>
        @if (cellData.restaurantId !== ALL_RESTAURANTS_ID) {
            <mat-icon class="display-on-hover ml-1 !hidden !h-3 !w-3" color="primary" [svgIcon]="SvgIcon.EXPORT"></mat-icon>
        }
    </div>
</ng-template>

<ng-template let-cellData="cellData" let-unit="unit" let-displayAsChip="displayAsChip" #valueWithEvolutionCellTemplate>
    <div class="flex items-center justify-center">
        <span
            class="!malou-text-12--regular mr-2 text-center leading-none"
            [ngClass]="{
                'malou-chip flex !h-5 !w-8 !cursor-default items-center justify-center !pt-[10px]': displayAsChip,
                'malou-chip--warn !text-malou-color-state-warn': displayAsChip && cellData.value >= 60 && cellData.value <= 80,
                'malou-chip--success-light !text-malou-color-text-green': displayAsChip && cellData.value > 80,
                'malou-chip--error-light !text-malou-color-state-error': displayAsChip && cellData.value < 60,
            }">
            {{ isNumber(cellData.value) ? (cellData.value | shortNumber) + unit : '-' }}
        </span>
        <div>
            @if (cellData.evolution) {
                <app-number-evolution
                    [value]="cellData.evolution"
                    [displayedValue]="cellData.evolution | shortNumber: { shouldDisplayMinusSign: false }"
                    [size]="'xs'">
                </app-number-evolution>
            }
        </div>
    </div>
</ng-template>

<ng-template let-cellValue="cellValue" #simpleValueCellTemplate>
    <div class="flex items-center justify-center">
        <span class="!malou-text-12--regular mr-2 text-center leading-none">
            {{ isNumber(cellValue) ? (cellValue | shortNumber) + '%' : '-' }}
        </span>
    </div>
</ng-template>

<ng-template let-topics="topics" let-sentiment="sentiment" let-restaurantId="restaurantId" #topicListTemplate>
    <div class="flex w-[24%] flex-col sm:w-[60%]">
        @for (topic of topics; track $index) {
            <ng-container [ngTemplateOutlet]="topicTemplate" [ngTemplateOutletContext]="{ sentiment, topic, restaurantId }"></ng-container>
        }
    </div>
</ng-template>

<ng-template let-sentiment="sentiment" let-topic="topic" let-restaurantId="restaurantId" #topicTemplate>
    @if (topic) {
        <div
            class="group flex max-h-9 w-[95%] cursor-pointer items-center justify-between rounded-[5px] p-1.5 hover:bg-malou-color-background-light"
            (click)="openModalForTopic(topic, sentiment, restaurantId)">
            <div class="items flex w-full items-center justify-between">
                <span
                    class="malou-text-11--regular custom-truncate group-hover:malou-text-11--bold text-malou-color-text-2"
                    [matTooltip]="
                        textContainer.offsetWidth < textContainer.scrollWidth
                            ? topic.displayedNameInCurrentLang || topic.displayedName
                            : null
                    "
                    #textContainer>
                    {{ topic.displayedNameInCurrentLang || topic.displayedName }}
                </span>
                <div
                    class="!malou-text-12--regular malou-chip ml-1 flex !h-5 !w-8 !cursor-default items-center justify-center !pt-[10px] text-malou-color-text-2"
                    [ngClass]="{
                        'malou-chip--success-light text-malou-color-text-green': sentiment === ReviewAnalysisSentiment.POSITIVE,
                        'malou-chip--error-light malou-color-state-error': sentiment === ReviewAnalysisSentiment.NEGATIVE,
                    }">
                    {{ sentiment === ReviewAnalysisSentiment.POSITIVE ? (topic.positiveCount ?? '-') : (topic.negativeCount ?? '-') }}
                </div>
            </div>
        </div>
    }
</ng-template>
