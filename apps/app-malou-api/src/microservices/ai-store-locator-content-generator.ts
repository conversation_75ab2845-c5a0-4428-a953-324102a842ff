import { singleton } from 'tsyringe';

import { AiInteractionRelatedEntityCollection } from '@malou-io/package-utils';

import { Config } from ':config';
import { GenericAiService, GenericAiServiceResponseType } from ':microservices/ai-lambda-template/generic-ai-service';
import { GenerateStoreLocatorContentPayload, GenerateStoreLocatorContentType } from ':modules/ai/interfaces/ai.interfaces';

export interface AiStoreLocatorContent {
    text: string;
}

export interface AiStoreLocatorDescriptionsContent {
    blocks: {
        title: string;
        sections: {
            subtitle: string;
            text: string;
        }[];
    }[];
}

export type AiStoreLocatorContentType<T> = T extends GenerateStoreLocatorContentType.DESCRIPTION_BLOCK_GENERATION
    ? AiStoreLocatorDescriptionsContent
    : AiStoreLocatorContent;

@singleton()
export class AiStoreLocatorContentService {
    async generateStoreLocatorContent<T extends GenerateStoreLocatorContentType>(
        type: T,
        payload: GenerateStoreLocatorContentPayload['restaurantData']
    ): Promise<GenericAiServiceResponseType<AiStoreLocatorContentType<T>>> {
        const AiService = new GenericAiService<GenerateStoreLocatorContentPayload, AiStoreLocatorContentType<T>>({
            lambdaUrl: Config.services.aiStoreLocatorContentGenerationService.functionName,
        });
        return AiService.generateCompletion({
            restaurantData: payload,
            relatedEntityCollection: AiInteractionRelatedEntityCollection.STORE_LOCATOR_RESTAURANT_PAGE,
            type,
        });
    }
}
