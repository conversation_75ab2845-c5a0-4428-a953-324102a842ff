import AWS from 'aws-sdk';
import { isNil } from 'lodash';
import assert from 'node:assert';

import {
    AiInteractionRelatedEntityCollection,
    AiInteractionSubType,
    AiInteractionType,
    ChatCompletionRole,
    isNotNil,
    MalouErrorCode,
} from '@malou-io/package-utils';

import { Config } from ':config';
import { MalouError } from ':helpers/classes/malou-error';
import { logger } from ':helpers/logger';

const lambda = new AWS.Lambda({
    region: Config.services.aws.region,
    accessKeyId: Config.services.aws.key,
    secretAccessKey: Config.services.aws.secret,
});

interface GenericAiServiceLambdaInteractionDetails {
    message: {
        system?: string;
        user: string;
    }[];
    completionText: string;
    completionTokenCount: number;
    promptTokenCount: number;
    type: AiInteractionType;
    subType?: AiInteractionSubType;
    completionTimeInMilliseconds: number;
    modelConfig?: {
        frequencyPenalty?: number;
        maxTokens?: number;
        temperature?: number;
        timeout?: number;
        model?: string;
    };
    numberOfRetries?: number;
}

interface GenericAiServiceLambdaResponse<K> {
    errorMessage?: string;
    errorType?: string;
    aiResponse?: K;
    aiInteractionDetails?: GenericAiServiceLambdaInteractionDetails | GenericAiServiceLambdaInteractionDetails[];
}

export interface GenericAiServiceCompletionResponse {
    completionText: string;
    completionTokenCount: number;
    promptTokenCount: number;
    completionTimeInMilliseconds: number;
    responseTimeInMilliseconds: number;
    messages: {
        role: ChatCompletionRole;
        text: string;
    }[];
    modelConfig?: {
        frequencyPenalty?: number;
        maxTokens?: number;
        temperature?: number;
        timeout?: number;
        model?: string;
    };
    retryCount?: number;
    rawCompletionText: string;
    relatedEntityCollection?: AiInteractionRelatedEntityCollection;
    type?: AiInteractionType;
    subType?: AiInteractionSubType;
}

export interface GenericAiServiceResponseType<K> {
    aiResponse: K;
    aiInteractionDetails: GenericAiServiceCompletionResponse[];
}

export class GenericAiService<T, K> {
    private readonly _lambdaUrl: string;

    constructor({ lambdaUrl }: { lambdaUrl: string }) {
        // Ideally this wouldn't be necessary but this issue is frequent because strictNullChecks is disabled.
        assert(
            lambdaUrl,
            `[GenericAiService] Missing lambda URL for the class ${this.constructor.name}. Something is probably missing in the .env.… configuration file.`
        );
        this._lambdaUrl = lambdaUrl;
    }

    async generateCompletion(payload: T): Promise<GenericAiServiceResponseType<K>> {
        const startTime = Date.now();
        logger.info(`[GenericAiService] [${this._lambdaUrl}] Start generating completion`, { payload });
        const response = await this._callLambda(payload);
        const endTime = Date.now();
        logger.info(`[GenericAiService] [${this._lambdaUrl}] End generating completion`, { response, duration: endTime - startTime });

        if (response.errorMessage || isNil(response.aiResponse)) {
            logger.error(`[GenericAiService] [${this._lambdaUrl}] Error`, response.errorMessage);
            throw new MalouError(MalouErrorCode.AI_REQUEST_FAILED, {
                message: 'Ai request failed',
                metadata: { payload, errorMessage: response.errorMessage, errorType: response.errorType },
            });
        }

        return {
            aiResponse: response.aiResponse,
            aiInteractionDetails: this._handleAiInteractionsDetails(
                response.aiInteractionDetails,
                response.aiResponse,
                endTime - startTime
            ),
        };
    }

    private async _callLambda<G>(payload: G): Promise<GenericAiServiceLambdaResponse<K>> {
        const params: AWS.Lambda.InvocationRequest = {
            FunctionName: this._lambdaUrl,
            Payload: JSON.stringify(payload),
        };

        return new Promise((resolve, reject) => {
            lambda.invoke(params, (err, data) => {
                if (err) {
                    reject(err);
                } else {
                    resolve(data);
                }
            });
        }).then((data) => JSON.parse((data as AWS.Lambda.InvocationResponse).Payload as string));
    }

    private _handleAiInteractionsDetails = (
        aiInteractionDetails: GenericAiServiceLambdaInteractionDetails | GenericAiServiceLambdaInteractionDetails[] | undefined,
        aiResponse: any,
        aiResponseTimeInMilliseconds: number
    ): GenericAiServiceCompletionResponse[] => {
        if (!aiInteractionDetails) {
            return [];
        }
        return Array.isArray(aiInteractionDetails)
            ? this._mapMultipleAiInteractionDetails(aiInteractionDetails, aiResponse, aiResponseTimeInMilliseconds)
            : this._mapSingleAiInteractionDetails(aiInteractionDetails, aiResponse, aiResponseTimeInMilliseconds);
    };

    private _mapMultipleAiInteractionDetails = (
        aiInteractionDetails: any,
        aiResponse: any,
        responseTimeInMilliseconds: number
    ): GenericAiServiceCompletionResponse[] =>
        aiInteractionDetails?.map((aiInteractionDetail) => ({
            completionText: typeof aiResponse === 'string' ? aiResponse : JSON.stringify(aiResponse),
            rawCompletionText: aiInteractionDetail.completionText,
            completionTokenCount: aiInteractionDetail.completionTokenCount,
            promptTokenCount: aiInteractionDetail.promptTokenCount,
            completionTimeInMilliseconds: aiInteractionDetail.completionTimeInMilliseconds,
            responseTimeInMilliseconds,
            modelConfig: aiInteractionDetail.modelConfig
                ? {
                      frequencyPenalty: aiInteractionDetail.modelConfig.frequencyPenalty,
                      maxTokens: aiInteractionDetail.modelConfig.maxTokens,
                      temperature: aiInteractionDetail.modelConfig.temperature,
                      timeout: aiInteractionDetail.modelConfig.timeout,
                      model: aiInteractionDetail.modelConfig.model,
                  }
                : null,
            retryCount: aiInteractionDetail.numberOfRetries,
            messages: this._mapRequestMessage(aiInteractionDetail.message),
            relatedEntityCollection: aiInteractionDetail.relatedEntityCollection,
            ...(aiInteractionDetail.type ? { type: aiInteractionDetail.type as AiInteractionType } : {}),
            ...(aiInteractionDetail.subType ? { subType: aiInteractionDetail.subType as AiInteractionSubType } : {}),
        })) ?? [];

    private _mapSingleAiInteractionDetails = (
        aiInteractionDetails: any,
        aiResponse: any,
        responseTimeInMilliseconds: number
    ): GenericAiServiceCompletionResponse[] =>
        aiInteractionDetails ? this._mapMultipleAiInteractionDetails([aiInteractionDetails], aiResponse, responseTimeInMilliseconds) : [];

    private _mapRequestMessage = (messages: { system?: string; user: string }[]): { role: ChatCompletionRole; text: string }[] =>
        messages
            .map((message) => {
                if (message.system) {
                    return {
                        role: ChatCompletionRole.SYSTEM,
                        text: message.system,
                    };
                }
                if (message.user) {
                    return {
                        role: ChatCompletionRole.USER,
                        text: message.user,
                    };
                }
                return null;
            })
            .filter(isNotNil)
            .filter((message) => !!message.text?.length);
}
