/* Use this task as a playground to run stuff using any app logic.
You can also just copy paste to another file to keep track of previous run tasks */
import 'reflect-metadata';

import ':env';

import { container, singleton } from 'tsyringe';

import { StoreLocatorCreateFirstPagesUseCase } from ':modules/store-locator/use-cases/create-first-pages/create-first-pages.use-case';
import ':plugins/db';

@singleton()
class DefaultTask {
    constructor(private readonly _storeLocatorCreateFirstPagesUseCase: StoreLocatorCreateFirstPagesUseCase) {}

    async execute() {
        await this._storeLocatorCreateFirstPagesUseCase.execute('61dc14920b4fcb4eea6b2fd1');
    }
}

const task = container.resolve(DefaultTask);
task.execute()
    .then(() => {
        process.exit(0);
    })
    .catch((error) => {
        console.error('err :>>', error);
        process.exit(1);
    });
