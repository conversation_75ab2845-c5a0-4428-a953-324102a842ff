import 'reflect-metadata';

import ':env';

import { container, singleton } from 'tsyringe';

import { toDbId } from '@malou-io/package-models';
import { StoreLocatorRestaurantPageElementIds } from '@malou-io/package-utils';

import { GenerateTailwindConfigurationService } from ':modules/store-locator/services/generate-tailwind-configuration/generate-tailwind-configuration.service';
import StoreLocatorOrganizationConfigRepository from ':modules/store-locator/store-locator-organization-config.repository';

@singleton()
class CreateOrganizationConfigurationTask {
    constructor(
        private readonly _storeLocatorOrganizationConfigRepository: StoreLocatorOrganizationConfigRepository,
        private readonly _generateTailwindConfigurationService: GenerateTailwindConfigurationService
    ) {}

    async execute(): Promise<void> {
        // const config = await this._getTailwindConfiguration('67cf1ef531d778287af0d2ef');
        await this._handleBioBurgerConfiguration();
        await this._handleBolkiriConfiguration();
        await this._handleKrispyKremeConfiguration();
    }

    private async _handleBioBurgerConfiguration(): Promise<void> {
        await this._storeLocatorOrganizationConfigRepository.updateOne({
            filter: {
                organizationId: toDbId('66695c8079a84d7da2a8e4cd'),
            },
            update: {
                plugins: {
                    googleAnalytics: {
                        trackingId: 'G-BB80WJF683',
                    },
                },
                isLive: true,
                shouldDisplayWhiteMark: false,
                styles: {
                    fonts: [
                        {
                            class: 'primary',
                            src: 'https://malou-production.s3.eu-west-3.amazonaws.com/store-locator/organization/66695c8079a84d7da2a8e4cd/fonts/BIOBURGERGintoNord_Regular.woff',
                        },
                        {
                            class: 'primary-bold',
                            src: 'https://malou-production.s3.eu-west-3.amazonaws.com/store-locator/organization/66695c8079a84d7da2a8e4cd/fonts/BIOBURGERGintoNordCondensed_Black.woff',
                            weight: '900',
                        },
                    ],
                    colors: [
                        {
                            class: 'primary',
                            value: '#19422c',
                        },
                        {
                            class: 'secondary',
                            value: '#eb7049',
                        },
                        {
                            class: 'tertiary',
                            value: '#fcf6ec',
                        },
                        {
                            class: 'fourth',
                            value: '#c4a696',
                        },
                    ],
                    pages: {
                        store: {
                            [StoreLocatorRestaurantPageElementIds.INFORMATION_WRAPPER]: [
                                'bg-tertiary',
                                'text-primary',
                                'mt-[116px]',
                                'xl:h-[calc(100vh-116px)]',
                            ],
                            [StoreLocatorRestaurantPageElementIds.INFORMATION_TITLE]: ['font-primary-bold', 'text-secondary'],
                            [StoreLocatorRestaurantPageElementIds.INFORMATION_ICONS]: ['fill-secondary'],
                            [StoreLocatorRestaurantPageElementIds.INFORMATION_OPENING_SOON_BANNER]: [
                                'bg-primary',
                                'text-white',
                                'rounded-full',
                            ],
                            [StoreLocatorRestaurantPageElementIds.INFORMATION_BANNER]: ['bg-tertiary'],
                            [StoreLocatorRestaurantPageElementIds.INFORMATION_BANNER_CTA_1]: [
                                'border-primary',
                                'text-primary',
                                'hover:bg-primary',
                                'hover:text-white',
                                'rounded-full',
                            ],
                            [StoreLocatorRestaurantPageElementIds.INFORMATION_BANNER_CTA_2]: [
                                'border-primary',
                                'bg-primary',
                                'text-white',
                                'hover:text-primary',
                                'rounded-full',
                            ],
                            [StoreLocatorRestaurantPageElementIds.INFORMATION_BANNER_CTA_ICON]: ['fill-primary'],
                            [StoreLocatorRestaurantPageElementIds.GALLERY_WRAPPER]: ['bg-fourth', 'text-primary'],
                            [StoreLocatorRestaurantPageElementIds.GALLERY_TITLE]: ['font-primary-bold'],
                            [StoreLocatorRestaurantPageElementIds.REVIEWS_WRAPPER]: ['bg-primary'],
                            [StoreLocatorRestaurantPageElementIds.REVIEWS_TITLE]: ['text-secondary', 'font-primary-bold'],
                            [StoreLocatorRestaurantPageElementIds.REVIEWS_CTA]: [
                                'bg-tertiary',
                                'text-primary',
                                'hover:bg-primary',
                                'hover:text-tertiary',
                                'hover:border-tertiary',
                                'rounded-full',
                            ],
                            [StoreLocatorRestaurantPageElementIds.CALL_TO_ACTIONS_WRAPPER]: ['bg-fourth'],
                            [StoreLocatorRestaurantPageElementIds.CALL_TO_ACTIONS_TITLE]: ['text-primary', 'font-primary-bold'],
                            [StoreLocatorRestaurantPageElementIds.CALL_TO_ACTIONS_CTA]: [
                                'bg-primary',
                                'hover:bg-tertiary',
                                'hover:text-primary',
                                'border-primary',
                                'text-white',
                                'rounded-full',
                            ],
                            [StoreLocatorRestaurantPageElementIds.SOCIAL_NETWORKS_WRAPPER]: ['bg-secondary'],
                            [StoreLocatorRestaurantPageElementIds.SOCIAL_NETWORKS_TITLE]: ['text-tertiary', 'font-primary-bold'],
                            [StoreLocatorRestaurantPageElementIds.SOCIAL_NETWORKS_PROFILE]: ['text-tertiary'],
                            [StoreLocatorRestaurantPageElementIds.SOCIAL_NETWORKS_PROFILE_NAME]: ['font-primary-bold'],
                            [StoreLocatorRestaurantPageElementIds.DESCRIPTIONS_BLOCK_EVEN]: ['bg-tertiary', 'text-primary'],
                            [StoreLocatorRestaurantPageElementIds.DESCRIPTIONS_BLOCK_TITLE_EVEN]: ['font-primary-bold'],
                            [StoreLocatorRestaurantPageElementIds.DESCRIPTIONS_BLOCK_UNEVEN]: ['bg-secondary', 'text-tertiary'],
                            [StoreLocatorRestaurantPageElementIds.DESCRIPTIONS_BLOCK_TITLE_UNEVEN]: ['font-primary-bold'],
                            [StoreLocatorRestaurantPageElementIds.DESCRIPTIONS_BLOCK_SUBTITLE]: ['font-primary-bold'],
                        },
                    },
                },
            },
        });
    }

    private async _handleKrispyKremeConfiguration(): Promise<void> {
        const storePageStyles = {
            [StoreLocatorRestaurantPageElementIds.INFORMATION_WRAPPER]: [
                'bg-white',
                'text-black',
                'mt-[110px]',
                'xl:h-[calc(100vh-110px)]',
            ],
            [StoreLocatorRestaurantPageElementIds.INFORMATION_TITLE]: ['font-primary', 'text-primary'],
            [StoreLocatorRestaurantPageElementIds.INFORMATION_ICONS]: ['fill-secondary'],
            [StoreLocatorRestaurantPageElementIds.INFORMATION_OPENING_SOON_BANNER]: ['bg-secondary', 'text-white', 'rounded-sm'],
            [StoreLocatorRestaurantPageElementIds.INFORMATION_BANNER]: ['bg-primary'],
            [StoreLocatorRestaurantPageElementIds.INFORMATION_BANNER_CTA_1]: [
                'bg-secondary',
                'text-white',
                'hover:bg-white',
                'hover:text-secondary',
                'hover:border-secondary',
                'rounded-sm',
            ],
            [StoreLocatorRestaurantPageElementIds.INFORMATION_BANNER_CTA_2]: [
                'border-primary',
                'bg-primary',
                'text-white',
                'hover:text-primary',
                'rounded-sm',
            ],
            [StoreLocatorRestaurantPageElementIds.INFORMATION_BANNER_CTA_ICON]: ['fill-white'],
            [StoreLocatorRestaurantPageElementIds.GALLERY_WRAPPER]: ['bg-white', 'text-primary'],
            [StoreLocatorRestaurantPageElementIds.GALLERY_TITLE]: ['font-primary'],
            [StoreLocatorRestaurantPageElementIds.GALLERY_PICTURE]: ['rounded-lg'],
            [StoreLocatorRestaurantPageElementIds.REVIEWS_WRAPPER]: ['bg-primary'],
            [StoreLocatorRestaurantPageElementIds.REVIEWS_TITLE]: ['text-white', 'font-primary'],
            [StoreLocatorRestaurantPageElementIds.REVIEWS_CTA]: [
                'bg-secondary',
                'text-white',
                'border-secondary',
                'hover:bg-transparent',
                'hover:text-secondary',
                'hover:border-secondary',
                'rounded-lg',
            ],
            [StoreLocatorRestaurantPageElementIds.CALL_TO_ACTIONS_WRAPPER]: ['bg-secondary'],
            [StoreLocatorRestaurantPageElementIds.CALL_TO_ACTIONS_TITLE]: ['text-white', 'font-primary'],
            [StoreLocatorRestaurantPageElementIds.CALL_TO_ACTIONS_CTA]: [
                'bg-transparent',
                'border-white',
                'text-white',
                'hover:bg-white',
                'hover:text-secondary',
                'rounded-lg',
            ],
            [StoreLocatorRestaurantPageElementIds.SOCIAL_NETWORKS_WRAPPER]: ['bg-white'],
            [StoreLocatorRestaurantPageElementIds.SOCIAL_NETWORKS_TITLE]: ['text-primary', 'font-primary'],
            [StoreLocatorRestaurantPageElementIds.SOCIAL_NETWORKS_PROFILE]: ['text-black'],
            [StoreLocatorRestaurantPageElementIds.SOCIAL_NETWORKS_PROFILE_NAME]: ['font-black'],
            [StoreLocatorRestaurantPageElementIds.DESCRIPTIONS_BLOCK_EVEN]: ['bg-white', 'text-black'],
            [StoreLocatorRestaurantPageElementIds.DESCRIPTIONS_BLOCK_TITLE_EVEN]: ['font-primary', 'text-tertiary'],
            [StoreLocatorRestaurantPageElementIds.DESCRIPTIONS_BLOCK_UNEVEN]: ['bg-white', 'text-black'],
            [StoreLocatorRestaurantPageElementIds.DESCRIPTIONS_BLOCK_TITLE_UNEVEN]: ['font-primary', 'text-tertiary'],
        };
        await this._storeLocatorOrganizationConfigRepository.create({
            data: {
                organizationId: toDbId('655e40ed9d2240f0d1f4bce4'),
                cloudfrontDistributionId: 'E16N8CKZ24TERD',
                baseUrl: 'https://restaurants.krispykreme.fr',
                isLive: false,
                styles: {
                    fonts: [
                        {
                            class: 'primary',
                            src: 'https://malou-production.s3.eu-west-3.amazonaws.com/store-locator/organization/655e40ed9d2240f0d1f4bce4/fonts/brandon_blk.woff2',
                        },
                    ],
                    colors: [
                        {
                            class: 'primary',
                            value: '#026D42',
                        },
                        {
                            class: 'secondary',
                            value: '#C72338',
                        },
                        {
                            class: 'tertiary',
                            value: '#833921',
                        },
                    ],
                    pages: {
                        store: storePageStyles,
                        map: {},
                        storeDraft: storePageStyles,
                        mapDraft: {},
                    },
                },
            },
        });
    }

    private async _handleBolkiriConfiguration(): Promise<void> {
        await this._storeLocatorOrganizationConfigRepository.updateOne({
            filter: { organizationId: toDbId('67cf1ef531d778287af0d2ef') },
            update: {
                isLive: false,
                shouldDisplayWhiteMark: true,
                'styles.pages': {
                    store: {
                        [StoreLocatorRestaurantPageElementIds.INFORMATION_WRAPPER]: [
                            'bg-white',
                            'text-tertiary',
                            'mt-[80px]',
                            'xl:h-[calc(100vh-80px)]',
                        ],
                        [StoreLocatorRestaurantPageElementIds.INFORMATION_TITLE]: ['font-tertiary', 'text-primary'],
                        [StoreLocatorRestaurantPageElementIds.INFORMATION_ICONS]: ['fill-secondary'],
                        [StoreLocatorRestaurantPageElementIds.INFORMATION_OPENING_SOON_BANNER]: [
                            'bg-secondary',
                            'text-primary',
                            'rounded-sm',
                        ],
                        [StoreLocatorRestaurantPageElementIds.INFORMATION_BANNER]: ['bg-white'],
                        [StoreLocatorRestaurantPageElementIds.INFORMATION_BANNER_CTA_1]: [
                            'border-tertiary',
                            'text-tertiary',
                            'hover:bg-tertiary',
                            'hover:text-white',
                            'rounded-sm',
                        ],
                        [StoreLocatorRestaurantPageElementIds.INFORMATION_BANNER_CTA_2]: [
                            'border-primary',
                            'bg-primary',
                            'text-white',
                            'hover:text-primary',
                            'rounded-sm',
                        ],
                        [StoreLocatorRestaurantPageElementIds.INFORMATION_BANNER_CTA_ICON]: ['fill-tertiary'],
                        [StoreLocatorRestaurantPageElementIds.GALLERY_WRAPPER]: ['bg-fourth', 'text-tertiary'],
                        [StoreLocatorRestaurantPageElementIds.GALLERY_TITLE]: ['font-tertiary'],
                        [StoreLocatorRestaurantPageElementIds.GALLERY_PICTURE]: ['rounded-lg'],
                        [StoreLocatorRestaurantPageElementIds.REVIEWS_WRAPPER]: ['bg-secondary'],
                        [StoreLocatorRestaurantPageElementIds.REVIEWS_TITLE]: ['text-primary', 'font-tertiary'],
                        [StoreLocatorRestaurantPageElementIds.REVIEWS_CTA]: [
                            'bg-primary',
                            'text-white',
                            'border-primary',
                            'hover:bg-secondary',
                            'hover:text-primary',
                            'rounded-lg',
                        ],
                        [StoreLocatorRestaurantPageElementIds.CALL_TO_ACTIONS_WRAPPER]: ['bg-fourth'],
                        [StoreLocatorRestaurantPageElementIds.CALL_TO_ACTIONS_TITLE]: ['text-tertiary', 'font-tertiary'],
                        [StoreLocatorRestaurantPageElementIds.CALL_TO_ACTIONS_CTA]: [
                            'bg-primary',
                            'border-primary',
                            'text-white',
                            'hover:bg-white',
                            'hover:border-primary',
                            'hover:text-primary',
                            'rounded-lg',
                        ],
                        [StoreLocatorRestaurantPageElementIds.SOCIAL_NETWORKS_WRAPPER]: ['bg-white'],
                        [StoreLocatorRestaurantPageElementIds.SOCIAL_NETWORKS_TITLE]: ['text-primary', 'font-tertiary'],
                        [StoreLocatorRestaurantPageElementIds.SOCIAL_NETWORKS_PROFILE]: ['text-black'],
                        [StoreLocatorRestaurantPageElementIds.SOCIAL_NETWORKS_PROFILE_NAME]: ['font-tertiary'],
                        [StoreLocatorRestaurantPageElementIds.DESCRIPTIONS_BLOCK_EVEN]: ['bg-white', 'text-tertiary'],
                        [StoreLocatorRestaurantPageElementIds.DESCRIPTIONS_BLOCK_TITLE_EVEN]: ['font-tertiary', 'text-secondary'],
                        [StoreLocatorRestaurantPageElementIds.DESCRIPTIONS_BLOCK_UNEVEN]: ['bg-white', 'text-tertiary'],
                        [StoreLocatorRestaurantPageElementIds.DESCRIPTIONS_BLOCK_TITLE_UNEVEN]: ['font-tertiary', 'text-primary'],
                        [StoreLocatorRestaurantPageElementIds.WHITE_MARK_WRAPPER]: ['font-secondary', 'bg-tertiary', 'text-white'],
                        [StoreLocatorRestaurantPageElementIds.WHITE_MARK_LOGO]: ['fill-white'],
                    },
                },
            },
        });
    }

    private async _getTailwindConfiguration(organizationId: string) {
        const storeLocatorOrganizationConfig =
            await this._storeLocatorOrganizationConfigRepository.getOrganizationConfiguration(organizationId);

        if (!storeLocatorOrganizationConfig) {
            throw new Error(`No configuration found for organization ${organizationId}`);
        }

        // Assuming GenerateTailwindConfigurationService is implemented and available
        return this._generateTailwindConfigurationService.execute({ storeLocatorOrganizationConfig });
    }
}

const task = container.resolve(CreateOrganizationConfigurationTask);

task.execute()
    .then(() => {
        console.log('Task completed');
        process.exit(0);
    })
    .catch((error) => {
        console.error('Error while executing task', error);
        process.exit(1);
    });
