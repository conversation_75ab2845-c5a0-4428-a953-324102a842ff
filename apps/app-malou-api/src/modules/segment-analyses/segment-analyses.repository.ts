import { FilterQuery } from 'mongoose';
import { singleton } from 'tsyringe';

import {
    EntityRepository,
    ISegmentAnalysis,
    ISegmentAnalysisWithParentTopics,
    ReadPreferenceMode,
    SegmentAnalysisModel,
    toDbId,
    toDbIds,
} from '@malou-io/package-models';
import { ReviewAnalysisSentiment, ReviewAnalysisTag } from '@malou-io/package-utils';

import { SegmentAnalysis, SegmentAnalysisProps } from ':modules/segment-analyses/entities/segment-analysis.entity';
import {
    SegmentAnalysesReviewCountWithCategory,
    SegmentAnalysesReviewCountWithCategoryByReviewSocialId,
    SegmentAnalysesReviewCountWithParentTopicIds,
} from ':modules/segment-analyses/segment-analyses.interface';
import { SegmentAnalysisParentTopic } from ':modules/segment-analysis-parent-topics/entities/segment-analysis-parent-topic.entity';
import { Translations } from ':modules/translations/entities/translations.entity';

export type SegmentAnalysisPayload = Omit<ISegmentAnalysis, 'createdAt' | 'updatedAt' | '_id'>;

export type SegmentAnalysisParentTopicWithSegmentCount = {
    id: string;
    restaurantId: string;
    segmentCount: number;
    subcategory?: string | null;
    category: ReviewAnalysisTag;
    sentiment?: ReviewAnalysisSentiment | null;
    name: string;
    mergedParentTopicNames: string[];
};

@singleton()
export class SegmentAnalysesRepository extends EntityRepository<ISegmentAnalysis> {
    constructor() {
        super(SegmentAnalysisModel);
    }

    async findBySegmentAnalysisParentTopicId(segmentAnalysisParentTopicId: string): Promise<SegmentAnalysis[]> {
        const segmentAnalyses = await this.find({
            filter: {
                segmentAnalysisParentTopicIds: toDbId(segmentAnalysisParentTopicId),
            },
            options: {
                lean: true,
            },
        });
        return segmentAnalyses.map((segmentAnalysis) => this.toEntity(segmentAnalysis));
    }

    async upsertManySegmentAnalyses(segmentAnalyses: Omit<SegmentAnalysis, 'id'>[]): Promise<void> {
        const bulkOperations = segmentAnalyses.map((segmentAnalysis) => ({
            updateOne: {
                filter: {
                    reviewSocialId: segmentAnalysis.reviewSocialId,
                    platformSocialId: segmentAnalysis.platformSocialId,
                    platformKey: segmentAnalysis.platformKey,
                    category: segmentAnalysis.category,
                    subcategory: segmentAnalysis.subcategory,
                    sentiment: segmentAnalysis.sentiment,
                    topic: segmentAnalysis.topic,
                    segment: segmentAnalysis.segment,
                },
                update: {
                    ...segmentAnalysis,
                    segmentAnalysisParentTopicIds: segmentAnalysis.segmentAnalysisParentTopicIds
                        ? toDbIds(segmentAnalysis.segmentAnalysisParentTopicIds)
                        : [],
                },
                upsert: true,
            },
        }));
        await SegmentAnalysisModel.bulkWrite(bulkOperations);
    }

    async getSegmentAnalysisParentTopicsWithSegmentCount(
        segmentAnalysisParentTopicIds: string[]
    ): Promise<SegmentAnalysisParentTopicWithSegmentCount[]> {
        const topicIdsList = toDbIds(segmentAnalysisParentTopicIds);
        const results = await this.aggregate(
            [
                { $match: { segmentAnalysisParentTopicIds: { $in: topicIdsList } } },
                { $unwind: '$segmentAnalysisParentTopicIds' },
                { $match: { segmentAnalysisParentTopicIds: { $in: topicIdsList } } },
                {
                    $lookup: {
                        from: 'segmentanalysisparenttopics',
                        localField: 'segmentAnalysisParentTopicIds',
                        foreignField: '_id',
                        as: 'topic',
                    },
                },
                { $unwind: '$topic' },
                {
                    $group: {
                        _id: '$segmentAnalysisParentTopicIds',
                        segmentCount: { $sum: 1 },
                        topic: { $first: '$topic' },
                    },
                },
                {
                    $project: {
                        _id: 1,
                        segmentCount: 1,
                        name: '$topic.name',
                        category: '$topic.category',
                        subcategory: '$topic.subcategory',
                        sentiment: '$topic.sentiment',
                        mergedParentTopicNames: '$topic.mergedParentTopicNames',
                        restaurantId: '$topic.restaurantId',
                    },
                },
            ],
            {
                readPreference: ReadPreferenceMode.SECONDARY,
                comment: 'getSegmentAnalysisParentTopicsWithSegmentCount',
            }
        );

        return results.map((res) => ({
            id: res._id.toString(),
            restaurantId: res.restaurantId.toString(),
            segmentCount: res.segmentCount,
            subcategory: res.subcategory || null,
            category: res.category,
            sentiment: res.sentiment || null,
            name: res.name,
            mergedParentTopicNames: res.mergedParentTopicNames || [],
        }));
    }

    async getSegmentAnalysesReviewCountBySentiment(
        filter: FilterQuery<ISegmentAnalysis>
    ): Promise<SegmentAnalysesReviewCountWithParentTopicIds[]> {
        return this.aggregate(
            [
                {
                    $match: filter,
                },
                {
                    $unwind: '$segmentAnalysisParentTopicIds',
                },
                {
                    $group: {
                        _id: {
                            sentiment: '$sentiment',
                            platformSocialId: '$platformSocialId',
                            segmentAnalysisParentTopicId: '$segmentAnalysisParentTopicIds',
                            reviewSocialId: '$reviewSocialId',
                        },
                    },
                },
                {
                    $group: {
                        _id: {
                            sentiment: '$_id.sentiment',
                            platformSocialId: '$_id.platformSocialId',
                            segmentAnalysisParentTopicId: '$_id.segmentAnalysisParentTopicId',
                        },
                        reviewSocialIds: {
                            $push: '$_id.reviewSocialId',
                        },
                    },
                },
                {
                    $project: {
                        _id: 0,
                        sentiment: '$_id.sentiment',
                        platformSocialId: '$_id.platformSocialId',
                        segmentAnalysisParentTopicId: '$_id.segmentAnalysisParentTopicId',
                        reviewSocialIds: '$reviewSocialIds',
                    },
                },
            ],
            {
                readPreference: ReadPreferenceMode.SECONDARY,
                comment: 'getSegmentAnalysesReviewCountBySentiment',
            }
        ).exec();
    }

    async getSegmentAnalysesReviewCountByCategoryAndSentiment(
        filter: FilterQuery<ISegmentAnalysis>
    ): Promise<SegmentAnalysesReviewCountWithCategory[]> {
        return this.aggregate(
            [
                {
                    $match: filter,
                },
                {
                    $group: {
                        _id: {
                            sentiment: '$sentiment',
                            category: '$category',
                            reviewSocialId: '$reviewSocialId',
                            platformKey: '$platformKey',
                            platformSocialId: '$platformSocialId',
                        },
                    },
                },
                {
                    $group: {
                        _id: {
                            sentiment: '$_id.sentiment',
                            category: '$_id.category',
                            platformSocialId: '$_id.platformSocialId',
                        },
                        uniqueReviewCount: {
                            $sum: 1,
                        },
                    },
                },
                {
                    $project: {
                        _id: 0,
                        sentiment: '$_id.sentiment',
                        category: '$_id.category',
                        platformSocialId: '$_id.platformSocialId',
                        reviewCount: '$uniqueReviewCount',
                    },
                },
            ],
            {
                readPreference: ReadPreferenceMode.SECONDARY,
                comment: 'getSegmentAnalysesReviewCountByCategoryAndSentiment',
            }
        ).exec();
    }
    async getSegmentAnalysesReviewCountByCategoryAndSentimentByReviewsSocialIds(
        reviewsSocialIds: string[]
    ): Promise<SegmentAnalysesReviewCountWithCategoryByReviewSocialId[]> {
        return this.aggregate(
            [
                {
                    $match: {
                        reviewSocialId: { $in: reviewsSocialIds },
                    },
                },
                {
                    $group: {
                        _id: '$reviewSocialId',
                        analysis: {
                            $push: {
                                category: '$category',
                                sentiment: '$sentiment',
                                segment: '$segment',
                            },
                        },
                    },
                },
                {
                    $project: {
                        _id: 0,
                        reviewSocialId: '$_id',
                        analysis: '$analysis',
                    },
                },
            ],
            {
                readPreference: ReadPreferenceMode.SECONDARY,
                comment: 'getSegmentAnalysesReviewCountByCategoryAndSentimentByReviewsSocialIds',
            }
        ).exec();
    }

    async getTimeSeriesSegmentAnalyses(matchFilter: FilterQuery<ISegmentAnalysis>): Promise<any[]> {
        return this.aggregate(
            [
                {
                    $match: matchFilter,
                },
                {
                    $addFields: {
                        day: { $dayOfYear: '$reviewSocialCreatedAt' },
                        week: { $isoWeek: '$reviewSocialCreatedAt' },
                        month: { $month: '$reviewSocialCreatedAt' },
                        year: { $year: '$reviewSocialCreatedAt' },
                        weekYear: { $isoWeekYear: '$reviewSocialCreatedAt' },
                    },
                },
                {
                    $facet: {
                        dataPerDay: [
                            {
                                $group: {
                                    _id: {
                                        year: '$year',
                                        day: '$day',
                                        reviewSocialId: '$reviewSocialId',
                                        sentiment: '$sentiment',
                                    },
                                },
                            },
                            {
                                $group: {
                                    _id: {
                                        year: '$_id.year',
                                        day: '$_id.day',
                                    },
                                    positiveCount: {
                                        $sum: {
                                            $cond: [{ $eq: ['$_id.sentiment', 'positive'] }, 1, 0],
                                        },
                                    },
                                    negativeCount: {
                                        $sum: {
                                            $cond: [{ $eq: ['$_id.sentiment', 'negative'] }, 1, 0],
                                        },
                                    },
                                },
                            },
                            {
                                $project: {
                                    _id: 0,
                                    year: '$_id.year',
                                    day: '$_id.day',
                                    positiveCount: '$positiveCount',
                                    negativeCount: '$negativeCount',
                                },
                            },
                            {
                                $sort: {
                                    year: 1,
                                    day: 1,
                                },
                            },
                        ],
                        dataPerWeek: [
                            {
                                $group: {
                                    _id: {
                                        year: '$year',
                                        week: '$week',
                                        reviewSocialId: '$reviewSocialId',
                                        sentiment: '$sentiment',
                                    },
                                },
                            },
                            {
                                $group: {
                                    _id: {
                                        year: '$_id.year',
                                        week: '$_id.week',
                                    },
                                    positiveCount: {
                                        $sum: {
                                            $cond: [{ $eq: ['$_id.sentiment', 'positive'] }, 1, 0],
                                        },
                                    },
                                    negativeCount: {
                                        $sum: {
                                            $cond: [{ $eq: ['$_id.sentiment', 'negative'] }, 1, 0],
                                        },
                                    },
                                },
                            },
                            {
                                $project: {
                                    _id: 0,
                                    year: '$_id.year',
                                    week: '$_id.week',
                                    positiveCount: '$positiveCount',
                                    negativeCount: '$negativeCount',
                                },
                            },
                            {
                                $sort: {
                                    year: 1,
                                    week: 1,
                                },
                            },
                        ],
                        dataPerMonth: [
                            {
                                $group: {
                                    _id: {
                                        year: '$year',
                                        month: '$month',
                                        reviewSocialId: '$reviewSocialId',
                                        sentiment: '$sentiment',
                                    },
                                },
                            },
                            {
                                $group: {
                                    _id: {
                                        year: '$_id.year',
                                        month: '$_id.month',
                                    },
                                    positiveCount: {
                                        $sum: {
                                            $cond: [{ $eq: ['$_id.sentiment', 'positive'] }, 1, 0],
                                        },
                                    },
                                    negativeCount: {
                                        $sum: {
                                            $cond: [{ $eq: ['$_id.sentiment', 'negative'] }, 1, 0],
                                        },
                                    },
                                },
                            },
                            {
                                $project: {
                                    _id: 0,
                                    year: '$_id.year',
                                    month: '$_id.month',
                                    positiveCount: '$positiveCount',
                                    negativeCount: '$negativeCount',
                                },
                            },
                            {
                                $sort: {
                                    year: 1,
                                    month: 1,
                                },
                            },
                        ],
                    },
                },
            ],
            {
                readPreference: ReadPreferenceMode.SECONDARY_PREFERRED,
                comment: 'getTimeSeriesSegmentAnalyses',
            }
        );
    }

    toEntity(document: ISegmentAnalysisWithParentTopics): SegmentAnalysis {
        return new SegmentAnalysis({
            id: document._id.toString(),
            platformKey: document.platformKey,
            reviewSocialId: document.reviewSocialId,
            reviewSocialCreatedAt: document.reviewSocialCreatedAt,
            platformSocialId: document.platformSocialId,
            category: document.category,
            subcategory: document.subcategory ?? undefined,
            topic: document.topic,
            segment: document.segment,
            aiFoundSegment: document.aiFoundSegment,
            isRatingTagOrMenuItem: document.isRatingTagOrMenuItem,
            sentiment: document.sentiment,
            createdAt: document.createdAt,
            updatedAt: document.updatedAt,
            segmentAnalysisParentTopicIds: document.segmentAnalysisParentTopicIds?.map((id) => id.toString()) ?? [],
            segmentAnalysisParentTopics: document.segmentAnalysisParentTopics
                ? document.segmentAnalysisParentTopics.map(
                      (topic) =>
                          new SegmentAnalysisParentTopic({
                              id: topic._id.toString(),
                              restaurantId: topic.restaurantId.toString(),
                              mergedParentTopicNames: topic.mergedParentTopicNames,
                              isUserInput: topic.isUserInput,
                              category: topic.category,
                              subcategory: topic.subcategory,
                              sentiment: topic.sentiment ?? null,
                              translationsId: topic.translationsId?.toString(),
                              translations: topic.translations
                                  ? new Translations({
                                        id: topic.translations._id.toString(),
                                        fr: topic.translations.fr,
                                        en: topic.translations.en,
                                        es: topic.translations.es,
                                        it: topic.translations.it,
                                        language: topic.translations.language,
                                        source: topic.translations.source,
                                    })
                                  : undefined,
                              isFavorite: topic.isFavorite,
                              name: topic.name,
                              createdAt: topic.createdAt,
                              updatedAt: topic.updatedAt,
                          })
                  )
                : [],
        });
    }

    toDocument(entity: SegmentAnalysisProps): ISegmentAnalysis {
        return {
            _id: toDbId(entity.id!),
            platformKey: entity.platformKey,
            reviewSocialId: entity.reviewSocialId,
            reviewSocialCreatedAt: entity.reviewSocialCreatedAt,
            platformSocialId: entity.platformSocialId,
            category: entity.category,
            subcategory: entity.subcategory,
            topic: entity.topic,
            segment: entity.segment,
            aiFoundSegment: entity.aiFoundSegment,
            isRatingTagOrMenuItem: entity.isRatingTagOrMenuItem,
            sentiment: entity.sentiment,
            createdAt: entity.createdAt ?? new Date(),
            updatedAt: entity.updatedAt ?? new Date(),
            segmentAnalysisParentTopicIds: entity.segmentAnalysisParentTopicIds ? toDbIds(entity.segmentAnalysisParentTopicIds) : [],
        };
    }
}
