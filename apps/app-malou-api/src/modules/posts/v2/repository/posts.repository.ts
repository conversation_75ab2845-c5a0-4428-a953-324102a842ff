import { DateTime } from 'luxon';
import assert from 'node:assert';
import { singleton } from 'tsyringe';

import { GetMediaForEditionResponseDto } from '@malou-io/package-dto';
import {
    DbId,
    EntityRepository,
    IFeedback,
    IMedia,
    IPost,
    IPostPublicationError,
    OverwriteOrAssign,
    PostModel,
    ReadPreferenceMode,
    toDbId,
    toDbIds,
} from '@malou-io/package-models';
import {
    CallToActionType,
    getPublicationType,
    MalouErrorCode,
    mapLanguageStringToApplicationLanguage,
    MapstrCtaButtonType,
    MediaType,
    PlatformKey,
    PostFeedbacks,
    PostPublicationStatus,
    PostSource,
    PostType,
    Role,
    SeoPostTopic,
    SocialAttachmentsMediaTypes,
    SocialPostCallToAction,
    SocialPostsListFilter,
    TiktokPrivacyStatus,
} from '@malou-io/package-utils';

import { MalouError } from ':helpers/classes/malou-error';
import { GetMediaInfoService } from ':modules/media/services/get-media-thumbnail/get-media-thumbnail.service';
import { GetMediaForEditionService } from ':modules/media/use-cases/get-media-for-edition/get-media-for-edition.service';
import { SocialPostHashtag } from ':modules/posts/entities/social-post-hashtag.entity';
import { MalouPostData } from ':modules/posts/posts.interface';
import { PostAuthor } from ':modules/posts/v2/entities/author.entity';
import { FeedItem } from ':modules/posts/v2/entities/feed-item.entity';
import { PostToDuplicate } from ':modules/posts/v2/entities/post-to-duplicate.entity';
import { SeoPost } from ':modules/posts/v2/entities/seo-post.entity';
import { SocialPostItem } from ':modules/posts/v2/entities/social-post-item.entity';
import { SocialPostMedia } from ':modules/posts/v2/entities/social-post-media.entity';
import { PostHashtag, SocialPost } from ':modules/posts/v2/entities/social-post.entity';
import {
    IFeedItem,
    ISocialPostItem,
    ISocialPostWithAttachmentsAndFeedbacks,
    upsertSocialPostIKeys,
} from ':modules/posts/v2/repository/posts-repository.types';
import {
    countSocialPostsStages,
    errorStage,
    feedbackMessageCountProjectionStages,
    firstAttachmentPopulationStages,
    limitStage,
    platformsKeysProjectionStage,
    projectFeedItemStage,
    projectSocialPostItemStage,
    reelThumbnailPopulationStages,
    sortStage,
} from ':modules/posts/v2/repository/posts.pipeline';

const SORT_DATE_COMPUTATION = () => ({
    sortDate: { $ifNull: ['$socialCreatedAt', { $ifNull: ['$plannedPublicationDate', new Date()] }] },
});
const SET_SORT_DATE = () => ({ $set: SORT_DATE_COMPUTATION() });

@singleton()
export class PostsRepository extends EntityRepository<IPost> {
    constructor(
        private readonly _getMediaForEditionService: GetMediaForEditionService,
        private readonly _getMediaInfoService: GetMediaInfoService
    ) {
        super(PostModel);
    }

    findById(id: string): Promise<IPost | null> {
        return this.findOne({ filter: { _id: toDbId(id) }, options: { lean: true } });
    }

    findByIds(ids: string[]): Promise<IPost[]> {
        return this.find({ filter: { _id: { $in: toDbIds(ids) } }, options: { lean: true } });
    }

    updateFeedbackId({ postId, feedbackId }: { postId: string; feedbackId: string }): Promise<IPost | null> {
        return this.findOneAndUpdate({
            filter: { _id: toDbId(postId) },
            update: { feedbackId: toDbId(feedbackId) },
            options: { lean: true, new: true },
        });
    }

    async createSocialPost(data: SocialPost, restaurantId: string): Promise<void> {
        const post = await this.create({
            data: this._mapSocialPostToPostDocumentForCreation(data, { restaurantId: toDbId(restaurantId) }),
            options: { lean: true },
        });

        if (!post) {
            throw new MalouError(MalouErrorCode.POST_NOT_FOUND, { metadata: { data }, message: 'Creation failed' });
        }
    }

    /**
     * For reels: We only use the media ID (the 'id' field) of the property `post.reelThumbnail.media`.
     * All other fields of `post.reelThumbnail.media` are ignored and can even be invalid (we rely
     * on this in duplicate-post.use-case.ts).
     */
    async createMultipleSocialPosts(
        data: { post: SocialPost; restaurantId: string; duplicatedFromRestaurantId?: string }[]
    ): Promise<void> {
        await this.createMany({
            data: data.map(({ post, restaurantId, duplicatedFromRestaurantId }) =>
                this._mapSocialPostToPostDocumentForCreation(post, {
                    restaurantId: toDbId(restaurantId),
                    duplicatedFromRestaurantId: duplicatedFromRestaurantId ? toDbId(duplicatedFromRestaurantId) : undefined,
                })
            ),
            options: { lean: true },
        });
    }

    async createMultipleSeoPosts(data: { post: SeoPost; restaurantId: string; duplicatedFromRestaurantId?: string }[]): Promise<void> {
        await this.createMany({
            data: data.map(({ post, restaurantId, duplicatedFromRestaurantId }) =>
                this._mapSeoPostToPostDocumentForCreation(post, {
                    restaurantId: toDbId(restaurantId),
                    duplicatedFromRestaurantId: duplicatedFromRestaurantId ? toDbId(duplicatedFromRestaurantId) : undefined,
                })
            ),
            options: { lean: true },
        });
    }

    deleteSocialPostById(id: string): Promise<{ acknowledged: boolean; deletedCount: number }> {
        return this.deleteOne({ filter: { source: PostSource.SOCIAL, _id: toDbId(id) } });
    }

    async getPostsToDuplicate(postIds: string[]): Promise<PostToDuplicate[]> {
        const posts = await this.find({
            filter: { _id: { $in: toDbIds(postIds) } },
            options: { lean: true, populate: [{ path: 'attachments' }, { path: 'thumbnail' }] },
        });
        const postsToDuplicate: PostToDuplicate[] = [];
        for (const post of posts) {
            const postToDuplicate = await this._toPostToDuplicateEntity(post);
            postsToDuplicate.push(postToDuplicate);
        }
        return postsToDuplicate;
    }

    async updateSocialPost(id: string, postToUpdate: SocialPost, author: PostAuthor): Promise<SocialPost | null> {
        const update = this._socialPostToUpdateDocument(postToUpdate);
        const addAuthorToAuthors = {
            authors: {
                $concatArrays: [
                    {
                        $ifNull: ['$authors', []],
                    },
                    [
                        {
                            _id: toDbId(author.id),
                            name: author.name,
                            lastname: author.lastname,
                            picture: author.picture,
                        },
                    ],
                ],
            },
        };
        const updatePostStage = {
            $set: {
                ...update,
                ...addAuthorToAuthors,
            },
        };

        const setSortDateStage = SET_SORT_DATE();

        const post = await this.findOneAndUpdate({
            filter: { _id: toDbId(id) },
            update: [updatePostStage, setSortDateStage],
            projection: upsertSocialPostIKeys,
            options: {
                lean: true,
                populate: [{ path: 'feedback' }, { path: 'attachments' }],
            },
        });

        if (!post) {
            return null;
        }

        return this._toSocialPostEntity(post);
    }

    async updateSocialPostFromMalouPostData(id: string, malouPost: MalouPostData): Promise<void> {
        const updatePostStage = {
            $set: malouPost,
        };

        const setSortDateStage = SET_SORT_DATE();

        await this.findOneAndUpdate({
            filter: { _id: toDbId(id) },
            update: [updatePostStage, setSortDateStage],
            projection: upsertSocialPostIKeys,
            options: {
                lean: true,
                populate: [{ path: 'feedback' }, { path: 'attachments' }],
            },
        });
    }

    async getSocialPostById(id: string): Promise<SocialPost | null> {
        const post = await this.findOne({
            filter: { _id: toDbId(id) },
            projection: upsertSocialPostIKeys,
            options: { lean: true, populate: [{ path: 'feedback' }, { path: 'attachments' }] },
        });

        if (!post) {
            return null;
        }

        if (post.source !== PostSource.SOCIAL) {
            throw new MalouError(MalouErrorCode.POST_IS_NOT_SOCIAL_POST, { message: 'Post is not a social post', metadata: { post } });
        }

        return this._toSocialPostEntity(post);
    }

    async getSocialPostsByIds(ids: string[]): Promise<SocialPostItem[]> {
        const matchStage = {
            $match: {
                source: PostSource.SOCIAL,
                _id: { $in: toDbIds(ids) },
            },
        };

        const pipeline = [
            matchStage,
            ...feedbackMessageCountProjectionStages,
            platformsKeysProjectionStage,
            ...firstAttachmentPopulationStages,
            ...reelThumbnailPopulationStages,
            errorStage,
            projectSocialPostItemStage,
        ];
        const posts = (await this.aggregate(pipeline)) as ISocialPostItem[];

        const results: SocialPostItem[] = [];
        for (const post of posts) {
            results.push(await this._toSocialPostItemEntity(post));
        }
        return results;
    }

    async getSocialPosts(
        restaurantId: string,
        cursor: null | Date,
        limit: number,
        filter: SocialPostsListFilter | null
    ): Promise<SocialPostItem[]> {
        if (filter === SocialPostsListFilter.FEEDBACK) {
            return this._getSocialPostsWithFeedback(restaurantId, cursor, limit);
        }
        const dbFilter = this._getDbFilterFromSocialPostsListFilter(filter);
        const matchStage = {
            $match: {
                restaurantId: toDbId(restaurantId),
                source: PostSource.SOCIAL,
                isStory: { $ne: true },
                ...(dbFilter && { ...dbFilter }),
                ...(cursor && { sortDate: { $lt: cursor } }),
            },
        };

        const pipeline = [
            matchStage,
            sortStage,
            limitStage(limit),
            ...feedbackMessageCountProjectionStages,
            platformsKeysProjectionStage,
            ...firstAttachmentPopulationStages,
            ...reelThumbnailPopulationStages,
            errorStage,
            projectSocialPostItemStage,
        ];

        const posts = (await this.aggregate(pipeline)) as ISocialPostItem[];

        const results: SocialPostItem[] = [];
        for (const post of posts) {
            results.push(await this._toSocialPostItemEntity(post));
        }
        return results;
    }

    async getSocialPostsCounts(restaurantId: string): Promise<{ total?: number; draft?: number; error?: number; feedbacks?: number }> {
        const matchStage = {
            $match: {
                restaurantId: toDbId(restaurantId),
                source: PostSource.SOCIAL,
                isStory: { $ne: true },
            },
        };

        const pipeline = [matchStage, ...feedbackMessageCountProjectionStages, ...countSocialPostsStages()];

        const result = (await this.aggregate(pipeline, { readPreference: ReadPreferenceMode.SECONDARY_PREFERRED }))[0] as {
            total?: number;
            draft?: number;
            error?: number;
            feedbacks?: number;
        };
        return result;
    }

    async getPostIdsFromFilter(restaurantId: string, filter: SocialPostsListFilter): Promise<string[]> {
        const dbFilter = this._getDbFilterFromSocialPostsListFilter(filter);
        const matchStage = {
            $match: {
                restaurantId: toDbId(restaurantId),
                source: PostSource.SOCIAL,
                isStory: { $ne: true },
                ...(dbFilter && { ...dbFilter }),
            },
        };

        const pipeline = [matchStage, { $project: { _id: 1 } }];

        const result = (await this.aggregate(pipeline)) as { _id: DbId }[];
        return result.map((post) => post._id.toString());
    }

    async getFeed(restaurantId: string, cursor: null | Date, limit: number): Promise<FeedItem[]> {
        const matchStage = {
            $match: {
                restaurantId: toDbId(restaurantId),
                source: PostSource.SOCIAL,
                isStory: { $ne: true },
                $and: [
                    { $or: [{ key: PlatformKey.INSTAGRAM }, { keys: PlatformKey.INSTAGRAM }] },
                    { $or: [{ postType: { $ne: PostType.REEL } }, { isReelDisplayedInFeed: true }] },
                ],
                ...(cursor && { sortDate: { $lt: cursor } }),
            },
        };

        const pipeline = [
            matchStage,
            sortStage,
            limitStage(limit),
            ...firstAttachmentPopulationStages,
            ...reelThumbnailPopulationStages,
            projectFeedItemStage,
        ];

        const results = (await this.aggregate(pipeline)) as IFeedItem[];
        const feedItems: FeedItem[] = [];
        for (const result of results) {
            feedItems.push(await this._toFeedItemEntity(result));
        }
        return feedItems;
    }

    async getProgrammedSocialPostPlatformKeys(
        restaurantId: string
    ): Promise<{ id: string; platformKeys: PlatformKey[]; plannedPublicationDate: Date }[]> {
        const matchStage = {
            $match: {
                restaurantId: toDbId(restaurantId),
                source: PostSource.SOCIAL,
                published: PostPublicationStatus.PENDING,
            },
        };

        const pipeline = [
            matchStage,
            { $project: { keys: 1, key: 1, plannedPublicationDate: 1 } },
            {
                $addFields: {
                    platformKeys: { $cond: ['$key', ['$key'], '$keys'] },
                },
            },
            { $project: { platformKeys: 1, plannedPublicationDate: 1 } },
        ];

        const posts = (await this.aggregate(pipeline)) as { _id: DbId; platformKeys: PlatformKey[]; plannedPublicationDate: Date }[];

        return posts.map((post) => ({
            id: post._id.toString(),
            platformKeys: post.platformKeys,
            plannedPublicationDate: post.plannedPublicationDate,
        }));
    }

    async incrementTries(postId: string): Promise<void> {
        await this.updateOne({
            filter: { _id: toDbId(postId) },
            update: [
                {
                    $set: {
                        tries: {
                            $add: [{ $ifNull: ['$tries', 0] }, 1],
                        },
                    },
                },
            ],
        });
    }

    async updatePublicationStatus(postId: string, published: PostPublicationStatus): Promise<void> {
        const update: Partial<IPost> = { published };
        if (published !== PostPublicationStatus.PENDING) {
            update.isPublishing = false;
        }
        await this.updateOne({ filter: { _id: toDbId(postId) }, update });
    }

    async pushPublicationError(postId: string, error: IPostPublicationError): Promise<void> {
        await this.updateOne({
            filter: { _id: toDbId(postId) },
            update: [
                {
                    $set: {
                        publicationErrors: { $concatArrays: ['$publicationErrors', [error]] },
                        errorStage: 'publish post', // retro compat
                        errorData: error.code ?? error.data ?? '', // retro compat
                    },
                },
            ],
        });
    }

    async publishNow(id: string): Promise<void> {
        // We used to add +1 minute to the date, but it has been removed to avoid confusion
        const plannedPublicationDate = DateTime.now().toJSDate();
        const updateStage = { $set: { published: PostPublicationStatus.PENDING, plannedPublicationDate, isPublishing: true } }; // We anticipate the isPublishing state for UX reasons

        const setSortDateStage = SET_SORT_DATE();

        await this.updateOne({
            filter: { _id: toDbId(id) },
            update: [updateStage, setSortDateStage],
        });
    }

    async getRestaurantUserTagsHistory(restaurantId: string): Promise<string[]> {
        const pipeline = [
            {
                $match: {
                    restaurantId: toDbId(restaurantId),
                    key: PlatformKey.INSTAGRAM,
                    userTagsList: { $exists: true },
                },
            },
            {
                $project: { restaurantId: 1, userTagsList: 1 },
            },
            {
                $unwind: { path: '$userTagsList', preserveNullAndEmptyArrays: false },
            },
            {
                $unwind: { path: '$userTagsList', preserveNullAndEmptyArrays: false },
            },
            { $group: { _id: '$restaurantId', userTags: { $push: '$userTagsList.username' } } },
        ];

        const result = (await this.aggregate(pipeline)) as { _id: DbId; userTags: string[] }[];
        return result[0]?.userTags ?? [];
    }

    async getRestaurantInstagramCollaboratorsHistory(restaurantId: string): Promise<string[]> {
        const pipeline = [
            {
                $match: {
                    restaurantId: toDbId(restaurantId),
                    key: PlatformKey.INSTAGRAM,
                    instagramCollaboratorsUsernames: { $exists: true },
                },
            },
            {
                $project: { restaurantId: 1, instagramCollaboratorsUsernames: 1 },
            },
            {
                $unwind: { path: '$instagramCollaboratorsUsernames', preserveNullAndEmptyArrays: false },
            },
            { $group: { _id: '$restaurantId', instagramCollaborators: { $push: '$instagramCollaboratorsUsernames' } } },
        ];

        const result = (await this.aggregate(pipeline)) as { _id: DbId; instagramCollaborators: string[] }[];
        return result[0]?.instagramCollaborators ?? [];
    }

    private async _toSocialPostEntity(post: ISocialPostWithAttachmentsAndFeedbacks): Promise<SocialPost> {
        const publicationType = getPublicationType(post.postType, post.isStory);
        const attachments = post.attachments ?? [];
        const medias = await Promise.all(
            attachments.map((attachment) => this._getMediaForEditionService.getMedia(attachment._id.toString(), publicationType))
        );

        let reelThumbnailMedia: GetMediaForEditionResponseDto | undefined = undefined;
        if (post.thumbnail) {
            reelThumbnailMedia = await this._getMediaForEditionService.getMedia(post.thumbnail.toString(), publicationType);
        }

        return new SocialPost({
            id: post._id.toString(),
            title: post.title,
            text: post.text ?? '',
            platformKeys: post.key ? [post.key] : post.keys,
            published: post.published,
            isPublishing: post.isPublishing ?? false,
            postType: post.postType,
            plannedPublicationDate: post.plannedPublicationDate ?? new Date(),
            attachments: medias,
            hashtags: this._mapIPostHashtagsToPostHashtags(post.hashtags),
            location: post.location ?? undefined,
            feedbacks: this._mapFeedbacksToPostFeedbacks(post.feedback),
            callToAction: this._mapCallToActionToSocialPostCallToAction(post.callToAction),
            error:
                post.errorStage && post.errorData
                    ? {
                          code: post.errorStage,
                          rawData: post.errorData,
                      }
                    : undefined,
            socialLink: post.socialLink,
            socialCreatedAt: post.socialCreatedAt,
            author: post.author
                ? new PostAuthor({
                      id: post.author._id.toString(),
                      name: post.author.name,
                      lastname: post.author.lastname,
                      picture: post.author.picture ?? undefined,
                  })
                : undefined,
            userTagsList: post.userTagsList ?? [],
            bindingId: post.bindingId ?? undefined,
            tiktokOptions: post.tiktokOptions ?? {
                privacyStatus: TiktokPrivacyStatus.PUBLIC_TO_EVERYONE,
                interactionAbility: {
                    comment: false,
                    duet: false,
                    stitch: false,
                },
                contentDisclosureSettings: {
                    isActivated: false,
                    yourBrand: false,
                    brandedContent: false,
                },
            },
            reelThumbnail: post.thumbnailOffsetTimeInMs
                ? {
                      type: 'videoFrame',
                      thumbnailOffsetTimeInMs: post.thumbnailOffsetTimeInMs,
                      media: reelThumbnailMedia,
                  }
                : reelThumbnailMedia
                  ? { type: 'custom', media: reelThumbnailMedia }
                  : undefined,
            instagramCollaboratorsUsernames: post.instagramCollaboratorsUsernames,
            createdFromDeviceType: post.createdFromDeviceType,
        });
    }

    private async _getSocialPostsWithFeedback(restaurantId: string, cursor: null | Date, limit: number): Promise<SocialPostItem[]> {
        const matchStage = {
            $match: {
                restaurantId: toDbId(restaurantId),
                source: PostSource.SOCIAL,
                feedbackId: { $exists: true },
                ...(cursor && { sortDate: { $lt: cursor } }),
            },
        };
        const secondMatchStage = {
            $match: {
                feedbackMessageCount: { $gt: 0 },
            },
        };

        const pipeline = [
            matchStage,
            ...feedbackMessageCountProjectionStages,
            secondMatchStage,
            sortStage,
            limitStage(limit),
            platformsKeysProjectionStage,
            ...firstAttachmentPopulationStages,
            errorStage,
            projectSocialPostItemStage,
        ];
        const posts = (await this.aggregate(pipeline)) as ISocialPostItem[];

        const results: SocialPostItem[] = [];
        for (const post of posts) {
            results.push(await this._toSocialPostItemEntity(post));
        }
        return results;
    }

    private _getDbFilterFromSocialPostsListFilter(
        filter: SocialPostsListFilter | null
    ): { published: PostPublicationStatus; createdAt?: { $gt: Date } } | null {
        switch (filter) {
            case SocialPostsListFilter.DRAFT:
                return { published: PostPublicationStatus.DRAFT };
            case SocialPostsListFilter.ERROR:
                return { published: PostPublicationStatus.ERROR, createdAt: { $gt: DateTime.now().minus({ months: 6 }).toJSDate() } };
            default:
                return null;
        }
    }

    private async _toSocialPostItemEntity(post: ISocialPostItem): Promise<SocialPostItem> {
        return new SocialPostItem({
            id: post._id.toString(),
            text: post.text ?? '',
            platformKeys: post.platformKeys,
            published: post.published,
            isPublishing: post.isPublishing ?? false,
            postType: post.postType,
            feedbackMessageCount: post.feedbackMessageCount,
            plannedPublicationDate: post.plannedPublicationDate ?? undefined,
            media: await this._getMediaFromISocialPostItem(post),
            hashtags: this._mapIPostHashtagsToSocialPostHashtags(post.hashtags),
            socialLink: post.socialLink,
            socialCreatedAt: post.socialCreatedAt,
            sortDate: post.sortDate ?? post.socialCreatedAt ?? post.plannedPublicationDate ?? new Date(),
            author: post.author
                ? new PostAuthor({
                      id: post.author._id.toString(),
                      name: post.author.name,
                      lastname: post.author.lastname,
                      picture: post.author.picture ?? undefined,
                  })
                : undefined,
            mostRecentPublicationErrorCode: post.mostRecentPublicationErrorCode,
            bindingId: post.bindingId,
            createdFromDeviceType: post.createdFromDeviceType,
        });
    }

    private async _toPostToDuplicateEntity(
        post: OverwriteOrAssign<IPost, { attachments: IMedia[]; thumbnail?: IMedia }>
    ): Promise<PostToDuplicate> {
        let medias: SocialPostMedia[] = [];
        if ((post.socialAttachments?.length ?? 0) > (post.attachments?.length ?? 0)) {
            medias =
                post.socialAttachments?.map(
                    (socialAttachment) =>
                        new SocialPostMedia({
                            url: socialAttachment.urls.original,
                            type: this._socialAttachmentsMediaTypesToMediaType(socialAttachment.type),
                            thumbnailUrl: socialAttachment.thumbnailUrl ?? undefined,
                            thumbnailDimensions: undefined,
                            transformData: undefined,
                        })
                ) ?? [];
        } else {
            if (post.postType === PostType.REEL && post.thumbnail) {
                const media = await this._IMediaToSocialPostMedia(post.thumbnail);
                if (media) {
                    medias.push(media);
                }
            } else {
                for (const attachment of post.attachments) {
                    const media = await this._IMediaToSocialPostMedia(attachment);
                    if (media) {
                        medias.push(media);
                    }
                }
            }
        }

        assert(post.bindingId);

        return new PostToDuplicate({
            id: post._id.toString(),
            bindingId: post.bindingId,
            postType: post.postType,
            source: post.source,
            platformKeys: post.key ? [post.key] : post.keys,
            published: post.published,
            text: post.text ?? '',
            plannedPublicationDate: post.plannedPublicationDate ?? null,
            medias: medias ?? [],
            language: post.language ? mapLanguageStringToApplicationLanguage(post.language) : null,
            hashtags: this._mapIPostHashtagsToSocialPostHashtags(post.hashtags),
            callToAction: post.callToAction,
            socialCreatedAt: post.socialCreatedAt ?? null,
        });
    }

    private async _toFeedItemEntity(feedItem: IFeedItem): Promise<FeedItem> {
        return new FeedItem({
            postId: feedItem._id.toString(),
            media: await this._getMediaFromISocialPostItem(feedItem),
            published: feedItem.published,
            postType: feedItem.postType,
            plannedPublicationDate: feedItem.plannedPublicationDate ?? null,
            socialCreatedAt: feedItem.socialCreatedAt,
            updatedAt: feedItem.updatedAt,
            sortDate: feedItem.sortDate ?? feedItem.socialCreatedAt ?? feedItem.plannedPublicationDate ?? new Date(),
        });
    }

    /** Returns the thumbnail to display in the post list or in the feed */
    private async _getMediaFromISocialPostItem(item: {
        firstAttachment?: IMedia;
        socialAttachments?: IPost['socialAttachments'];
        reelThumbnail?: IMedia;
    }): Promise<SocialPostMedia | null> {
        // we always prefer `socialAttachments[0].thumbnailUrl` over thumbnail/reelThumbnail
        // because:
        //  - if the reel is published, `socialAttachments` is always set and:
        //      - reelThumbnail will no longer be updated because it’s not possible to update
        //      published reels on Malou
        //      - if the reel is updated on the external platform, socialAttachments[0].thumbnailUrl
        //      will be updated and we wand to reflect these changes on the Malou app
        //  - if the reel is not published, socialAttachments is not set and we must use
        //  reelThumbnail (the Malou thumbnail) instead.

        if (item.socialAttachments?.[0]) {
            return new SocialPostMedia({
                url: item.socialAttachments[0].urls.original,
                type: this._socialAttachmentsMediaTypesToMediaType(item.socialAttachments[0].type),
                thumbnailUrl: item.socialAttachments[0].thumbnailUrl ?? undefined,
                thumbnailDimensions: undefined,
                transformData: undefined,
            });
        } else if (item.reelThumbnail) {
            return await this._IMediaToSocialPostMedia(item.reelThumbnail);
        } else if (item.firstAttachment) {
            return await this._IMediaToSocialPostMedia(item.firstAttachment);
        }
        return null;
    }

    private async _IMediaToSocialPostMedia(media: IMedia): Promise<SocialPostMedia | null> {
        const urlAndDimensions = this._getMediaInfoService.getUrlAndDimensions(media);
        const thumbnail256OutsideUrlAndDimensions = await this._getMediaInfoService.getThumbnail256OutsideUrlAndDimensions(media);

        if (!urlAndDimensions || !thumbnail256OutsideUrlAndDimensions) {
            return null;
        }

        return new SocialPostMedia({
            url: urlAndDimensions.url,
            type: media.type,
            thumbnailUrl: thumbnail256OutsideUrlAndDimensions.url,
            thumbnailDimensions: thumbnail256OutsideUrlAndDimensions.dimensions,
            transformData: media.transformData,
        });
    }

    private _socialAttachmentsMediaTypesToMediaType(socialAttachmentsMediaType: SocialAttachmentsMediaTypes): MediaType {
        switch (socialAttachmentsMediaType) {
            case SocialAttachmentsMediaTypes.IMAGE:
                return MediaType.PHOTO;
            case SocialAttachmentsMediaTypes.VIDEO:
                return MediaType.VIDEO;
        }
    }

    private _mapIPostHashtagsToSocialPostHashtags(hashtags: IPost['hashtags']):
        | {
              selected: SocialPostHashtag[];
              suggested: SocialPostHashtag[];
          }
        | undefined {
        return hashtags
            ? {
                  selected:
                      hashtags.selected?.map(
                          (h) =>
                              new SocialPostHashtag({
                                  id: h._id ? h._id.toString() : (h as any).id, // TODO remove this when hashtags are no longer dirty in db
                                  text: h.text,
                                  isMain: h.isMain,
                                  isCustomerInput: h.isCustomerInput,
                                  type: h.type,
                                  createdAt: h.createdAt,
                                  updatedAt: h.updatedAt,
                              })
                      ) ?? [],
                  suggested:
                      hashtags.suggested?.map(
                          (h) =>
                              new SocialPostHashtag({
                                  id: h._id ? h._id.toString() : (h as any).id, // TODO remove this when hashtags are no longer dirty in db
                                  text: h.text,
                                  isMain: h.isMain,
                                  isCustomerInput: h.isCustomerInput,
                                  type: h.type,
                                  createdAt: h.createdAt,
                                  updatedAt: h.updatedAt,
                              })
                      ) ?? [],
              }
            : undefined;
    }

    private _mapIPostHashtagsToPostHashtags(
        hashtags: IPost['hashtags']
    ): { selected: PostHashtag[]; suggested: PostHashtag[] } | undefined {
        return hashtags
            ? {
                  selected:
                      hashtags.selected?.map((h) => ({
                          id: h._id?.toString() ?? (h as any).id ?? '', // TODO must investigate why sometimes _id is not present
                          text: h.text,
                          isMain: h.isMain,
                          isCustomerInput: h.isCustomerInput,
                          type: h.type,
                          createdAt: h.createdAt,
                          updatedAt: h.updatedAt,
                      })) ?? [],
                  suggested:
                      hashtags.suggested?.map((h) => ({
                          id: h._id?.toString() ?? (h as any).id ?? '', // TODO must investigate why sometimes _id is not present
                          text: h.text,
                          isMain: h.isMain,
                          isCustomerInput: h.isCustomerInput,
                          type: h.type,
                          createdAt: h.createdAt,
                          updatedAt: h.updatedAt,
                      })) ?? [],
              }
            : undefined;
    }

    /**
     * For reels: We only use the media ID (the field ID) of the property `post.reelThumbnail.media`.
     * All other fields of `post.reelThumbnail.media` are ignored and can even be invalid (we rely
     * on this in duplicate-post.use-case.ts).
     */
    private _mapSocialPostToPostDocumentForCreation(post: SocialPost, partialData: Partial<IPost> & { restaurantId: DbId }): IPost {
        return {
            _id: toDbId(post.id),
            keys: post.platformKeys,
            postType: post.postType,
            published: post.published,
            source: PostSource.SOCIAL,
            title: post.title,
            text: post.text,
            isStory: false,
            plannedPublicationDate: post.plannedPublicationDate,
            shouldDuplicateInOtherPlatforms: false,
            postTopic: SeoPostTopic.STANDARD,
            feedbackId: post.feedbacks ? toDbId(post.feedbacks.id) : undefined,
            attachments: toDbIds(post.attachments.map((attachment) => attachment.id)),
            location: post.location ?? null,
            callToAction: post.callToAction ?? null,
            userTagsList: post.userTagsList,
            hashtags: post.hashtags
                ? {
                      selected: post.hashtags.selected.map((hashtag) => ({
                          _id: toDbId(hashtag.id),
                          text: hashtag.text,
                          isMain: hashtag.isMain,
                          isCustomerInput: hashtag.isCustomerInput,
                          type: hashtag.type,
                          createdAt: new Date(),
                          updatedAt: new Date(),
                      })),
                      suggested: post.hashtags.suggested.map((hashtag) => ({
                          _id: toDbId(hashtag.id),
                          text: hashtag.text,
                          isMain: hashtag.isMain,
                          isCustomerInput: hashtag.isCustomerInput,
                          type: hashtag.type,
                          createdAt: new Date(),
                          updatedAt: new Date(),
                      })),
                  }
                : undefined,
            ...(post.author && {
                author: {
                    _id: toDbId(post.author.id),
                    name: post.author.name,
                    lastname: post.author.lastname,
                    picture: post.author.picture,
                },
            }),
            createdAt: new Date(),
            updatedAt: new Date(),
            sortDate: post.plannedPublicationDate ?? new Date(),
            thumbnail: post.reelThumbnail?.media?.id ? toDbId(post.reelThumbnail.media.id) : undefined,
            thumbnailOffsetTimeInMs: post.reelThumbnail?.type === 'videoFrame' ? post.reelThumbnail.thumbnailOffsetTimeInMs : undefined,
            createdFromDeviceType: post.createdFromDeviceType,
            ...partialData,
        };
    }

    private _mapSeoPostToPostDocumentForCreation(post: SeoPost, partialData: Partial<IPost> & { restaurantId: DbId }): IPost {
        return {
            _id: toDbId(post.id),
            text: post.text,
            keys: [post.key],
            published: post.published,
            postType: post.postType,
            postTopic: post.postTopic,
            event: post.event,
            offer: post.offer,
            plannedPublicationDate: post.plannedPublicationDate,
            attachments: toDbIds(post.attachments.map((attachment) => attachment.id)),
            callToAction: post.callToAction ?? null,
            feedbackId: post.feedbacks ? toDbId(post.feedbacks.id) : undefined,
            shouldDuplicateInOtherPlatforms: false,
            source: PostSource.SEO,
            isStory: false,
            ...(post.author && {
                author: {
                    _id: toDbId(post.author.id),
                    name: post.author.name,
                    lastname: post.author.lastname,
                    picture: post.author.picture,
                },
            }),
            createdAt: new Date(),
            updatedAt: new Date(),
            sortDate: post.plannedPublicationDate ?? new Date(),
            createdFromDeviceType: post.createdFromDeviceType,
            ...partialData,
        };
    }

    private _mapFeedbacksToPostFeedbacks(feedbacks: IFeedback | null): PostFeedbacks | undefined {
        if (!feedbacks) {
            return undefined;
        }

        return {
            id: feedbacks._id.toString(),
            isOpen: feedbacks.isOpen,
            feedbackMessages: feedbacks.feedbackMessages.map((feedbackMessage) => ({
                id: feedbackMessage._id.toString(),
                text: feedbackMessage.text,
                type: feedbackMessage.type,
                visibility: feedbackMessage.visibility,
                author: feedbackMessage.author
                    ? {
                          id: feedbackMessage.author._id.toString(),
                          name: feedbackMessage.author.name,
                          lastname: feedbackMessage.author.lastname,
                          profilePictureUrl: feedbackMessage.author.profilePictureUrl,
                          email: feedbackMessage.author.email,
                          role: this._mapRoleStringToRole(feedbackMessage.author.role) ?? Role.MALOU_BASIC,
                      }
                    : {
                          id: '',
                          name: '',
                          lastname: '',
                          profilePictureUrl: undefined,
                          email: '',
                          role: Role.MALOU_BASIC,
                      },
                createdAt: feedbackMessage.createdAt,
                updatedAt: feedbackMessage.updatedAt ?? feedbackMessage.createdAt, // retro compatibility, some feedback messages have no updatedAt in db
                publishedAt: feedbackMessage.publishedAt,
                lastUpdatedAt: feedbackMessage.lastUpdatedAt,
            })),
            participants: feedbacks.participants.map((participant) => ({
                participant: {
                    id: participant.participant._id.toString(),
                    name: participant.participant.name,
                    lastname: participant.participant.lastname,
                    email: participant.participant.email,
                    role: this._mapRoleStringToRole(participant.participant.role),
                },
                types: participant.types,
            })),
            createdAt: feedbacks.createdAt,
            updatedAt: feedbacks.updatedAt,
        };
    }

    private _mapRoleStringToRole(role?: string): Role | undefined {
        if (Object.values(Role).includes(role as any)) {
            return role as Role;
        }
        return undefined;
    }

    private _mapCallToActionToSocialPostCallToAction(callToAction: IPost['callToAction']): SocialPostCallToAction | undefined {
        if (!callToAction || !callToAction.actionType || !callToAction.url) {
            return undefined;
        }

        return this._isMapstrCtaButtonType(callToAction.actionType)
            ? {
                  actionType: callToAction.actionType,
                  url: callToAction.url,
              }
            : undefined;
    }

    private _isMapstrCtaButtonType(actionType: MapstrCtaButtonType | CallToActionType | undefined): actionType is MapstrCtaButtonType {
        return Object.values(MapstrCtaButtonType).includes(actionType as any);
    }

    /**
     * Only keep the fields that need to be updated in the document
     */
    private _socialPostToUpdateDocument(post: SocialPost): Omit<
        IPost,
        | 'createdAt'
        | 'updatedAt'
        | 'hashtags'
        | 'restaurantId'
        | 'source'
        | 'postTopic'
        | 'isStory'
        | 'author'
        | 'shouldDuplicateInOtherPlatforms'
    > & {
        hashtags?: {
            selected?: Omit<NonNullable<NonNullable<IPost['hashtags']>['selected']>[0], 'createdAt' | 'updatedAt'>[];
            suggested?: Omit<NonNullable<NonNullable<IPost['hashtags']>['suggested']>[0], 'createdAt' | 'updatedAt'>[];
        };
    } {
        const reelThumbnailMediaId: string | undefined = post.reelThumbnail?.media?.id;
        return {
            _id: toDbId(post.id),
            keys: post.platformKeys,
            postType: post.postType,
            published: post.published,
            text: post.text,
            title: post.title ?? undefined,
            plannedPublicationDate: post.plannedPublicationDate,
            attachments: toDbIds(post.attachments.map((attachment) => attachment.id)),
            hashtags: post.hashtags
                ? {
                      selected: post.hashtags.selected.map((hashtag) => ({
                          _id: toDbId(hashtag.id),
                          text: hashtag.text,
                          isMain: hashtag.isMain,
                          isCustomerInput: hashtag.isCustomerInput,
                          type: hashtag.type,
                      })),
                      suggested: post.hashtags.suggested.map((hashtag) => ({
                          _id: toDbId(hashtag.id),
                          text: hashtag.text,
                          isMain: hashtag.isMain,
                          isCustomerInput: hashtag.isCustomerInput,
                          type: hashtag.type,
                      })),
                  }
                : undefined,
            location: post.location ?? null,
            callToAction: post.callToAction ?? null,
            feedbackId: post.feedbacks ? toDbId(post.feedbacks.id) : null,
            errorStage: post.error?.code ?? null,
            errorData: post.error?.rawData ?? null,
            socialLink: post.socialLink ?? undefined,
            socialCreatedAt: post.socialCreatedAt ?? undefined,
            userTagsList: post.userTagsList,
            isPublishing: post.isPublishing,
            tiktokOptions: post.tiktokOptions,
            thumbnail: reelThumbnailMediaId ? toDbId(reelThumbnailMediaId) : undefined,
            thumbnailOffsetTimeInMs: post.reelThumbnail?.type === 'videoFrame' ? post.reelThumbnail.thumbnailOffsetTimeInMs : undefined,
            instagramCollaboratorsUsernames: post.instagramCollaboratorsUsernames,
        };
    }
}
