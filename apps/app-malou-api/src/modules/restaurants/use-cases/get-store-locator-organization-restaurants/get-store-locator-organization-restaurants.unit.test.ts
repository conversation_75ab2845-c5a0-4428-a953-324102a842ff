import { container } from 'tsyringe';

import { StoreLocatorOrganizationRestaurantDto } from '@malou-io/package-dto';
import { MalouAttributesEnum, PlatformKey, RestaurantAttributeValue } from '@malou-io/package-utils';

import { registerRepositories, TestCaseBuilderV2 } from ':helpers/tests/testing-utils';
import { getDefaultAttribute, getDefaultRestaurantAttribute } from ':modules/attributes/tests/attribute.builder';
import { getDefaultOrganization } from ':modules/organizations/organization.builder';
import { getDefaultRestaurant } from ':modules/restaurants/tests/restaurant.builder';
import { GetStoreLocatorOrganizationRestaurantsUseCase } from ':modules/restaurants/use-cases/get-store-locator-organization-restaurants/get-store-locator-organization-restaurants.use-case';

describe('GetStoreLocatorOrganizationRestaurantsUseCase', () => {
    beforeAll(() => {
        registerRepositories([
            'RestaurantsRepository',
            'OrganizationsRepository',
            'AttributesRepository',
            'RestaurantAttributesRepository',
        ]);
    });

    it('should get restaurants by organization id with name, internalName, address and populated categories', async () => {
        const testCase = new TestCaseBuilderV2<'organizations' | 'attributes' | 'restaurantAttributes' | 'restaurants'>({
            seeds: {
                organizations: {
                    data() {
                        return [
                            getDefaultOrganization().name('Test Organization').build(),
                            getDefaultOrganization().name('Other Organization').build(),
                        ];
                    },
                },
                restaurants: {
                    data(dependencies) {
                        return [
                            getDefaultRestaurant()
                                .name('Restaurant 1')
                                .internalName('Internal Name 1')
                                .organizationId(dependencies.organizations()[0]._id)
                                .active(true)
                                .build(),
                            getDefaultRestaurant()
                                .name('Restaurant 2')
                                .internalName('Internal Name 2')
                                .organizationId(dependencies.organizations()[0]._id)
                                .active(true)
                                .build(),
                            getDefaultRestaurant()
                                .name('Inactive Restaurant')
                                .organizationId(dependencies.organizations()[0]._id)
                                .active(false)
                                .build(),
                            getDefaultRestaurant()
                                .name('Different Org Restaurant')
                                .organizationId(dependencies.organizations()[1]._id)
                                .active(true)
                                .build(),
                        ];
                    },
                },
                attributes: {
                    data() {
                        return [
                            getDefaultAttribute()
                                .attributeId(MalouAttributesEnum.SERVES_ORGANIC)
                                .attributeName({ fr: 'bio' })
                                .platformKey(PlatformKey.GMB)
                                .build(),
                            getDefaultAttribute()
                                .attributeId(MalouAttributesEnum.SERVES_VEGAN)
                                .attributeName({ fr: 'vegan friendly' })
                                .platformKey(PlatformKey.GMB)
                                .build(),
                            getDefaultAttribute()
                                .attributeId('attribute_id_3')
                                .attributeName({ fr: 'Attribute 3' })
                                .platformKey(PlatformKey.GMB)
                                .build(),
                            getDefaultAttribute()
                                .attributeId('attribute_id_4')
                                .attributeName({ fr: 'Attribute 4' })
                                .platformKey(PlatformKey.YELP)
                                .build(),
                            getDefaultAttribute()
                                .attributeId('attribute_id_5')
                                .attributeName({ fr: 'Attribute 5' })
                                .platformKey(PlatformKey.GMB)
                                .build(),
                        ];
                    },
                },
                restaurantAttributes: {
                    data(dependencies) {
                        return [
                            getDefaultRestaurantAttribute()
                                .attributeId(dependencies.attributes()[0]._id)
                                .restaurantId(dependencies.restaurants()[0]._id)
                                .build(),
                            getDefaultRestaurantAttribute()
                                .attributeId(dependencies.attributes()[1]._id)
                                .restaurantId(dependencies.restaurants()[0]._id)
                                .build(),
                            // not returned: RestaurantAttributeValue NO
                            getDefaultRestaurantAttribute()
                                .attributeId(dependencies.attributes()[2]._id)
                                .restaurantId(dependencies.restaurants()[0]._id)
                                .attributeValue(RestaurantAttributeValue.NO)
                                .build(),
                            // not returned: platformKey different from GMB
                            getDefaultRestaurantAttribute()
                                .attributeId(dependencies.attributes()[3]._id)
                                .restaurantId(dependencies.restaurants()[0]._id)
                                .attributeValue(RestaurantAttributeValue.YES)
                                .build(),
                            // not returned: not in RELEVANT_ATTRIBUTES
                            getDefaultRestaurantAttribute()
                                .attributeId(dependencies.attributes()[4]._id)
                                .restaurantId(dependencies.restaurants()[0]._id)
                                .build(),
                        ];
                    },
                },
            },
            expectedResult(dependencies): StoreLocatorOrganizationRestaurantDto[] {
                const attributes = dependencies.attributes;
                const restaurants = dependencies.restaurants;

                return [
                    {
                        id: restaurants[0]._id.toString(),
                        name: restaurants[0].name,
                        internalName: restaurants[0].internalName,
                        address: restaurants[0].address || undefined,
                        type: restaurants[0].type,
                        attributeList: [
                            {
                                id: attributes[0]._id.toString(),
                                priority: 1, // MalouAttributesEnum.SERVES_ORGANIC has priority 1
                                attributeId: attributes[0].attributeId,
                                platformKey: attributes[0].platformKey,
                                attributeName: {
                                    fr: attributes[0]?.attributeName.fr ?? '',
                                    en: attributes[0]?.attributeName.en ?? undefined,
                                    es: attributes[0]?.attributeName.es ?? undefined,
                                    it: attributes[0]?.attributeName.it ?? undefined,
                                },
                            },
                            {
                                id: attributes[1]._id.toString(),
                                priority: 2, // MalouAttributesEnum.SERVES_VEGAN has priority 2
                                attributeId: attributes[1].attributeId,
                                platformKey: attributes[1].platformKey,
                                attributeName: {
                                    fr: attributes[1]?.attributeName.fr ?? '',
                                    en: attributes[1]?.attributeName.en ?? undefined,
                                    es: attributes[1]?.attributeName.es ?? undefined,
                                    it: attributes[1]?.attributeName.it ?? undefined,
                                },
                            },
                        ],
                    },
                    {
                        id: restaurants[1]._id.toString(),
                        name: restaurants[1].name,
                        type: restaurants[1].type,
                        internalName: restaurants[1].internalName,
                        address: restaurants[1].address || undefined,
                        attributeList: [],
                    },
                ];
            },
        });

        await testCase.build();
        const seededObjects = testCase.getSeededObjects();
        const expectedResult = testCase.getExpectedResult();

        const useCase = container.resolve(GetStoreLocatorOrganizationRestaurantsUseCase);

        const organizationId = seededObjects.organizations[0]._id.toString();

        const result = await useCase.execute(organizationId);

        expect(result).toHaveLength(2);
        expect(result).toEqual(expectedResult);
    });
});
