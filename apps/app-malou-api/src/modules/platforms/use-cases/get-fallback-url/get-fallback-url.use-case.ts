import assert from 'node:assert';
import { singleton } from 'tsyringe';

import { PlatformKey } from '@malou-io/package-utils';

import { SevenroomsCredentialRepository } from ':modules/credentials/platforms/sevenrooms/sevenrooms.repository';
import PlatformsRepository from ':modules/platforms/platforms.repository';
import { SevenroomsProvider } from ':providers/sevenrooms/sevenrooms.provider';

@singleton()
export class GetFallbackUrlUseCase {
    constructor(
        private readonly _platformsRepository: PlatformsRepository,
        private readonly _sevenroomsProvider: SevenroomsProvider,
        private readonly _sevenroomsCredentialRepository: SevenroomsCredentialRepository
    ) {}

    async execute(platformKey: PlatformKey, restaurantId: string): Promise<string | null> {
        const platform = await this._platformsRepository.getPlatformByRestaurantIdAndPlatformKey(restaurantId, platformKey);
        const socialId = platform?.socialId;
        if (!socialId) {
            return null;
        }
        if (platformKey === PlatformKey.SEVENROOMS) {
            const sevenroomsCredential = await this._sevenroomsCredentialRepository.getSuperCredential();
            assert(sevenroomsCredential, 'Missing Sevenrooms credential');
            return this._sevenroomsProvider.getReservationUrl(sevenroomsCredential);
        }
        return null;
    }
}
