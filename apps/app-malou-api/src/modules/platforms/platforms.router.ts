import { NextFunction, Request, Response, Router } from 'express';
import { singleton } from 'tsyringe';

import {
    DeletePlatformByRestaurantIdAndPlatformKeyParamsDto,
    GetPlatformParamsDto,
    GetPlatformsByRestaurantIdsBodyDto,
    GetPlatformsForRestaurantParamsDto,
    GetPlatformsForRestaurantQueryDto,
    PullOverviewPlatformParamsDto,
    PullPlatformQueryDto,
    UpsertPlatformBodyDto,
} from '@malou-io/package-dto';
import { PlatformKey, Role } from '@malou-io/package-utils';

import AbstractRouter from ':helpers/abstracts/abstract-router';
import { casl } from ':helpers/casl/middlewares';
import { RequestWithExperimentation, RequestWithPermissions, RequestWithUser } from ':helpers/utils.types';
import PlatformsController from ':modules/platforms/platforms.controller';
import { authorize } from ':plugins/passport';
import { experimentationMiddleware } from ':services/experimentations-service/experimentation.service';

@singleton()
export default class PlatformsRouter extends AbstractRouter {
    constructor(private readonly _platformsController: PlatformsController) {
        super();
    }

    init(): Router {
        /**
         * Delete platform
         */
        this.router.delete('/:platform_id', authorize(), (req, res, next) =>
            this._platformsController.handleDeletePlatform(req, res, next)
        );

        /**
         *
         * @api {patch} /platforms Upserts a platform for a specific restaurant
         * @apiName PatchPlatform
         * @apiGroup Platforms
         *
         * @apiParam {string} restaurant_id Restaurant's unique ID.
         * @apiParam {Object} platform Platform's data.
         * @apiParamExample {json}
         * {
         *    restaurant_id: '1234'
         *    platform: {
         *        key: 'facebook',
         *        socialId '123456'
         *        name: 'Restal'
         *    }
         * }
         *
         * @apiSuccess {string} msg
         *
         * @apiSuccessExample Success-Response:
         *     HTTP/1.1 200 OK
         *     {
         *       "msg": "Successfully upserted platform"
         *     }
         *
         * @apiError MissingParameter Missing parameter, need restaurant_id and platform_key.
         * @apiErrorExample {json} Error-Response:
         *     HTTP/1.1 400 Bad Request
         *     {
         *       "msg": {
         *            error: true,
         *            message: 'missing_param'
         *            errorData: '{}'
         *        }
         *     }
         */
        this.router.patch('/', authorize(), casl(), (req: RequestWithPermissions<any, any, UpsertPlatformBodyDto>, res, next) =>
            this._platformsController.handleUpsertPlatform(req, res, next)
        );

        this.router.get(
            '/restaurants/:restaurant_id/disconnected',
            authorize(),
            (
                req: RequestWithUser<GetPlatformsForRestaurantParamsDto, never, never, GetPlatformsForRestaurantQueryDto>,
                res: Response,
                next: NextFunction
            ) => this._platformsController.handleGetDisconnectedPlatformsForRestaurant(req, res, next)
        );

        /**
         *
         * @api {get} /platforms/restaurants/:restaurant_id Get platforms for a specific restaurant
         * @apiName GetPlatformsForRestaurant
         * @apiGroup Platforms
         *
         * @apiParam {string} restaurant_id Restaurant's unique ID.
         * @apiParamExample {json}
         * {
         *    restaurant_id: '1234'
         * }
         *
         * @apiSuccess {string} msg
         *
         * @apiSuccessExample Success-Response:
         *     HTTP/1.1 200 OK
         *     [
         *        {
         *            key: 'gmb',
         *            socialId: 1234
         *        }
         *    ]
         *
         * @apiError MissingParameter Missing parameter, need restaurant_id and platform_key.
         * @apiErrorExample {json} Error-Response:
         *     HTTP/1.1 400 Bad Request
         *     {
         *       "msg": {
         *            error: true,
         *            message: 'missing_param'
         *            errorData: 'restaurant_id is needed'
         *        }
         *     }
         * @apiError NotFound Can't find restaurant.
         * @apiErrorExample {json} Error-Response:
         *     HTTP/1.1 404 Not Found
         *     {
         *       "msg": {
         *            error: true,
         *            message: 'missing_resource'
         *        }
         *     }
         */
        this.router.get(
            '/restaurants/:restaurant_id',
            authorize([Role.MALOU_FREE]),
            (req: RequestWithExperimentation<false>, res: Response, next: NextFunction) =>
                experimentationMiddleware({ req, res, next, shouldRaiseError: false }),
            (req: RequestWithExperimentation<false, GetPlatformsForRestaurantParamsDto>, res, next) =>
                this._platformsController.handleGetPlatformsForRestaurant(req, res, next)
        );

        this.router.post('/restaurants', authorize([Role.MALOU_FREE]), (req, res, next) =>
            this._platformsController.handleGetPlatformsForRestaurants(req, res, next)
        );

        /**
         *
         * @api {get} /platforms/:platform_key/restaurants/:restaurant_id/pull Pull overview for one restaurant and one specific platform
         * @apiName PullOverviewForPlatform
         * @apiGroup Platforms
         *
         * @apiParam {string} restaurant_id Restaurant's unique ID.
         * @apiParam {string} platform_key Platform key.
         * @apiParam {Boolean} switch_platform - if true delete last platform and insert the new else just update platform.
         *
         * @apiSuccess {string} msg
         *
         * @apiSuccessExample Success-Response:
         *     HTTP/1.1 200 OK
         *     {
         *       "msg": "Successfully pulled reviews"
         *     }
         *
         * @apiError MissingParameter Missing parameter, need restaurant_id and platform_key.
         * @apiErrorExample {json} Error-Response:
         *     HTTP/1.1 400 Bad Request
         *     {
         *       "msg": "Missing parameter, need restaurant_id and platform_key"
         *     }
         * @apiError NotSupported Reviews for this platform is not supported.
         * @apiErrorExample {json} Error-Response:
         *     HTTP/1.1 500 Internal Server Error
         *     {
         *       "msg": {
         *           error: true,
         *           message: 'xxx',
         *           errorData: 'xxx'
         *         }
         *     }
         */
        this.router.get(
            '/:platform_key/restaurants/:restaurant_id/pull',
            authorize(),
            casl(),
            (req: RequestWithPermissions<PullOverviewPlatformParamsDto, never, never, PullPlatformQueryDto>, res, next) =>
                this._platformsController.handlePullOverviewForPlatform(req, res, next)
        );

        this.router.put('/:platform_key/restaurants/:restaurant_id/credentials/:credential_id/choose', authorize(), (req, res, next) =>
            this._platformsController.handleChoosePlatformCredentials(req, res, next)
        );

        this.router.get('/:platform_key/restaurants/:restaurant_id/profile-picture-url', authorize(), (req, res, next) =>
            this._platformsController.handleGetProfilePictureUrl(req, res, next)
        );

        this.router.get('/:platform_key/restaurants/:restaurant_id/fallback-url', authorize(), (req, res, next) =>
            this._platformsController.handleGetFallbackUrl(req, res, next)
        );

        /**
         *
         * @api {get} /platforms/:platform_key/restaurants/:restaurant_id Get current platform data for one restaurant and one specific platform
         * @apiName GetPlatform
         * @apiGroup Platforms
         *
         * @apiParam {string} restaurant_id Restaurant's unique ID.
         * @apiParam {string} platform_key Platform key.
         *
         * @apiSuccess {string} msg
         *
         * @apiError MissingParameter Missing parameter, need restaurant_id and platform_key.
         * @apiErrorExample {json} Error-Response:
         *     HTTP/1.1 400 Bad Request
         *     {
         *       "msg": "Missing parameter, need restaurant_id and platform_key"
         *     }
         * @apiError NotSupported GetData for this platform is not supported.
         * @apiErrorExample {json} Error-Response:
         *     HTTP/1.1 500 Internal Server Error
         *     {
         *       "msg": {
         *           error: true,
         *           message: 'xxx',
         *           errorData: 'xxx'
         *         }
         *     }
         */
        this.router.get('/:platform_key/restaurants/:restaurant_id/upsert', authorize(), (req, res, next) =>
            this._platformsController.handleGetPlatformAndUpsert(req, res, next)
        );

        this.router.get('/:platform_key/restaurants/:restaurant_id', (req: Request<GetPlatformParamsDto>, res, next) =>
            this._platformsController.handleGetPlatform(req, res, next)
        );

        /**
         *
         * @api {get} /platforms/:platform_key/search Search for social ids
         * @apiName SeachForPlatform
         * @apiGroup Platforms
         *
         * @apiParam {string} platform_key Platform key.
         *
         * @apiSuccess {string} msg
         *
         * @apiSuccessExample Success-Response:
         *     HTTP/1.1 200 OK
         *     {
         *       "msg": {
         *           data: [
         *                   {socialId: xxxx},
         *               ]
         *           }
         *     }
         *
         * @apiError Bad , need restaurant_id and platform_key.
         * @apiErrorExample {json} Error-Response:
         *     HTTP/1.1 400 Bad Request
         *     {
         *       "msg": "Missing parameter, need restaurant_id and platform_key"
         *     }
         * @apiError Internal Server Error for this platform is not supported.
         * @apiErrorExample {json} Error-Response:
         *     HTTP/1.1 500 Internal Server Error
         *     {
         *       "msg": {
         *           error: true,
         *           message: 'xxx',
         *           errorData: 'xxx'
         *         }
         *     }
         */
        this.router.get('/:platform_key/search', authorize(), (req, res, next) =>
            this._platformsController.handleSearchPlatformKey(req, res, next)
        );

        /**
         *
         * @api {post} /platforms/:platform_key/scrap Returns the value of the platform endpoint page (either raw or json)
         * @apiName ScrapPlatform
         * @apiGroup Platforms
         *
         * @apiParam {string} platform_key Platform key.
         * @apiParam {Object} {endpoint: string} Platform endpoint
         *
         * @apiSuccess {Object} data
         *
         * @apiSuccessExample Success-Response:
         *     HTTP/1.1 200 OK
         *     {
         *        data: json || string
         *     }
         *
         * @apiError Bad Request, need platform_key and endpoint.
         * @apiErrorExample {json} Error-Response:
         *     HTTP/1.1 400 Bad Request
         *     {
         *       error: true,
         *           message: "Missing parameter, need platform_key as param and endpoint in body"
         *     }
         * @apiError Bad Request, endpoint malformed.
         * @apiErrorExample {json} Error-Response:
         *     HTTP/1.1 400 Bad Request
         *     {
         *       error: true,
         *       message: "Endpoint is malformed"
         *     }
         */
        this.router.post('/:platform_key/scrap', authorize(), (req, res, next) =>
            this._platformsController.handleScrapPlatform(req, res, next)
        );

        /**
         *
         * @api {delete} /platforms/:platform_key/restaurants/:restaurant_id/ Delete platform bound to the restaurant
         * @apiName DeletePlatformForRestaurant
         * @apiGroup Platforms
         *
         * @apiParam {string} restaurant_id Restaurant's unique ID.
         * @apiParam {string} platform_key Platform key.
         *
         * @apiSuccess {string} msg
         *
         * @apiSuccessExample Success-Response:
         *     HTTP/1.1 200 OK
         *     {
         *       "msg": "Successfully deleted"
         *     }
         *
         * @apiError MissingParameter Missing parameter, need restaurant_id and platform_key.
         * @apiErrorExample {json} Error-Response:
         *     HTTP/1.1 400 Bad Request
         *     {
         *       "msg": "Missing parameter, need restaurant_id and platform_key"
         *     }
         * @apiError NotSupported Reviews for this platform is not supported.
         * @apiErrorExample {json} Error-Response:
         *     HTTP/1.1 500 Internal Server Error
         *     {
         *       "msg": {
         *           error: true,
         *           message: 'xxx',
         *           errorData: 'xxx'
         *         }
         *     }
         */
        this.router.delete(
            '/:platform_key/restaurants/:restaurant_id',
            authorize(),
            casl(),
            (req: RequestWithPermissions<DeletePlatformByRestaurantIdAndPlatformKeyParamsDto>, res, next) =>
                this._platformsController.handleDeletePlatformByRestaurantIdAndPlatformKey(req, res, next)
        );

        /**
         * @api {put} /:platform_id/:field_key/lockField lock field in platform
         * @apiName lockField
         * @apiGroupe platforms
         *
         * @apiParam {string} platform_id Platform id.
         * @apiParam {string} field_key field to lock.
         *
         * @apiSuccess {string} msg
         *
         * @apiSuccessExample Success-Response:
         *     HTTP/1.1 200 OK
         *     {
         *       "msg": "Succesfully locked field"
         *     }
         *
         * @apiError MissingParameter Missing parameter, need platform_id and field_key.
         * @apiErrorExample {json} Error-Response:
         *     HTTP/1.1 400 Bad Request
         *     {
         *       "msg": "Missing parameter, need platform_id and field_key"
         *     }
         *
         */
        this.router.put('/:platform_id/:field_key/lock', authorize(), (req, res, next) =>
            this._platformsController.handleLockField(req, res, next)
        );

        /**
         * @api {put} /:platform_id/:field_key/unlockField lock field in platform
         * @apiName unlockField
         * @apiGroupe platforms
         *
         * @apiParam {string} platform_id Platform id.
         * @apiParam {string} field_key field to lock.
         *
         * @apiSuccess {string} msg
         *
         * @apiSuccessExample Success-Response:
         *     HTTP/1.1 200 OK
         *     {
         *       "msg": "Succesfully unlocked field"
         *     }
         *
         * @apiError MissingParameter Missing parameter, need platform_id and field_key.
         * @apiErrorExample {json} Error-Response:
         *     HTTP/1.1 400 Bad Request
         *     {
         *       "msg": "Missing parameter, need platform_id and field_key"
         *     }
         *
         */
        this.router.put('/:platform_id/:field_key/unlock', authorize(), (req, res, next) =>
            this._platformsController.handleUnlockField(req, res, next)
        );

        /**
         *
         * @api {get} /platforms/:platform_key/restaurants/:restaurant_id/social_link Get platform social link for a specific restaurant
         * @apiName GetPlatformSocialLinkForRestaurant
         * @apiGroup Platforms
         *
         * @apiParam {string} restaurant_id Restaurant's unique ID.
         *
         * @apiSuccess {string} msg
         *
         * @apiSuccessExample Success-Response:
         *     HTTP/1.1 200 OK
         *     [
         *        {
         *            key: 'gmb',
         *            socialId: 1234,
         *            socialLink: '1234'
         *        }
         *    ]
         * !!! WARNING !!!
         * Route used with external api key
         *
         */
        this.router.get('/:platform_key/restaurants/:restaurant_id/social_link', (req, res, next) =>
            this._platformsController.handleGetPlatformSocialLinkForRestaurant(req as any, res, next)
        );

        /**
         *
         * @api {get} /platforms/:platform_key/media Search for platform profile and cover pictures
         * @apiName getProfileAndCoverMediaForPlatform
         * @apiGroup Platforms
         *
         * @apiParam {string} platform_key Platform key.
         *
         * @apiSuccess {string} msg
         *
         * @apiSuccessExample Success-Response:
         *     HTTP/1.1 200 OK
         *     {
         *       "msg": {},
         *       "data": {logo: Media, cover: Media}
         *     }
         *
         * @apiError Internal Server Error for this platform is not supported.
         * @apiErrorExample {json} Error-Response:
         *     HTTP/1.1 500 Internal Server Error
         *     {
         *       "msg": {
         *           error: true,
         *           message: 'xxx',
         *           errorData: 'xxx'
         *         }
         *     }
         */
        this.router.get(
            '/:platform_key/media',
            authorize(),
            casl(),
            (req: Request<{ platform_key: PlatformKey }, never, never, { credential_id: string; social_id: string }>, res, next) =>
                this._platformsController.handleGetProfileAndCoverMediaForPlatform(req, res, next)
        );

        this.router.post(
            '/search',
            authorize(),
            casl(),
            (req: RequestWithExperimentation<false>, res: Response, next: NextFunction) =>
                experimentationMiddleware({ req, res, next, shouldRaiseError: false }),
            (req: RequestWithExperimentation<false, any, any, GetPlatformsByRestaurantIdsBodyDto>, res, next) =>
                this._platformsController.handleGetPlatformsByRestaurantIds(req, res, next)
        );

        // TODO: remove after credentials are unique by authId
        this.router.get('/index', authorize([Role.ADMIN]), (req, res, next) =>
            this._platformsController.handleGetPlatformsByPlatformsKeys(req, res, next)
        );

        this.router.patch('/:platformId/credentials/:credentialId', authorize(), (req, res, next) =>
            this._platformsController.handleAttachCredential(req, res, next)
        );

        this.router.post('/send-mapstr-reminder', authorize(), (req, res, next) =>
            this._platformsController.handleSendMapstrReminder(req, res, next)
        );

        return this.router;
    }
}
