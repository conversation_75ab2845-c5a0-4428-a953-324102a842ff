import { ForbiddenError, subject } from '@casl/ability';
import { NextFunction, Request, Response } from 'express';
import _ from 'lodash';
import assert from 'node:assert';
import { singleton } from 'tsyringe';

import {
    AttachCredentialParamsDto,
    attachCredentialParamsValidator,
    AttachCredentialResponseDto,
    ChoosePlatformCredentialsParamsDto,
    choosePlatformCredentialsParamsValidator,
    DeletePlatformByRestaurantIdAndPlatformKeyParamsDto,
    deletePlatformByRestaurantIdAndPlatformKeyParamsValidator,
    DeletePlatformParamsDto,
    deletePlatformParamsValidator,
    GetFallbackUrlParamsDto,
    getFallbackUrlParamsValidator,
    GetPlatformParamsDto,
    getPlatformParamsValidator,
    GetPlatformsByRestaurantIdsBodyDto,
    getPlatformsByRestaurantIdsBodyValidator,
    GetPlatformsByRestaurantIdsResponseDto,
    GetPlatformsForRestaurantParamsDto,
    getPlatformsForRestaurantParamsValidator,
    GetPlatformsForRestaurantQueryDto,
    getPlatformsForRestaurantQueryValidator,
    GetPlatformsForRestaurantsBodyDto,
    getPlatformsForRestaurantsBodyValidator,
    GetProfilePictureUrlParamsDto,
    getProfilePictureUrlParamsValidator,
    LockOrUnlockFieldParamsDto,
    lockOrUnlockFieldParamsValidator,
    PlatformDto,
    PlatformKeyParamsDto,
    platformKeyParamsValidator,
    PullOverviewPlatformParamsDto,
    pullOverviewPlatformParamsValidator,
    PullPlatformQueryDto,
    pullPlatformQueryValidator,
    ScrapPlatformBodyValidator,
    scrapPlatformBodyValidator,
    searchPlatformKeyParamsValidator,
    searchPlatformKeyQueryValidator,
    SearchPlatformKeysParamsDto,
    SearchPlatformKeysQueryDto,
    SendMapstrReminderBodyDto,
    sendMapstrReminderBodyValidator,
    UpsertPlatformBodyDto,
    upsertPlatformBodyValidator,
} from '@malou-io/package-dto';
import { toDbId } from '@malou-io/package-models';
import {
    ApiResult,
    ApiResultV2,
    CaslAction,
    CaslSubject,
    getFeatureFlaggedPlatforms,
    getPlatformDefinition,
    MalouErrorCode,
    PlatformKey,
    PostPublicationStatus,
} from '@malou-io/package-utils';

import { MalouError } from ':helpers/classes/malou-error';
import { Body, Params, Query } from ':helpers/decorators/validators';
import { logger } from ':helpers/logger';
import { shouldUpsertWithLinkedComponents } from ':helpers/utils';
import { RequestWithExperimentation, RequestWithPermissions, RequestWithUser } from ':helpers/utils.types';
import { CommentsRepository } from ':modules/comments/comments.repository';
import { PlatformsDtoMapper } from ':modules/platforms/platforms.dto-mapper';
import { Platform } from ':modules/platforms/platforms.entity';
import PlatformsRepository from ':modules/platforms/platforms.repository';
import { PlatformsUseCases } from ':modules/platforms/platforms.use-cases';
import { KeywordSearchImpressionsService } from ':modules/platforms/services/keyword-search-impressions/keyword-search-impressions.service';
import { GetDisconnectedPlatformsForRestaurantUseCase } from ':modules/platforms/use-cases/get-disconnected-platforms-for-restaurant/get-disconnected-platforms-for-restaurant.use-case';
import { GetFallbackUrlUseCase } from ':modules/platforms/use-cases/get-fallback-url/get-fallback-url.use-case';
import { GetProfilePictureUrlUseCase } from ':modules/platforms/use-cases/get-profile-picture-url/get-profile-picture-url.use-case';
import { ScheduleMapstrReminderUseCase } from ':modules/platforms/use-cases/schedule-mapstr-reminder/schedule-mapstr-reminder.use-case';
import { UpsertPlatformUseCase } from ':modules/platforms/use-cases/upsert-platform/upsert-platform.use-case';
import PostsUseCases from ':modules/posts/posts.use-cases';
import { SynchronizePostsUseCase } from ':modules/posts/use-cases/synchronize-posts/synchronize-posts.use-case';
import { ReviewsRepository } from ':modules/reviews/reviews.repository';

@singleton()
export default class PlatformsController {
    constructor(
        private readonly _commentsRepository: CommentsRepository,
        private readonly _platformsRepository: PlatformsRepository,
        private readonly _reviewsRepository: ReviewsRepository,
        private readonly _platformsUseCases: PlatformsUseCases,
        private readonly _postsUseCases: PostsUseCases,
        private readonly _synchronizePostsUseCase: SynchronizePostsUseCase,
        private readonly _platformsDtoMapper: PlatformsDtoMapper,
        private readonly _scheduleMapstrReminderUseCase: ScheduleMapstrReminderUseCase,
        private readonly _getDisconnectedPlatformsForRestaurantUseCase: GetDisconnectedPlatformsForRestaurantUseCase,
        private readonly _getProfilePictureUrlUseCase: GetProfilePictureUrlUseCase,
        private readonly _upsertPlatformUseCase: UpsertPlatformUseCase,
        private readonly _getFallbackUrlUseCase: GetFallbackUrlUseCase,
        private readonly _keywordSearchImpressionsService: KeywordSearchImpressionsService
    ) {}

    @Params(attachCredentialParamsValidator)
    async handleAttachCredential(
        req: Request<AttachCredentialParamsDto>,
        res: Response<ApiResult<AttachCredentialResponseDto>>,
        next: NextFunction
    ) {
        try {
            const updatedPlatformDto = await this._platformsUseCases.attachCredential(req.params.platformId, req.params.credentialId);
            res.status(200).json({ data: updatedPlatformDto });
        } catch (err) {
            next(err);
        }
    }

    @Params(deletePlatformParamsValidator)
    async handleDeletePlatform(req: Request<DeletePlatformParamsDto>, res: Response, next: NextFunction) {
        try {
            const { platformId } = req.params;
            const platform = await this._platformsRepository.getPlatformById(platformId);
            logger.info('[DELETE_PLATFORM_TRACING] - ', {
                user: req.user,
                platform,
            });
            const platformDeleted = await this._platformsUseCases.deletePlatform(platformId);
            return res.json({ msg: 'Platform deleted', data: platformDeleted });
        } catch (err) {
            next(err);
        }
    }

    @Body(getPlatformsByRestaurantIdsBodyValidator)
    async handleGetPlatformsByRestaurantIds(
        req: RequestWithExperimentation<false, any, any, GetPlatformsByRestaurantIdsBodyDto>,
        res: Response<ApiResult<GetPlatformsByRestaurantIdsResponseDto[]>>,
        next: NextFunction
    ) {
        try {
            const { restaurantIds, keys } = req.body;

            const filter = keys?.length
                ? { restaurantId: { $in: restaurantIds }, key: { $in: keys } }
                : { restaurantId: { $in: restaurantIds } };
            const platforms = await this._platformsRepository.find({
                filter,
                projection: { _id: true, restaurantId: true, key: true, credentials: true },
                options: { lean: true },
            });

            const featureFlaggedPlatforms = getFeatureFlaggedPlatforms();
            const platformsToRemove = featureFlaggedPlatforms
                .filter((platform) => !!platform.featureFlagKey && !req.experimentationService?.isFeatureEnabled(platform.featureFlagKey))
                .map((platform) => platform.key);
            const filteredPlatforms = platforms.filter((platform) => !platformsToRemove.includes(platform.key));

            const getPlatformsByRestaurantIdsResponseDto =
                this._platformsDtoMapper.toGetPlatformsByRestaurantIdsResponseDto(filteredPlatforms);

            return res.status(200).json({ data: getPlatformsByRestaurantIdsResponseDto });
        } catch (err) {
            next(err);
        }
    }

    @Body(upsertPlatformBodyValidator)
    async handleUpsertPlatform(req: RequestWithPermissions<any, any, UpsertPlatformBodyDto>, res: Response, next: NextFunction) {
        try {
            const { restaurantId, platform, credentialId } = req.body;
            Object.keys(_.omit(platform, 'key') || {}).forEach((key) => {
                assert(req.userRestaurantsAbility);
                ForbiddenError.from(req.userRestaurantsAbility).throwUnlessCan(
                    CaslAction.UPDATE,
                    subject(CaslSubject.PLATFORM, { restaurantId }),
                    key
                );
            });
            const { key } = platform;

            // social id may need to be cleaned of some query params because of the link given by the user
            // e.g. ubereats' home link looks like this: https://merchants.ubereats.com/manager/home/<USER>
            const paramsStartingIndex = platform.socialId?.indexOf('?') ?? -1;
            const cleanSocialId = paramsStartingIndex >= 0 ? platform.socialId?.slice(0, paramsStartingIndex) : platform.socialId;
            platform.socialId = cleanSocialId;

            const platformDoc = await this._platformsRepository.upsert({
                filter: { restaurantId: toDbId(restaurantId), key },
                update: { ...platform },
            });
            if (credentialId) {
                await this._platformsRepository.unshiftCredentials(platformDoc._id, credentialId);
            }
            return res.status(200).json({ msg: 'Successfully upserted platform', data: platformDoc });
        } catch (err) {
            next(err);
        }
    }

    @Params(getPlatformsForRestaurantParamsValidator)
    @Query(getPlatformsForRestaurantQueryValidator)
    async handleGetDisconnectedPlatformsForRestaurant(
        req: RequestWithUser<GetPlatformsForRestaurantParamsDto, never, never, GetPlatformsForRestaurantQueryDto>,
        res: Response<ApiResultV2<Partial<PlatformDto>[]>>,
        next: NextFunction
    ) {
        try {
            const { restaurantId } = req.params;
            const { platformKeys } = req.query;
            assert(req.user, 'User not found');
            const userId = req.user._id.toString();
            const platforms = await this._getDisconnectedPlatformsForRestaurantUseCase.execute(restaurantId, userId, platformKeys);
            return res.json({
                data: platforms,
            });
        } catch (err) {
            next(err);
        }
    }

    @Params(getPlatformsForRestaurantParamsValidator)
    async handleGetPlatformsForRestaurant(
        req: RequestWithExperimentation<false, GetPlatformsForRestaurantParamsDto>,
        res: Response,
        next: NextFunction
    ) {
        try {
            const { restaurantId } = req.params;
            const platforms = await this._platformsRepository.find({
                filter: { restaurantId: toDbId(restaurantId) },
                options: { lean: true, populate: [{ path: 'credentials' }] },
            });

            const featureFlaggedPlatforms = getFeatureFlaggedPlatforms();
            const platformsToRemove = featureFlaggedPlatforms
                .filter((platform) => !!platform.featureFlagKey && !req.experimentationService?.isFeatureEnabled(platform.featureFlagKey))
                .map((platform) => platform.key);
            const filteredPlatforms = platforms.filter((platform) => !platformsToRemove.includes(platform.key));

            return res.json({
                data: filteredPlatforms.map((p) => ({
                    ...p,
                })),
            });
        } catch (err) {
            next(err);
        }
    }

    @Body(getPlatformsForRestaurantsBodyValidator)
    async handleGetPlatformsForRestaurants(
        req: Request<any, any, GetPlatformsForRestaurantsBodyDto>,
        res: Response<ApiResultV2<PlatformDto[]>>,
        next: NextFunction
    ): Promise<void> {
        try {
            const { restaurantIds } = req.body;
            const platforms = await this._platformsRepository.find({
                filter: { restaurantId: { $in: restaurantIds.map((restaurantId) => toDbId(restaurantId)) } },
                options: { lean: true },
            });
            const dto = platforms.map((platform) => this._platformsDtoMapper.toDto(platform));
            res.json({ data: dto });
        } catch (err) {
            next(err);
        }
    }

    @Params(pullOverviewPlatformParamsValidator)
    @Query(pullPlatformQueryValidator)
    async handlePullOverviewForPlatform(
        req: RequestWithPermissions<PullOverviewPlatformParamsDto, never, never, PullPlatformQueryDto>,
        res: Response<ApiResult<PlatformDto>>,
        next: NextFunction
    ) {
        try {
            const { restaurantId, platformKey } = req.params;
            assert(req.userRestaurantsAbility);
            ForbiddenError.from(req.userRestaurantsAbility).throwUnlessCan(
                CaslAction.UPDATE,
                subject(CaslSubject.PLATFORM, { restaurantId })
            );
            assert(req.user, 'User not found');
            const userId = req.user._id.toString();
            const { switchPlatform } = req.query;

            const platform = await this._platformsRepository.getPlatformByRestaurantIdAndPlatformKey(restaurantId, platformKey);
            if (!platform) {
                throw new MalouError(MalouErrorCode.PLATFORM_NOT_FOUND, {
                    metadata: { restaurantId, platformKey },
                });
            }
            const credentialId = platform.credentials?.[0]?.toString();

            if (switchPlatform === 'true' && platform) {
                const { _id: platformId } = platform;
                await Promise.all([
                    this._reviewsRepository.deleteMany({ filter: { platformId } }),
                    this._commentsRepository.deleteMany({ filter: { platformId } }),
                    this._postsUseCases.deleteManyPostsAndHandleSideEffects({
                        platformId,
                        published: PostPublicationStatus.PUBLISHED,
                        isStory: false,
                    }),
                ]);
            }

            const rawPlatformData = await this._platformsUseCases.getPlatformDataForRestaurantId(restaurantId, platformKey, credentialId);
            const result: any = shouldUpsertWithLinkedComponents(rawPlatformData, platform.key)
                ? await this._platformsUseCases.upsertPlatformWithLinkedComponents(restaurantId, platformKey, rawPlatformData, userId)
                : await this._upsertPlatformUseCase.execute(restaurantId, platformKey, { socialId: rawPlatformData.socialId });

            if (result.error) {
                throw new MalouError(MalouErrorCode.PLATFORM_PULL_OVERVIEW_ERROR, {
                    metadata: result,
                });
            }

            if (result._id) {
                this._keywordSearchImpressionsService
                    .createMessageQueueToFetchMonthlyKeywordSearchImpressions(result._id.toString(), platformKey)
                    .catch((err) => logger.error('[KEYWORD_SEARCH_IMPRESSIONS_ERROR]', err));
            }

            this._synchronizePostsUseCase.execute(restaurantId, [platformKey]).catch((err) => logger.error('[SYNC_POSTS_ERROR]', err));

            return res.status(200).json({ data: this._platformsDtoMapper.toDto(result) });
        } catch (err) {
            next(err);
        }
    }

    @Params(choosePlatformCredentialsParamsValidator)
    async handleChoosePlatformCredentials(
        req: RequestWithPermissions<ChoosePlatformCredentialsParamsDto>,
        res: Response,
        next: NextFunction
    ) {
        try {
            const { restaurantId, platformKey, credentialId } = req.params;
            await this._platformsUseCases.changeCurrentlyUsedCredential(platformKey as PlatformKey, restaurantId, credentialId);
            return res.json({ msg: 'Changed account successfully' });
        } catch (err) {
            next(err);
        }
    }

    // TODO WOF: it is only used in permissions.effects.ts, and it has to be refactored from a product point of view and tech point of view
    @Params(getPlatformParamsValidator)
    async handleGetPlatformAndUpsert(req: Request<GetPlatformParamsDto>, res: Response, next: NextFunction) {
        try {
            const { restaurantId, platformKey } = req.params;
            let platformData;
            if (getPlatformDefinition(platformKey)?.isAsynchronouslyScrapped) {
                platformData = await this._platformsRepository.getPlatformByRestaurantIdAndPlatformKey(restaurantId, platformKey);
            } else {
                const userId = req.user._id.toString();
                const rawPlatformData = await this._platformsUseCases.getPlatformDataForRestaurantId(restaurantId, platformKey);
                platformData = shouldUpsertWithLinkedComponents(rawPlatformData, platformKey)
                    ? await this._platformsUseCases.upsertPlatformWithLinkedComponents(restaurantId, platformKey, rawPlatformData, userId)
                    : await this._upsertPlatformUseCase.execute(restaurantId, platformKey, {
                          socialId: rawPlatformData.socialId,
                      });
            }

            if (platformData.error) {
                throw new MalouError(MalouErrorCode.PLATFORM_UPSERT_ERROR, {
                    metadata: platformData,
                });
            }

            platformData = await platformData.populate('credentials');
            return res.json({ msg: 'Platform retrieved', data: platformData });
        } catch (err: any) {
            let prettyError = err;

            if (err.message.match(/All promises were rejected/)) {
                prettyError = new MalouError(MalouErrorCode.INTERNAL_SERVER_ERROR, { metadata: { rawError: err } });
            }

            next(prettyError);
        }
    }

    @Params(getPlatformParamsValidator)
    async handleGetPlatform(req: Request<GetPlatformParamsDto>, res: Response<ApiResultV2<PlatformDto>>, next: NextFunction) {
        try {
            const { restaurantId, platformKey } = req.params;
            const platform = await this._platformsUseCases.getPlatformByPlatformKeyAndRestaurantId(platformKey, restaurantId);
            return res.json({ data: platform });
        } catch (error) {
            next(error);
        }
    }

    @Params(searchPlatformKeyParamsValidator)
    @Query(searchPlatformKeyQueryValidator)
    async handleSearchPlatformKey(
        req: Request<SearchPlatformKeysParamsDto, null, null, SearchPlatformKeysQueryDto>,
        res: Response,
        next: NextFunction
    ) {
        try {
            const { platform_key: platformKey } = req.params;
            const { restaurantId, credentialId, socialId } = req.query;

            // social id may need to be cleaned of some query params because of the link given by the user
            // e.g. ubereats' home link looks like this: https://merchants.ubereats.com/manager/home/<USER>
            const paramsStartingIndex = socialId?.indexOf('?') ?? -1;
            const cleanSocialId = paramsStartingIndex >= 0 ? socialId?.slice(0, paramsStartingIndex) : socialId;

            const result = await this._platformsUseCases.getSocialIds({
                restaurantId,
                platformKey,
                credentialId,
                socialId: cleanSocialId,
            });
            return res.status(200).json({ data: result });
        } catch (err) {
            next(err);
        }
    }

    @Params(platformKeyParamsValidator)
    @Body(scrapPlatformBodyValidator)
    async handleScrapPlatform(req: Request<PlatformKeyParamsDto, any, ScrapPlatformBodyValidator>, res: Response, next: NextFunction) {
        try {
            const { platformKey } = req.params;
            const { endpoint } = req.body;

            const result = await this._platformsUseCases.scrapPlatformEndpoint(platformKey, endpoint);

            if (result.error) {
                throw new MalouError(MalouErrorCode.PLATFORM_SCRAP_ERROR, {
                    metadata: result,
                });
            }

            return res.status(200).json({ data: result });
        } catch (err) {
            next(err);
        }
    }

    @Params(deletePlatformByRestaurantIdAndPlatformKeyParamsValidator)
    async handleDeletePlatformByRestaurantIdAndPlatformKey(
        req: RequestWithPermissions<DeletePlatformByRestaurantIdAndPlatformKeyParamsDto>,
        res: Response,
        next: NextFunction
    ) {
        try {
            const { restaurantId, platformKey } = req.params;
            assert(req.userRestaurantsAbility);
            ForbiddenError.from(req.userRestaurantsAbility).throwUnlessCan(
                CaslAction.DELETE,
                subject(CaslSubject.PLATFORM, { restaurantId })
            );
            const platform = await this._platformsRepository.getPlatformByRestaurantIdAndPlatformKey(restaurantId, platformKey);
            logger.info('[DELETE_PLATFORM_TRACING_2] - ', {
                user: req.user,
                platform,
            });
            if (!platform) {
                throw new MalouError(MalouErrorCode.PLATFORM_NOT_FOUND, {
                    message: '[handleDeletePlatformByRestaurantIdAndPlatformKey] Platform not found',
                    metadata: { restaurantId, platformKey },
                });
            }
            const result: any = await this._platformsUseCases.deletePlatform(platform._id.toString());

            if (result?.error) {
                throw new MalouError(MalouErrorCode.PLATFORM_DELETE_ERROR, {
                    metadata: result,
                });
            }

            return res.status(200).json({ msg: 'Successfully deleted platform' });
        } catch (err) {
            return next(err);
        }
    }

    @Params(lockOrUnlockFieldParamsValidator)
    async handleLockField(req: Request<LockOrUnlockFieldParamsDto>, res: Response, next: NextFunction) {
        try {
            const { platformId, fieldKey } = req.params;
            const result = await this._platformsUseCases.lock(platformId, fieldKey);
            return res.status(200).json({ msg: 'Successfully locked field', data: result });
        } catch (error) {
            next(error);
        }
    }

    @Params(lockOrUnlockFieldParamsValidator)
    async handleUnlockField(req: Request<LockOrUnlockFieldParamsDto>, res: Response, next: NextFunction) {
        try {
            const { platformId, fieldKey } = req.params;
            const result = await this._platformsUseCases.unlock(platformId, fieldKey);
            return res.status(200).json({ msg: 'Successfully unlocked field', data: result });
        } catch (error) {
            next(error);
        }
    }

    @Params(getPlatformParamsValidator)
    async handleGetPlatformSocialLinkForRestaurant(req: Request<GetPlatformParamsDto>, res: Response, next: NextFunction) {
        try {
            const { restaurantId, platformKey } = req.params;
            const platform: Pick<Platform, 'key' | 'socialId' | 'socialLink' | 'name'> | null =
                await this._platformsRepository.getPlatformByRestaurantIdAndPlatformKey(restaurantId, platformKey, {
                    key: 1,
                    socialId: 1,
                    socialLink: 1,
                    name: 1,
                });
            if (!platform) {
                throw new MalouError(MalouErrorCode.PLATFORM_NOT_FOUND, {
                    message: '[handleGetPlatformSocialLinkForRestaurant] Platform not found',
                });
            }
            return res.json({
                data: platform,
            });
        } catch (err) {
            next(err);
        }
    }

    async handleGetProfileAndCoverMediaForPlatform(
        req: Request<{ platform_key: PlatformKey }, never, never, { credential_id: string; social_id: string }>,
        res: Response,
        next: NextFunction
    ) {
        try {
            const { platform_key: platformKey } = req.params;
            const { credential_id: credentialId, social_id: socialId } = req.query;
            const result = await this._platformsUseCases.getProfileAndCoverMediaForPlatform({ platformKey, socialId, credentialId });
            return res.status(200).json({ data: result });
        } catch (err) {
            next(err);
        }
    }

    async handleGetPlatformsByPlatformsKeys(
        req: Request<never, never, never, { platforms_keys: string }>,
        res: Response,
        next: NextFunction
    ) {
        try {
            const { platforms_keys: platformKeys } = req.query;
            const platforms = await this._platformsRepository.find({
                filter: { key: { $in: platformKeys.split(',') } },
                projection: {
                    key: 1,
                    credentials: 1,
                    name: 1,
                },
                options: { lean: true },
            });
            res.json({ data: platforms });
        } catch (error) {
            next(error);
        }
    }

    @Body(sendMapstrReminderBodyValidator)
    async handleSendMapstrReminder(req: Request<any, any, SendMapstrReminderBodyDto>, res: Response, next: NextFunction) {
        try {
            await this._scheduleMapstrReminderUseCase.execute({
                restaurantId: req.body.restaurantId,
                user: {
                    name: req.user.name,
                    email: req.user.email,
                    defaultLanguage: req.user.defaultLanguage,
                },
            });
            res.status(204).end();
        } catch (error) {
            next(error);
        }
    }

    @Params(getProfilePictureUrlParamsValidator)
    async handleGetProfilePictureUrl(
        req: Request<GetProfilePictureUrlParamsDto>,
        res: Response<ApiResultV2<{ profilePictureUrl: string }>>,
        next: NextFunction
    ) {
        try {
            const { restaurantId, platformKey } = req.params;
            const profilePictureUrl = await this._getProfilePictureUrlUseCase.execute({ restaurantId, platformKey });

            res.json({ data: { profilePictureUrl } });
        } catch (error) {
            next(error);
        }
    }

    @Params(getFallbackUrlParamsValidator)
    async handleGetFallbackUrl(req: Request<GetFallbackUrlParamsDto>, res: Response<ApiResultV2<string | null>>, next: NextFunction) {
        try {
            const { restaurantId, platformKey } = req.params;
            const fallbackUrl = await this._getFallbackUrlUseCase.execute(platformKey, restaurantId);
            res.json({ data: fallbackUrl });
        } catch (error) {
            next(error);
        }
    }
}
