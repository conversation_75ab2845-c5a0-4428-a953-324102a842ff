import { Router } from 'express';
import { singleton } from 'tsyringe';

import { Role } from '@malou-io/package-utils';

import { apiKeyAuthorize } from ':modules/api-keys/middlewares';
import StoreLocatorController from ':modules/store-locator/store-locator.controller';
import { authorize } from ':plugins/passport';

@singleton()
export default class StoreLocatorRouter {
    constructor(private _storeLocatorController: StoreLocatorController) {}

    init(router: Router): void {
        router.get('/store-locator/:organizationId/pages', apiKeyAuthorize, (req: any, res, next) =>
            this._storeLocatorController.handleGetPages(req, res, next)
        );

        router.get('/store-locator/:organizationId/configuration', apiKeyAuthorize, (req: any, res, next) =>
            this._storeLocatorController.handleGetOrganizationConfiguration(req, res, next)
        );

        router.get('/store-locator/:organizationId/all-pages', authorize([Role.ADMIN]), (req: any, res, next) =>
            this._storeLocatorController.handleGetPagesForEdit(req, res, next)
        );

        router.get('/store-locator/:organizationId/organization-configuration', authorize([Role.ADMIN]), (req: any, res, next) =>
            this._storeLocatorController.handleGetStoreLocatorOrganizationConfiguration(req, res, next)
        );

        router.put(
            '/store-locator/:organizationId/organization-configuration/ai-settings',
            authorize([Role.ADMIN]),
            (req: any, res, next) => this._storeLocatorController.handleUpdateOrganizationConfigurationAiSettings(req, res, next)
        );

        router.put('/store-locator/:organizationId/all-pages', authorize([Role.ADMIN]), (req: any, res, next) =>
            this._storeLocatorController.handleUpdateRestaurantPages(req, res, next)
        );

        router.get('/store-locator/:organizationId/generate/start', authorize([Role.ADMIN]), (req: any, res, next) =>
            this._storeLocatorController.handleStartStoreLocatorPagesGeneration(req, res, next)
        );

        router.get('/store-locator/:organizationId/generate/watch', authorize([Role.ADMIN]), (req: any, res, next) =>
            this._storeLocatorController.handleWatchStoreLocatorPagesGeneration(req, res, next)
        );

        router.put(
            '/store-locator/:organizationId/organization-configuration/store-pages',
            authorize([Role.ADMIN]),
            (req: any, res, next) => this._storeLocatorController.handleUpdateOrganizationConfigurationStorePages(req, res, next)
        );
    }
}
