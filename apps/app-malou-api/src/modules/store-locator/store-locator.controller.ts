import { NextFunction, Request, Response } from 'express';
import { singleton } from 'tsyringe';

import {
    GetOrganizationConfigurationParamsDto,
    getOrganizationConfigurationParamsValidator,
    GetStoreLocatorOrganizationConfigurationDto,
    GetStoreLocatorPagesDto,
    GetStoreLocatorStoresParamsDto,
    getStoreLocatorStoresParamsValidator,
    StartStoreLocatorPagesGenerationParamsDto,
    startStoreLocatorPagesGenerationParamsValidator,
    StartStoreLocatorPagesGenerationResponseDto,
    StoreLocatorOrganizationConfigurationResponseDto,
    SuccessResponse,
    UpdateOrganizationConfigurationAiSettingsBodyDto,
    updateOrganizationConfigurationAiSettingsBodyValidator,
    UpdateOrganizationConfigurationParamsDto,
    updateOrganizationConfigurationParamsValidator,
    UpdateOrganizationConfigurationStorePagesBodyDto,
    updateOrganizationConfigurationStorePagesBodyValidator,
    updateStoreLocatorStoreBodyValidator,
    UpdateStoreLocatorStorePageParamsDto,
    updateStoreLocatorStorePageParamsValidator,
    UpdateStoreLocatorStorePagesBodyDto,
    WatchStoreLocatorPagesGenerationParamsDto,
    watchStoreLocatorPagesGenerationParamsValidator,
    WatchStoreLocatorPagesGenerationResponseDto,
} from '@malou-io/package-dto';
import { ApiResultError, ApiResultV2 } from '@malou-io/package-utils';

import { Body, Params } from ':helpers/decorators/validators';
import { StartPagesGenerationUseCase } from ':modules/store-locator/use-cases/generate-store-locator-pages/start-pages-generation.use-case';
import { WatchPagesGenerationUseCase } from ':modules/store-locator/use-cases/generate-store-locator-pages/watch-pages-generation.use-case';
import { GetOrganizationConfigurationUseCase } from ':modules/store-locator/use-cases/get-organization-configuration/get-organization-configuration.use-case';
import { GetStoreLocatorOrganizationConfigurationUseCase } from ':modules/store-locator/use-cases/get-store-locator-organization-configuration/get-store-locator-organization-configuration.use-case';
import { GetStoreLocatorPagesUseCase } from ':modules/store-locator/use-cases/get-store-locator-pages/get-store-locator-pages.use-case';
import { GetStoreLocatorStorePagesForEditUseCase } from ':modules/store-locator/use-cases/get-store-locator-store-pages-for-edit/get-store-locator-store-pages-for-edit.use-case';
import UpdateOrganizationConfigAISettingsUseCase from ':modules/store-locator/use-cases/update-organization-config-ai-settings/update-organization-config-ai-settings.use-case';
import UpdateOrganizationConfigStorePagesUseCase from ':modules/store-locator/use-cases/update-organization-config-store-pages/update-organization-config-store-pages.use-case';
import UpdateStoreLocatorStorePagesUseCase from ':modules/store-locator/use-cases/update-store-pages/update-store-pages.use-case';

@singleton()
export default class StoreLocatorController {
    constructor(
        private readonly _getOrganizationConfigurationUseCase: GetOrganizationConfigurationUseCase,
        private readonly _updateOrganizationConfigAISettingsUseCase: UpdateOrganizationConfigAISettingsUseCase,
        private readonly _getStoreLocatorPagesUseCase: GetStoreLocatorPagesUseCase,
        private readonly _getStoreLocatorOrganizationConfigurationUseCase: GetStoreLocatorOrganizationConfigurationUseCase,
        private readonly _updateStoreLocatorStorePagesUseCase: UpdateStoreLocatorStorePagesUseCase,
        private readonly _startPagesGenerationUseCase: StartPagesGenerationUseCase,
        private readonly _watchPagesGenerationUseCase: WatchPagesGenerationUseCase,
        private readonly _updateOrganizationConfigStorePagesUseCase: UpdateOrganizationConfigStorePagesUseCase,
        private readonly _getStoreLocatorStorePagesForEditUseCase: GetStoreLocatorStorePagesForEditUseCase
    ) {}

    @Params(getStoreLocatorStoresParamsValidator)
    async handleGetPages(
        req: Request<GetStoreLocatorStoresParamsDto, never, never>,
        res: Response<ApiResultV2<GetStoreLocatorPagesDto, ApiResultError>>,
        next: NextFunction
    ) {
        try {
            const { organizationId } = req.params;
            const data = await this._getStoreLocatorPagesUseCase.execute(organizationId);
            return res.json({ data });
        } catch (err) {
            next(err);
        }
    }

    @Params(getStoreLocatorStoresParamsValidator)
    async handleGetPagesForEdit(
        req: Request<GetStoreLocatorStoresParamsDto, never, never>,
        res: Response<ApiResultV2<GetStoreLocatorPagesDto, ApiResultError>>,
        next: NextFunction
    ) {
        try {
            const { organizationId } = req.params;
            const data = await this._getStoreLocatorStorePagesForEditUseCase.execute(organizationId);
            return res.json({ data });
        } catch (err) {
            next(err);
        }
    }

    @Params(getOrganizationConfigurationParamsValidator)
    async handleGetOrganizationConfiguration(
        req: Request<GetOrganizationConfigurationParamsDto, never, never>,
        res: Response<ApiResultV2<GetStoreLocatorOrganizationConfigurationDto, ApiResultError>>,
        next: NextFunction
    ) {
        try {
            const { organizationId } = req.params;
            const data = await this._getOrganizationConfigurationUseCase.execute(organizationId);

            return res.json({ data });
        } catch (err) {
            next(err);
        }
    }

    @Params(getOrganizationConfigurationParamsValidator)
    async handleGetStoreLocatorOrganizationConfiguration(
        req: Request<GetOrganizationConfigurationParamsDto, never, never>,
        res: Response<ApiResultV2<StoreLocatorOrganizationConfigurationResponseDto, ApiResultError>>,
        next: NextFunction
    ) {
        try {
            const { organizationId } = req.params;
            const data = await this._getStoreLocatorOrganizationConfigurationUseCase.execute(organizationId);

            return res.json({ data });
        } catch (err) {
            next(err);
        }
    }

    @Params(updateOrganizationConfigurationParamsValidator)
    @Body(updateOrganizationConfigurationAiSettingsBodyValidator)
    async handleUpdateOrganizationConfigurationAiSettings(
        req: Request<UpdateOrganizationConfigurationParamsDto, never, UpdateOrganizationConfigurationAiSettingsBodyDto>,
        res: Response<ApiResultV2<StoreLocatorOrganizationConfigurationResponseDto, ApiResultError>>,
        next: NextFunction
    ) {
        try {
            const { organizationId } = req.params;
            const { aiSettings } = req.body;
            const organizationConfig = await this._updateOrganizationConfigAISettingsUseCase.execute({ organizationId, aiSettings });
            return res.json({ data: organizationConfig });
        } catch (err) {
            next(err);
        }
    }

    @Params(updateStoreLocatorStorePageParamsValidator)
    @Body(updateStoreLocatorStoreBodyValidator)
    async handleUpdateRestaurantPages(
        req: Request<UpdateStoreLocatorStorePageParamsDto, never, UpdateStoreLocatorStorePagesBodyDto>,
        res: Response<ApiResultV2<SuccessResponse, ApiResultError>>,
        next: NextFunction
    ) {
        try {
            const { organizationId } = req.params;
            await this._updateStoreLocatorStorePagesUseCase.execute({ organizationId, pages: req.body });
            return res.json({ data: { success: true } });
        } catch (err) {
            next(err);
        }
    }

    @Params(startStoreLocatorPagesGenerationParamsValidator)
    async handleStartStoreLocatorPagesGeneration(
        req: Request<StartStoreLocatorPagesGenerationParamsDto, never, never>,
        res: Response<ApiResultV2<StartStoreLocatorPagesGenerationResponseDto, ApiResultError>>,
        next: NextFunction
    ) {
        try {
            const { organizationId } = req.params;
            const data = await this._startPagesGenerationUseCase.execute(organizationId);
            return res.json({ data });
        } catch (err) {
            next(err);
        }
    }

    @Params(watchStoreLocatorPagesGenerationParamsValidator)
    async handleWatchStoreLocatorPagesGeneration(
        req: Request<WatchStoreLocatorPagesGenerationParamsDto, never, never>,
        res: Response<ApiResultV2<WatchStoreLocatorPagesGenerationResponseDto, ApiResultError>>,
        next: NextFunction
    ) {
        try {
            const { organizationId } = req.params;
            const data = await this._watchPagesGenerationUseCase.execute(organizationId);
            return res.json({ data });
        } catch (err) {
            next(err);
        }
    }

    @Params(updateOrganizationConfigurationParamsValidator)
    @Body(updateOrganizationConfigurationStorePagesBodyValidator)
    async handleUpdateOrganizationConfigurationStorePages(
        req: Request<UpdateOrganizationConfigurationParamsDto, never, UpdateOrganizationConfigurationStorePagesBodyDto>,
        res: Response<ApiResultV2<SuccessResponse, ApiResultError>>,
        next: NextFunction
    ) {
        try {
            const { organizationId } = req.params;
            const { data } = req.body;
            await this._updateOrganizationConfigStorePagesUseCase.execute({ organizationId, storePages: data });
            return res.json({ data: { success: true } });
        } catch (err) {
            next(err);
        }
    }
}
