import { Builder } from 'builder-pattern';

import { IStoreLocatorOrganizationConfig, newDbId } from '@malou-io/package-models';
import { StoreLocatorAiSettingsLanguageStyle } from '@malou-io/package-utils';

type StoreLocatorOrganizationConfigPayload = IStoreLocatorOrganizationConfig;

const _buildStoreLocatorOrganizationConfig = (storeLocatorOrganizationConfig: StoreLocatorOrganizationConfigPayload) =>
    Builder<StoreLocatorOrganizationConfigPayload>(storeLocatorOrganizationConfig);

export const getDefaultStoreLocatorOrganizationConfig = () =>
    _buildStoreLocatorOrganizationConfig({
        _id: newDbId(),
        organizationId: newDbId(),
        cloudfrontDistributionId: 'EXAMPLE_CLOUDFRONT_ID',
        baseUrl: 'https://example.com',
        isLive: false,
        styles: {
            fonts: [{ class: 'primary', src: 'https://example.io' }],
            colors: [{ class: 'primary', value: '#FFFFFF' }],
            pages: {
                store: {},
                map: {},
                storeDraft: {},
                mapDraft: {},
            },
        },
        aiSettings: {
            tone: ['friendly'],
            languageStyle: StoreLocatorAiSettingsLanguageStyle.FORMAL,
            attributeIds: [],
            restaurantKeywordIds: [],
            specialAttributes: [],
        },
        shouldDisplayWhiteMark: false,
        createdAt: new Date(),
        updatedAt: new Date(),
    });
