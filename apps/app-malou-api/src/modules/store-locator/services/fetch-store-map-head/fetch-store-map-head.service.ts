import { singleton } from 'tsyringe';

import { GetStoreLocatorMapDto, storeLocatorStorePageMapBlockValidator } from '@malou-io/package-dto';
import { IStoreLocatorMapPage } from '@malou-io/package-models';

import { logger } from ':helpers/logger';
import { StoreLocatorOrganizationConfiguration } from ':modules/store-locator/entities/store-locator.organization-configuration.entity';
import { GenerateMapSchemaOrgMicroDataService } from ':modules/store-locator/services/generate-schema-org-micro-data/generate-map-micro-data.service';
import { mapStoreLocatorLanguageToLocale } from ':modules/store-locator/shared/utils';
import StoreLocatorMapPageRepository from ':modules/store-locator/store-locator-map-page.repository';
import { AwsS3 } from ':plugins/cloud-storage/s3';

@singleton()
export class FetchStoreLocatorMapHeadBlockService {
    constructor(
        private readonly _storeLocatorMapPageRepository: StoreLocatorMapPageRepository,
        private readonly _cloudStorageService: AwsS3,
        private readonly _generateMapSchemaOrgMicroDataService: GenerateMapSchemaOrgMicroDataService
    ) {}

    async execute({
        storeLocatorOrganizationConfig,
        storeLocatorMapPage,
    }: {
        storeLocatorOrganizationConfig: StoreLocatorOrganizationConfiguration;
        storeLocatorMapPage: IStoreLocatorMapPage;
    }): Promise<GetStoreLocatorMapDto['headBlock'] | null> {
        try {
            const organizationId = storeLocatorOrganizationConfig.organization.id;
            const organizationName = storeLocatorOrganizationConfig.organization.name;
            const googleAnalyticsId = storeLocatorOrganizationConfig.plugins?.googleAnalytics?.trackingId || '';
            const facebookImageUrl = storeLocatorMapPage.blocks.head.facebookImageUrl;
            const twitterImageUrl = storeLocatorMapPage.blocks.head.twitterImageUrl;
            const snippetImageUrl = storeLocatorMapPage.blocks.head.snippetImageUrl;
            const title = storeLocatorMapPage.blocks.head.title;
            const description = storeLocatorMapPage.blocks.head.description;
            const twitterDescription = storeLocatorMapPage.blocks.head.twitterDescription;
            const keywords = storeLocatorMapPage.blocks.head.keywords;
            const locale = mapStoreLocatorLanguageToLocale(storeLocatorMapPage.lang);
            const url = storeLocatorMapPage.fullUrl;
            const isLive = storeLocatorOrganizationConfig.isLive;

            const microdata = await this._generateMapSchemaOrgMicroDataService.execute({
                storeLocatorMapPage,
                title,
                description,
                organization: {
                    name: organizationName,
                    logo: `${this._cloudStorageService.getBucketBaseUrl()}/store-locator/organization/${organizationId}/favicons/favicon.png`,
                },
            });

            const headBlock = {
                organizationName,
                googleAnalyticsId,
                title,
                description,
                twitterDescription,
                keywords,
                microdata,
                facebookImageUrl,
                twitterImageUrl,
                snippetImageUrl,
                locale,
                url,
                // TODO: What should we do with this? [@hamza]
                xUserName: '',
                isLive,
            };

            const parsedHeadBlock = await storeLocatorStorePageMapBlockValidator.parseAsync(headBlock);

            logger.info('[STORE_LOCATOR] [Map] [Head block] Head block is valid, updating it as backup and returning it');
            await this._storeLocatorMapPageRepository.updateOne({
                filter: { _id: storeLocatorMapPage._id },
                update: { 'blocks.head.backup': parsedHeadBlock },
            });

            return headBlock;
        } catch (err) {
            logger.error('[STORE_LOCATOR] [Map] [Head block] Failed to fetch map head block, try to return backup', { err });

            const storeLocatorMapPageHeadBackupBlock = storeLocatorMapPage.blocks?.head?.backup;
            if (storeLocatorMapPageHeadBackupBlock) {
                try {
                    const parsedHeadBlock = await storeLocatorStorePageMapBlockValidator.parseAsync(storeLocatorMapPageHeadBackupBlock);
                    return parsedHeadBlock;
                } catch (error) {
                    logger.error('[STORE_LOCATOR] [Map] [Head block] Failed to validate backup', { err: error });
                }
            }

            return null;
        }
    }
}
