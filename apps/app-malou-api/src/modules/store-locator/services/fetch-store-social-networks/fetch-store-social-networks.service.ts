import { singleton } from 'tsyringe';

import { GetStoreLocatorStorePageDto, storeLocatorStorePageSocialNetworksBlockValidator } from '@malou-io/package-dto';
import { IStoreLocatorRestaurantPage } from '@malou-io/package-models';
import { getInstagramUserName, SocialNetworkKey } from '@malou-io/package-utils';

import { logger } from ':helpers/logger';
import { StoreLocatorOrganizationConfiguration } from ':modules/store-locator/entities/store-locator.organization-configuration.entity';
import { RestaurantPopulatedForStoreLocator } from ':modules/store-locator/services/fetch-store-head/interfaces';
import {
    InstagramProfileData,
    StoreLocatorInstagramDataService,
} from ':modules/store-locator/services/fetch-store-social-networks/social-networks/store-locator-instagram-data.service';
import StoreLocatorRestaurantPageRepository from ':modules/store-locator/store-locator-restaurant-page.repository';

export type SocialNetworksData = {
    instagramAccounts: Record<string, InstagramProfileData>;
};
@singleton()
export class FetchStoreLocatorSocialNetworksBlockService {
    constructor(
        private readonly _storeLocatorRestaurantPageRepository: StoreLocatorRestaurantPageRepository,
        private readonly _storeLocatorInstagramDataService: StoreLocatorInstagramDataService
    ) {}

    async execute({
        restaurant,
        storeLocatorRestaurantPage,
        storeLocatorOrganizationConfig,
        socialNetworksData,
    }: {
        restaurant: RestaurantPopulatedForStoreLocator;
        storeLocatorRestaurantPage: IStoreLocatorRestaurantPage;
        storeLocatorOrganizationConfig: StoreLocatorOrganizationConfiguration;
        socialNetworksData: SocialNetworksData;
    }): Promise<{ success: boolean; data: GetStoreLocatorStorePageDto['socialNetworksBlock'] | undefined }> {
        try {
            const socialNetworks = await this._computeSocialNetworksData({
                restaurant,
                storeLocatorRestaurantPage,
                storeLocatorOrganizationConfig,
                socialNetworksData,
            });

            const socialNetworksBlock = {
                title: storeLocatorRestaurantPage.blocks.socialNetworks.title,
                socialNetworks,
            };

            const parsedSocialNetworksBlock = await storeLocatorStorePageSocialNetworksBlockValidator.parseAsync(socialNetworksBlock);

            logger.info('[STORE_LOCATOR] [SocialNetworks block] SocialNetworks block is valid, updating it as backup and returning it');
            await this._storeLocatorRestaurantPageRepository.updateOne({
                filter: { _id: storeLocatorRestaurantPage._id },
                update: { 'blocks.socialNetworks.backup': parsedSocialNetworksBlock },
            });

            return { success: true, data: parsedSocialNetworksBlock };
        } catch (err) {
            logger.error('[STORE_LOCATOR] [SocialNetworks block] Failed to fetch store socialNetworks, try to return backup', { err });

            if (storeLocatorRestaurantPage.blocks?.socialNetworks?.backup) {
                try {
                    const socialNetworksBlock = storeLocatorRestaurantPage.blocks.socialNetworks.backup;
                    const parsedSocialNetworksBlock =
                        await storeLocatorStorePageSocialNetworksBlockValidator.parseAsync(socialNetworksBlock);

                    return { success: false, data: parsedSocialNetworksBlock };
                } catch (error) {
                    logger.error('[STORE_LOCATOR] [SocialNetworks block] Failed to validate backup', { err: error });
                }
            }

            return { success: false, data: undefined };
        }
    }

    async fetchSocialProfilesData({ organizationId }: { organizationId: string }): Promise<SocialNetworksData> {
        const instagramAccounts = await this._storeLocatorInstagramDataService.fetchInstagramDataForRestaurants({
            organizationId,
        });

        return { instagramAccounts };
    }

    private async _computeSocialNetworksData({
        restaurant,
        storeLocatorRestaurantPage,
        storeLocatorOrganizationConfig,
        socialNetworksData,
    }: {
        restaurant: RestaurantPopulatedForStoreLocator;
        storeLocatorRestaurantPage: IStoreLocatorRestaurantPage;
        storeLocatorOrganizationConfig: StoreLocatorOrganizationConfiguration;
        socialNetworksData: SocialNetworksData;
    }): Promise<{ instagram?: GetStoreLocatorStorePageDto['socialNetworksBlock']['socialNetworks']['instagram'] }> {
        const [instagram] = await Promise.all([
            this._computeInstagramData({
                restaurant,
                storeLocatorRestaurantPage,
                storeLocatorOrganizationConfig,
                instagramAccounts: socialNetworksData.instagramAccounts,
            }),
        ]);

        return { instagram };
    }

    private async _computeInstagramData({
        restaurant,
        storeLocatorRestaurantPage,
        storeLocatorOrganizationConfig,
        instagramAccounts,
    }: {
        restaurant: RestaurantPopulatedForStoreLocator;
        storeLocatorRestaurantPage: IStoreLocatorRestaurantPage;
        storeLocatorOrganizationConfig: StoreLocatorOrganizationConfiguration;
        instagramAccounts: SocialNetworksData['instagramAccounts'];
    }): Promise<GetStoreLocatorStorePageDto['socialNetworksBlock']['socialNetworks']['instagram'] | undefined> {
        const instagramUrl = restaurant.socialNetworkUrls?.find(({ key }) => key === SocialNetworkKey.INSTAGRAM)?.url;
        if (!instagramUrl) {
            logger.warn('[STORE_LOCATOR] [SocialNetworks block] No Instagram URL found for restaurant', {
                restaurantId: storeLocatorRestaurantPage.restaurantId.toString(),
            });
            return undefined;
        }

        const instagramProfileData = instagramAccounts[getInstagramUserName(instagramUrl) ?? ''];
        if (!instagramProfileData) {
            logger.warn('[STORE_LOCATOR] [SocialNetworks block] No Instagram profile data found for restaurant', {
                restaurantId: storeLocatorRestaurantPage.restaurantId.toString(),
                instagramUrl,
            });
            return undefined;
        }

        return await this._storeLocatorInstagramDataService.mapInstagramDataToSocialNetworksBlockSection({
            restaurant,
            restaurantPage: storeLocatorRestaurantPage,
            storeLocatorOrganizationConfig,
            instagramData: instagramProfileData,
        });
    }
}
