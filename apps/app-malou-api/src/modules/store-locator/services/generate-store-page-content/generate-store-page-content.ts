import { singleton } from 'tsyringe';

import { IOrganization, IRestaurant, IStoreLocatorRestaurantPageWithRestaurant, PopulateBuilderHelper } from '@malou-io/package-models';
import { StoreLocatorLanguage } from '@malou-io/package-utils';

import {
    AiStoreLocatorContentService,
    AiStoreLocatorContentType,
    AiStoreLocatorDescriptionsContent,
} from ':microservices/ai-store-locator-content-generator';
import { GenerateStoreLocatorContentPayload, GenerateStoreLocatorContentType } from ':modules/ai/interfaces/ai.interfaces';
import { StoreLocatorOrganizationConfiguration } from ':modules/store-locator/entities/store-locator.organization-configuration.entity';

export type RestaurantWithOrganization = PopulateBuilderHelper<IRestaurant, [{ path: 'organization' }]>;
interface WholeStorePageContent {
    pageUrl: string;
    title: string;
    metaDescription: string;
    twitterDescription: string;
    descriptions: AiStoreLocatorDescriptionsContent['blocks'];
    ctaTitle: string;
    reviewsTitle: string;
    galleryTitle: string;
    gallerySubtitle: string;
    socialNetworksTitle: string;
}

@singleton()
export class GenerateStorePageContentService {
    constructor(private readonly _aiStoreLocatorContentService: AiStoreLocatorContentService) {}

    async generateWholePageContent({
        restaurant,
        storeLocatorOrganizationConfig,
        lang,
    }: {
        restaurant: RestaurantWithOrganization;
        storeLocatorOrganizationConfig: StoreLocatorOrganizationConfiguration;
        lang: StoreLocatorLanguage;
    }): Promise<WholeStorePageContent> {
        // todo store-locator see if we should filter by language
        const keywords = storeLocatorOrganizationConfig.aiSettings.keywords.map(({ text }) => text.toLowerCase().trim());
        const brandTone = storeLocatorOrganizationConfig.aiSettings.tone;
        const restaurantOffers = storeLocatorOrganizationConfig.aiSettings.attributes.map(({ attributeName }) =>
            (attributeName.en || attributeName.fr).toLowerCase().trim()
        );

        const [
            { text: pageUrl },
            { text: title },
            { text: twitterDescription },
            { blocks: descriptions },
            { text: ctaTitle },
            { text: reviewsTitle },
        ] = await Promise.all([
            this.generateStoreLocatorContent({
                restaurant,
                organization: restaurant.organization,
                keywords,
                lang,
                type: GenerateStoreLocatorContentType.RESTAURANT_PAGE_URL_GENERATION,
            }),
            this.generateStoreLocatorContent({
                restaurant,
                organization: restaurant.organization,
                keywords,
                lang,
                type: GenerateStoreLocatorContentType.H1_TITLE_GENERATION,
            }),
            this.generateStoreLocatorContent({
                type: GenerateStoreLocatorContentType.HEAD_META_TWITTER_DESCRIPTION_GENERATION,
                restaurant,
                organization: restaurant.organization,
                keywords,
                lang,
                params: {
                    brandTone,
                    restaurantOffers,
                },
            }),
            this.generateStoreLocatorContent({
                type: GenerateStoreLocatorContentType.DESCRIPTION_BLOCK_GENERATION,
                restaurant,
                organization: restaurant.organization,
                keywords,
                lang,
                params: {
                    brandTone,
                    restaurantOffers,
                },
            }),
            this.generateStoreLocatorContent({
                type: GenerateStoreLocatorContentType.CTA_BLOCK_TITLE_GENERATION,
                restaurant,
                organization: restaurant.organization,
                keywords,
                lang,
            }),
            this.generateStoreLocatorContent({
                type: GenerateStoreLocatorContentType.REVIEWS_BLOCK_TITLE_GENERATION,
                restaurant,
                organization: restaurant.organization,
                keywords,
                lang,
            }),
        ]);

        const [{ text: metaDescription }, { text: galleryTitle }, { text: socialNetworksTitle }] = await Promise.all([
            this.generateStoreLocatorContent({
                type: GenerateStoreLocatorContentType.HEAD_META_DESCRIPTION_GENERATION,
                restaurant,
                organization: restaurant.organization,
                keywords,
                lang,
                params: {
                    context: [{ [GenerateStoreLocatorContentType.H1_TITLE_GENERATION]: title }],
                },
            }),
            this.generateStoreLocatorContent({
                type: GenerateStoreLocatorContentType.GALLERY_BLOCK_TITLE_GENERATION,
                restaurant,
                organization: restaurant.organization,
                keywords,
                lang,
                params: {
                    context: [{ [GenerateStoreLocatorContentType.H1_TITLE_GENERATION]: title }],
                },
            }),
            this.generateStoreLocatorContent({
                type: GenerateStoreLocatorContentType.SOCIAL_MEDIA_BLOCK_TITLE_GENERATION,
                restaurant,
                organization: restaurant.organization,
                keywords,
                lang,
                params: {
                    context: [{ [GenerateStoreLocatorContentType.REVIEWS_BLOCK_TITLE_GENERATION]: reviewsTitle }],
                },
            }),
        ]);

        const [{ text: gallerySubtitle }] = await Promise.all([
            this.generateStoreLocatorContent({
                type: GenerateStoreLocatorContentType.GALLERY_BLOCK_SUBTITLE_GENERATION,
                restaurant,
                organization: restaurant.organization,
                keywords,
                lang,
                params: {
                    brandTone,
                    restaurantOffers,
                    context: [{ [GenerateStoreLocatorContentType.GALLERY_BLOCK_TITLE_GENERATION]: galleryTitle }],
                },
            }),
        ]);

        return {
            pageUrl,
            title,
            metaDescription,
            twitterDescription,
            descriptions,
            ctaTitle,
            reviewsTitle,
            galleryTitle,
            gallerySubtitle,
            socialNetworksTitle,
        };
    }

    async generateSpecificPageContent<T extends GenerateStoreLocatorContentType>({
        type,
        storeLocatorRestaurantPage,
        storeLocatorOrganizationConfig,
        lang,
    }: {
        type: T;
        storeLocatorOrganizationConfig: StoreLocatorOrganizationConfiguration;
        storeLocatorRestaurantPage: IStoreLocatorRestaurantPageWithRestaurant;
        lang: StoreLocatorLanguage;
    }): Promise<AiStoreLocatorContentType<T>> {
        const { restaurant } = storeLocatorRestaurantPage;
        const { organization } = storeLocatorOrganizationConfig;

        const keywords = storeLocatorOrganizationConfig.aiSettings.keywords.map(({ text }) => text.toLowerCase().trim());
        const brandTone = storeLocatorOrganizationConfig.aiSettings.tone;
        const restaurantOffers = storeLocatorOrganizationConfig.aiSettings.attributes.map(({ attributeName }) =>
            (attributeName.en || attributeName.fr).toLowerCase().trim()
        );

        let params: Partial<GenerateStoreLocatorContentPayload['restaurantData']> = {};
        switch (type) {
            case GenerateStoreLocatorContentType.RESTAURANT_PAGE_URL_GENERATION:
            case GenerateStoreLocatorContentType.H1_TITLE_GENERATION:
            case GenerateStoreLocatorContentType.CTA_BLOCK_TITLE_GENERATION:
            case GenerateStoreLocatorContentType.REVIEWS_BLOCK_TITLE_GENERATION:
                break;
            case GenerateStoreLocatorContentType.HEAD_META_DESCRIPTION_GENERATION:
                params = {
                    context: [
                        { [GenerateStoreLocatorContentType.H1_TITLE_GENERATION]: storeLocatorRestaurantPage.blocks.information.title },
                    ],
                };
                break;
            case GenerateStoreLocatorContentType.HEAD_META_TWITTER_DESCRIPTION_GENERATION:
                params = {
                    brandTone,
                    restaurantOffers,
                };
                break;
            case GenerateStoreLocatorContentType.DESCRIPTION_BLOCK_GENERATION:
                params = {
                    brandTone,
                    restaurantOffers,
                };
                break;
            case GenerateStoreLocatorContentType.GALLERY_BLOCK_TITLE_GENERATION:
                params = {
                    context: [
                        { [GenerateStoreLocatorContentType.H1_TITLE_GENERATION]: storeLocatorRestaurantPage.blocks.information.title },
                    ],
                };
                break;
            case GenerateStoreLocatorContentType.GALLERY_BLOCK_SUBTITLE_GENERATION:
                params = {
                    brandTone,
                    restaurantOffers,
                    context: [
                        {
                            [GenerateStoreLocatorContentType.GALLERY_BLOCK_TITLE_GENERATION]:
                                storeLocatorRestaurantPage.blocks.gallery.title,
                        },
                    ],
                };
                break;
            case GenerateStoreLocatorContentType.SOCIAL_MEDIA_BLOCK_TITLE_GENERATION:
                params = {
                    context: [
                        {
                            [GenerateStoreLocatorContentType.REVIEWS_BLOCK_TITLE_GENERATION]:
                                storeLocatorRestaurantPage.blocks.reviews.title,
                        },
                    ],
                };
                break;
        }

        const payload = {
            restaurantName: restaurant.name,
            organizationName: organization.name,
            address: {
                locality: restaurant.address?.locality,
                postalCode: restaurant.address?.postalCode,
            },
            language: lang,
            keywords,
            ...params,
        };

        const response = await this._aiStoreLocatorContentService.generateStoreLocatorContent(type, payload);

        return response.aiResponse;
    }

    async generateStoreLocatorContent<T extends GenerateStoreLocatorContentType>({
        type,
        restaurant,
        organization,
        keywords,
        lang,
        params,
    }: {
        type: T;
        restaurant: IRestaurant;
        keywords: string[];
        organization: IOrganization;
        lang: StoreLocatorLanguage;
        params?: Partial<GenerateStoreLocatorContentPayload['restaurantData']>;
    }): Promise<AiStoreLocatorContentType<T>> {
        const payload = {
            restaurantName: restaurant.name,
            organizationName: organization.name,
            address: {
                locality: restaurant.address?.locality,
                postalCode: restaurant.address?.postalCode,
            },
            language: lang,
            keywords,
            ...params,
        };

        const response = await this._aiStoreLocatorContentService.generateStoreLocatorContent(type, payload);

        return response.aiResponse;
    }
}
