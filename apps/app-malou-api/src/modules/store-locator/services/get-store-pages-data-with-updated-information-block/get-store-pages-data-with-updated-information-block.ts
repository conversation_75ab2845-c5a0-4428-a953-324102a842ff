import assert from 'assert';
import { singleton } from 'tsyringe';

import { GetStoreLocatorStorePageDto } from '@malou-io/package-dto';
import { IStoreLocatorRestaurantPage, toDbId } from '@malou-io/package-models';
import { MalouErrorCode, StoreLocatorLanguage, StoreLocatorPageStatus } from '@malou-io/package-utils';

import { MalouError } from ':helpers/classes/malou-error';
import { FetchStoreLocatorInformationBlockService } from ':modules/store-locator/services/fetch-store-information/fetch-store-information.service';
import StoreLocatorRestaurantPageRepository from ':modules/store-locator/store-locator-restaurant-page.repository';

@singleton()
export class GetStorePagesDataWithUpdatedInformationBlockService {
    constructor(
        private readonly _fetchStoreLocatorInformationBlockService: FetchStoreLocatorInformationBlockService,
        private readonly _storeLocatorRestaurantPageRepository: StoreLocatorRestaurantPageRepository
    ) {}

    async execute(
        organizationId: string,
        options: {
            lang?: StoreLocatorLanguage;
            isForEdit?: boolean;
        } = {}
    ): Promise<
        { storeLocatorRestaurantPage: IStoreLocatorRestaurantPage; informationBlock: GetStoreLocatorStorePageDto['informationBlock'] }[]
    > {
        const storeLocatorRestaurantPages = await this._storeLocatorRestaurantPageRepository.find({
            filter: {
                organizationId: toDbId(organizationId),
                ...(options.lang && { lang: options.lang }),
                status: options.isForEdit ? StoreLocatorPageStatus.DRAFT : StoreLocatorPageStatus.PUBLISHED,
            },
            options: { lean: true },
        });

        const storesWithInformationBlock = await Promise.all(
            storeLocatorRestaurantPages.map(async (storeLocatorRestaurantPage) => {
                const storeInformationBlock = await this._fetchStoreLocatorInformationBlockService.execute({
                    restaurantId: storeLocatorRestaurantPage.restaurantId.toString(),
                    storeLocatorRestaurantPage,
                });
                assert(
                    storeInformationBlock.data,
                    new MalouError(MalouErrorCode.STORE_LOCATOR_DATA_FETCH_FAILED, {
                        message: 'Failed to fetch store information block',
                        metadata: { organizationId, restaurantId: storeLocatorRestaurantPage.restaurantId.toString() },
                    })
                );
                return {
                    storeLocatorRestaurantPage,
                    informationBlock: storeInformationBlock.data,
                };
            })
        );

        return storesWithInformationBlock;
    }
}
