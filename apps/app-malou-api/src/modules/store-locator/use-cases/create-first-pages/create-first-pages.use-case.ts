import assert from 'node:assert';
import { singleton } from 'tsyringe';

import { getUrlDomain, MediaCategory, MediaType, PlatformKey, StoreLocatorLanguage } from '@malou-io/package-utils';

import { fetchImage } from ':helpers/fetch-image-from-remote';
import { logger } from ':helpers/logger';
import { filterByRequiredKeys } from ':helpers/validators/filter-by-required-keys';
import { AiMediaDescriptionService } from ':microservices/ai-media-description';
import { GenerateMediaDescriptionImageType } from ':modules/ai/interfaces/ai.interfaces';
import { MediasRepository } from ':modules/media/medias.repository';
import PlatformsRepository from ':modules/platforms/platforms.repository';
import RestaurantsRepository from ':modules/restaurants/restaurants.repository';
import { StoreLocatorOrganizationConfiguration } from ':modules/store-locator/entities/store-locator.organization-configuration.entity';
import {
    GenerateStorePageContentService,
    RestaurantWithOrganization,
} from ':modules/store-locator/services/generate-store-page-content/generate-store-page-content';
import { PreprocessStoreLocatorPictureService } from ':modules/store-locator/services/preprocess-store-locator-picture/preprocess-store-locator-picture.service';
import StoreLocatorOrganizationConfigRepository from ':modules/store-locator/store-locator-organization-config.repository';
import StoreLocatorRestaurantPageRepository from ':modules/store-locator/store-locator-restaurant-page.repository';
import { AwsS3 } from ':plugins/cloud-storage/s3';
import { GmbPlaceActionsProvider } from ':providers/google/gmb.place-actions.provider';
import { GmbRefreshTokenService } from ':services/credentials/gmb/gmb-refresh-token.service';

@singleton()
export class StoreLocatorCreateFirstPagesUseCase {
    private readonly _DEFAULT_PLACEHOLDER_IMAGE_URL = 'https://malou-production.s3.eu-west-3.amazonaws.com/assets/placeholder.jpg';

    constructor(
        private readonly _storeLocatorOrganizationConfigRepository: StoreLocatorOrganizationConfigRepository,
        private readonly _storeLocatorRestaurantPageRepository: StoreLocatorRestaurantPageRepository,
        private readonly _generateStorePageContentService: GenerateStorePageContentService,
        private readonly _restaurantsRepository: RestaurantsRepository,
        private readonly _mediasRepository: MediasRepository,
        private readonly _platformsRepository: PlatformsRepository,
        private readonly _gmbRefreshTokenService: GmbRefreshTokenService,
        private readonly _gmbPlaceActionsProvider: GmbPlaceActionsProvider,
        private readonly _preprocessStoreLocatorPictureService: PreprocessStoreLocatorPictureService,
        private readonly _cloudStorageService: AwsS3,
        private readonly _aiMediaDescriptionService: AiMediaDescriptionService
    ) {}

    async execute(organizationId: string): Promise<void> {
        const [storeLocatorOrganizationConfig, restaurants] = await Promise.all([
            this._storeLocatorOrganizationConfigRepository.getOrganizationConfiguration(organizationId),
            this._restaurantsRepository.find({
                filter: { organizationId },
                options: { lean: true, populate: [{ path: 'organization', select: { _id: 1, name: 1 } }] },
            }),
        ]);
        assert(storeLocatorOrganizationConfig, 'Store Locator Organization Config not found');

        const desiredLanguages = [StoreLocatorLanguage.FR];
        await Promise.all(
            restaurants.map(async (restaurant) =>
                this._generateRestaurantPages({ restaurant, storeLocatorOrganizationConfig, langs: desiredLanguages })
            )
        );
    }

    private async _generateRestaurantPages({
        restaurant,
        storeLocatorOrganizationConfig,
        langs,
    }: {
        restaurant: RestaurantWithOrganization;
        storeLocatorOrganizationConfig: StoreLocatorOrganizationConfiguration;
        langs: StoreLocatorLanguage[];
    }): Promise<void> {
        const imageUrls = await this._uploadImages({ restaurant });

        await Promise.all(
            langs.map(async (lang) => this._generateRestaurantPage({ restaurant, storeLocatorOrganizationConfig, lang, imageUrls }))
        );
    }

    private async _generateRestaurantPage({
        restaurant,
        storeLocatorOrganizationConfig,
        lang,
        imageUrls,
    }: {
        restaurant: RestaurantWithOrganization;
        storeLocatorOrganizationConfig: StoreLocatorOrganizationConfiguration;
        lang: StoreLocatorLanguage;
        imageUrls: {
            head: { uploadedUrl: string; uploadedUrlForAi: string }[];
            information: { uploadedUrl: string; uploadedUrlForAi: string }[];
            gallery: { uploadedUrl: string; uploadedUrlForAi: string }[];
            descriptions: { uploadedUrl: string; uploadedUrlForAi: string }[];
        };
    }): Promise<void> {
        const [
            {
                pageUrl,
                title,
                metaDescription,
                twitterDescription,
                descriptions,
                ctaTitle,
                reviewsTitle,
                galleryTitle,
                gallerySubtitle,
                socialNetworksTitle,
            },
            usefulLinks,
            medias,
        ] = await Promise.all([
            this._generateStorePageContentService.generateWholePageContent({
                restaurant,
                storeLocatorOrganizationConfig,
                lang,
            }),
            this._getCtas(restaurant),
            this._getMedias({
                restaurant,
                storeLocatorOrganizationConfig,
                lang,
                imageUrls,
            }),
        ]);

        const orderCta = restaurant.orderUrl ? { text: 'Commander', url: restaurant.orderUrl } : usefulLinks[0];
        const keywords = storeLocatorOrganizationConfig.aiSettings.keywords.map((keyword) => keyword.text).join(', ');

        await this._storeLocatorRestaurantPageRepository.create({
            data: {
                restaurantId: restaurant._id,
                organizationId: restaurant.organization._id,
                lang,
                fullUrl: `${storeLocatorOrganizationConfig.baseUrl}${pageUrl}`,
                relativePath: pageUrl.substring(1),
                blocks: {
                    head: {
                        title,
                        description: metaDescription,
                        twitterDescription,
                        keywords,
                        // todo store-locator add mapping between categories and schema.org cuisine types
                        schemaOrgCuisineType: 'Burger, Fast Food, Organic, Vegetarian, Vegan',
                        facebookImageUrl: medias.head[0].url,
                        twitterImageUrl: medias.head[0].url,
                        snippetImageUrl: medias.head[0].url,
                    },
                    information: {
                        title,
                        image: medias.information[0],
                        cta: orderCta,
                    },
                    gallery: {
                        title: galleryTitle,
                        subtitle: gallerySubtitle,
                        images: medias.gallery,
                    },
                    reviews: {
                        title: reviewsTitle,
                        cta: orderCta,
                    },
                    callToActions: {
                        title: ctaTitle,
                        ctas: usefulLinks.slice(4),
                    },
                    descriptions: {
                        items: descriptions.map((description, index) => ({
                            title: description.title,
                            image: medias.descriptions[index],
                            blocks: description.sections.map(({ subtitle, text }) => ({
                                title: subtitle,
                                text,
                            })),
                        })),
                    },
                    socialNetworks: {
                        title: socialNetworksTitle,
                    },
                },
            },
        });
    }

    private async _getCtas(restaurant: RestaurantWithOrganization): Promise<
        {
            url: string;
            text: string;
        }[]
    > {
        const links: { url: string; text: string }[] = [];

        try {
            const platform = await this._platformsRepository.getPlatformByRestaurantIdAndPlatformKey(
                restaurant._id.toString(),
                PlatformKey.GMB
            );
            assert(platform, 'Platform not found for restaurant');

            const { credentials, apiEndpointV2 } = platform;
            const credentialId = credentials?.[0];
            assert(credentialId, 'No credential found for platform');
            assert(apiEndpointV2, 'No API endpoint V2 found for platform');

            const locationId = apiEndpointV2.replace('locations/', '');
            const { accessToken } = await this._gmbRefreshTokenService.getFreshTokenIfNecessary(credentialId);

            const {
                data: { placeActionLinks },
            } = await this._gmbPlaceActionsProvider.listPlaceActionLinks({
                accessToken,
                locationId,
            });

            if (placeActionLinks && placeActionLinks.length > 0) {
                links.push(
                    ...filterByRequiredKeys(placeActionLinks, ['uri']).map(({ uri }) => ({
                        url: uri,
                        text: getUrlDomain(uri) || 'Lien externe',
                    }))
                );
            }
        } catch (err) {
            logger.error('[STORE_LOCATOR] Failed to fetch GMB place action links', { err });
        }

        if (restaurant.menuUrl) {
            links.push({
                url: restaurant.menuUrl,
                text: 'Menu',
            });
        }

        if (restaurant.orderUrl) {
            links.push({
                url: restaurant.orderUrl,
                text: 'Commander',
            });
        }

        if (restaurant.reservationUrl) {
            links.push({
                url: restaurant.reservationUrl,
                text: 'Réserver',
            });
        }

        // Backup
        links.push({
            url: 'https://malou.io',
            text: 'Lien backup',
        });

        return links;
    }

    private async _getMedias({
        restaurant,
        storeLocatorOrganizationConfig,
        lang,
        imageUrls,
    }: {
        restaurant: RestaurantWithOrganization;
        storeLocatorOrganizationConfig: StoreLocatorOrganizationConfiguration;
        lang: StoreLocatorLanguage;
        imageUrls: {
            head: { uploadedUrl: string; uploadedUrlForAi: string }[];
            information: { uploadedUrl: string; uploadedUrlForAi: string }[];
            gallery: { uploadedUrl: string; uploadedUrlForAi: string }[];
            descriptions: { uploadedUrl: string; uploadedUrlForAi: string }[];
        };
    }): Promise<{
        head: { description: string; url: string }[];
        information: { description: string; url: string }[];
        gallery: { description: string; url: string }[];
        descriptions: { description: string; url: string }[];
    }> {
        try {
            const keywords = storeLocatorOrganizationConfig.aiSettings.keywords.map((keyword) => keyword.text);
            const finalMedias: Record<'head' | 'information' | 'gallery' | 'descriptions', { description: string; url: string }[]> = {
                head: [],
                information: [],
                gallery: [],
                descriptions: [],
            };
            const imageTypeMapping: Record<string, GenerateMediaDescriptionImageType> = {
                information: GenerateMediaDescriptionImageType.ALT_TEXT_INFORMATION_BLOCK,
                gallery: GenerateMediaDescriptionImageType.ALT_TEXT_GALLERY_BLOCK,
                descriptions: GenerateMediaDescriptionImageType.ALT_TEXT_DESCRIPTIONS_BLOCK,
            };

            await Promise.all(
                Object.entries(imageUrls).map(async ([section, urls]) => {
                    const imageType = imageTypeMapping[section];

                    finalMedias[section] = await Promise.all(
                        urls.map(async (url) => {
                            try {
                                let description = '';
                                if (imageType) {
                                    const data = await this._aiMediaDescriptionService.generateMediaDescription({
                                        imageLink: url.uploadedUrlForAi,
                                        imageType,
                                        language: lang,
                                        keywords,
                                        restaurantName: restaurant.name,
                                    });
                                    description = data.aiResponse.description;
                                }

                                return {
                                    description,
                                    url: url.uploadedUrl,
                                };
                            } catch (error) {
                                logger.error(`[STORE LOCATOR] Failed to process image for ${section}`, { error });

                                return {
                                    description: '',
                                    url: url.uploadedUrl,
                                };
                            }
                        })
                    );
                })
            );

            return finalMedias;
        } catch (err) {
            logger.error('[STORE_LOCATOR] Failed to generate media descriptions', { restaurantId: restaurant._id, error: err });

            return {
                head: imageUrls.head.map((url) => ({ description: '', url: url.uploadedUrl })),
                information: imageUrls.information.map((url) => ({ description: '', url: url.uploadedUrl })),
                gallery: imageUrls.gallery.map((url) => ({ description: '', url: url.uploadedUrl })),
                descriptions: imageUrls.descriptions.map((url) => ({ description: '', url: url.uploadedUrl })),
            };
        }
    }

    private async _uploadImages({ restaurant }: { restaurant: RestaurantWithOrganization }): Promise<{
        head: { uploadedUrl: string; uploadedUrlForAi: string }[];
        information: { uploadedUrl: string; uploadedUrlForAi: string }[];
        gallery: { uploadedUrl: string; uploadedUrlForAi: string }[];
        descriptions: { uploadedUrl: string; uploadedUrlForAi: string }[];
    }> {
        try {
            const organizationId = restaurant.organization._id.toString();
            const restaurantId = restaurant._id.toString();

            const mediasPerSection = await this._fetchUrlsFromMedias({ restaurant });
            const finalMedias: Record<
                'head' | 'information' | 'gallery' | 'descriptions',
                { uploadedUrl: string; uploadedUrlForAi: string }[]
            > = {
                head: [],
                information: [],
                gallery: [],
                descriptions: [],
            };

            // Clear S3 bucket
            const baseS3Url = `store-locator/organization/${organizationId}/restaurants/${restaurantId}`;
            try {
                await this._cloudStorageService.emptyDirectory(baseS3Url);
            } catch (err) {
                logger.warn('[STORE LOCATOR] Failed to empty publications pictures folder', { err });
            }

            await Promise.all(
                Object.entries(mediasPerSection).map(async ([section, urls]) => {
                    finalMedias[section] = await Promise.all(
                        urls.map(async (url, imageIndex) => {
                            try {
                                const imageBuffer = await fetchImage(url);
                                if (!imageBuffer) {
                                    throw new Error(`Failed to fetch image from ${url}`);
                                }

                                const imageRemoteKey = `${baseS3Url}/${section}/photo${imageIndex}`;
                                const uploadedUrls = await this._preprocessStoreLocatorPictureService.uploadImage({
                                    buffer: imageBuffer,
                                    s3Key: imageRemoteKey,
                                });

                                if (!uploadedUrls || !uploadedUrls.uploadedUrl || !uploadedUrls.uploadedUrlForAi) {
                                    throw new Error(`Failed to upload image to S3 for ${section}`);
                                }

                                return uploadedUrls;
                            } catch (error) {
                                logger.error(`[STORE_LOCATOR] Failed to process image for ${section}`, { error });

                                return {
                                    uploadedUrl: this._DEFAULT_PLACEHOLDER_IMAGE_URL,
                                    uploadedUrlForAi: this._DEFAULT_PLACEHOLDER_IMAGE_URL,
                                };
                            }
                        })
                    );
                })
            );

            return finalMedias;
        } catch (err) {
            logger.error('[STORE_LOCATOR] Failed to generate media descriptions', { restaurantId: restaurant._id, error: err });

            return {
                head: [{ uploadedUrl: this._DEFAULT_PLACEHOLDER_IMAGE_URL, uploadedUrlForAi: this._DEFAULT_PLACEHOLDER_IMAGE_URL }],
                information: [{ uploadedUrl: this._DEFAULT_PLACEHOLDER_IMAGE_URL, uploadedUrlForAi: this._DEFAULT_PLACEHOLDER_IMAGE_URL }],
                gallery: Array.from({ length: 7 }, () => ({
                    uploadedUrl: this._DEFAULT_PLACEHOLDER_IMAGE_URL,
                    uploadedUrlForAi: this._DEFAULT_PLACEHOLDER_IMAGE_URL,
                })),
                descriptions: Array.from({ length: 2 }, () => ({
                    uploadedUrl: this._DEFAULT_PLACEHOLDER_IMAGE_URL,
                    uploadedUrlForAi: this._DEFAULT_PLACEHOLDER_IMAGE_URL,
                })),
            };
        }
    }

    private async _fetchUrlsFromMedias({ restaurant }: { restaurant: RestaurantWithOrganization }): Promise<{
        head: string[];
        information: string[];
        gallery: string[];
        descriptions: string[];
    }> {
        const medias = await this._mediasRepository.find({
            filter: {
                restaurantId: restaurant._id,
                category: MediaCategory.ADDITIONAL,
                type: MediaType.PHOTO,
            },
            projection: { dimensions: 1, urls: 1 },
            options: { lean: true },
        });

        if (!medias || medias.length === 0) {
            logger.warn('[STORE_LOCATOR] No medias found for restaurant', { restaurantId: restaurant._id });
            return {
                head: [this._DEFAULT_PLACEHOLDER_IMAGE_URL],
                information: [this._DEFAULT_PLACEHOLDER_IMAGE_URL],
                gallery: Array.from({ length: 7 }, () => this._DEFAULT_PLACEHOLDER_IMAGE_URL),
                descriptions: Array.from({ length: 2 }, () => this._DEFAULT_PLACEHOLDER_IMAGE_URL),
            };
        }

        // todo store-locator pick best images thanks to dimensions

        // Dispatch medias into information, gallery, ... to have the most different images possible, if there are more than needed, and reusing them the least if images count is not enough
        // information has 1 image, gallery 7, descriptions 2 and head 2
        const usedMediasOccurrence: Record<string, number> = {};
        medias.forEach((media) => (usedMediasOccurrence[media._id.toString()] = usedMediasOccurrence[media._id.toString()] ?? 0));

        const getNextMedia = (): string => {
            const minimumOccurrence = Math.min(...Object.values(usedMediasOccurrence));
            const mediasUsedTheLeast = Object.keys(usedMediasOccurrence).filter(
                (mediaId) => usedMediasOccurrence[mediaId] === minimumOccurrence
            );
            const nextMediaId = mediasUsedTheLeast[Math.floor(Math.random() * mediasUsedTheLeast.length)];
            usedMediasOccurrence[nextMediaId]++;

            return medias.find((media) => media._id.toString() === nextMediaId)?.urls?.original ?? this._DEFAULT_PLACEHOLDER_IMAGE_URL;
        };

        const mediasPerSection: Record<'information' | 'gallery' | 'descriptions' | 'head', string[]> = {
            head: [getNextMedia()],
            information: [getNextMedia()],
            gallery: Array.from({ length: 7 }, getNextMedia),
            descriptions: Array.from({ length: 2 }, getNextMedia),
        };

        return mediasPerSection;
    }
}
