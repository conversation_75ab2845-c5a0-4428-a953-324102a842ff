import { singleton } from 'tsyringe';

import { GetStoreLocatorPagesDto } from '@malou-io/package-dto';

import { logger } from ':helpers/logger';
import { GetStorePagesDataWithUpdatedInformationBlockService } from ':modules/store-locator/services/get-store-pages-data-with-updated-information-block/get-store-pages-data-with-updated-information-block';
import StoreLocatorOrganizationConfigRepository from ':modules/store-locator/store-locator-organization-config.repository';
import { GetStoreLocatorMapUseCase } from ':modules/store-locator/use-cases/get-store-locator-map/get-store-locator-map.use-case';

@singleton()
export class GetStoreLocatorStorePagesForEditUseCase {
    constructor(
        private readonly _getStoreLocatorMapUseCase: GetStoreLocatorMapUseCase,
        private readonly _storeLocatorOrganizationConfigRepository: StoreLocatorOrganizationConfigRepository,
        private readonly _getStorePagesDataWithUpdatedInformationBlockService: GetStorePagesDataWithUpdatedInformationBlockService
    ) {}

    async execute(organizationId: string): Promise<GetStoreLocatorPagesDto> {
        const storeLocatorOrganizationConfig =
            await this._storeLocatorOrganizationConfigRepository.getOrganizationConfiguration(organizationId);

        const [stores, mapPages] = await Promise.all([
            this._getStoreLocatorStores(organizationId),
            this._getStoreLocatorMapUseCase.execute(storeLocatorOrganizationConfig, {
                isForEdit: true,
            }),
        ]);

        return {
            restaurantsPages: stores,
            mapPages: mapPages ?? [],
            urls: {},
        };
    }

    /**
     * We use the backup blocks for Enhancing the performance of fetching store locator stores for edit,
     * (Head, Reviews, Social Networks) blocks are not used in the edit mode,
     */

    private async _getStoreLocatorStores(organizationId: string): Promise<GetStoreLocatorPagesDto['restaurantsPages']> {
        try {
            const storesWithInformationBlock = await this._getStorePagesDataWithUpdatedInformationBlockService.execute(organizationId, {
                isForEdit: true,
            });
            return storesWithInformationBlock.map((storePageWithInformationBlock) => {
                const { informationBlock, storeLocatorRestaurantPage } = storePageWithInformationBlock;

                const headBlockBackup = storeLocatorRestaurantPage.blocks?.head
                    ?.backup as GetStoreLocatorPagesDto['restaurantsPages'][number]['headBlock'];
                const reviewsBlockBackup = storeLocatorRestaurantPage.blocks?.reviews
                    ?.backup as GetStoreLocatorPagesDto['restaurantsPages'][number]['reviewsBlock'];
                const socialNetworksBlockBackup = storeLocatorRestaurantPage.blocks?.socialNetworks
                    ?.backup as GetStoreLocatorPagesDto['restaurantsPages'][number]['socialNetworksBlock'];
                return {
                    lang: storeLocatorRestaurantPage.lang,
                    relativePath: storeLocatorRestaurantPage.relativePath,
                    id: storeLocatorRestaurantPage.restaurantId.toString(),
                    organizationName: headBlockBackup.organizationName,
                    headBlock: headBlockBackup,
                    name: '',
                    internalName: '',
                    shouldDisplayWhiteMark: false,
                    styles: {},
                    informationBlock,
                    galleryBlock: storeLocatorRestaurantPage.blocks?.gallery,
                    reviewsBlock: {
                        title: storeLocatorRestaurantPage.blocks?.reviews?.title,
                        cta: storeLocatorRestaurantPage.blocks?.reviews?.cta,
                        reviews: reviewsBlockBackup?.reviews ?? [],
                    },
                    socialNetworksBlock: {
                        title: storeLocatorRestaurantPage.blocks?.socialNetworks?.title,
                        socialNetworks: socialNetworksBlockBackup.socialNetworks ?? [],
                    },
                    callToActionsBlock: {
                        title: storeLocatorRestaurantPage.blocks?.callToActions?.title,
                        links: (storeLocatorRestaurantPage.blocks?.callToActions?.ctas ?? []).map((cta) => ({
                            url: cta.url,
                            text: cta.text,
                        })),
                    },
                    descriptionsBlock: {
                        items: (storeLocatorRestaurantPage.blocks?.descriptions?.items ?? []).map((item) => ({
                            title: item.title,
                            imageUrl: item.image?.url,
                            imageDescription: item.image?.description,
                            blocks: item.blocks,
                        })),
                    },
                };
            });
        } catch (err) {
            logger.error('[STORE_LOCATOR] Failed to get stores for edit', { err });
            throw err;
        }
    }
}
