import lodash from 'lodash';
import { singleton } from 'tsyringe';

import { GetStoreLocatorStorePageDto } from '@malou-io/package-dto';
import { isNotNil, MalouErrorCode } from '@malou-io/package-utils';

import { AsyncLocalStorageService } from ':helpers/classes/async-local-storage-service';
import { MalouError } from ':helpers/classes/malou-error';
import { logger } from ':helpers/logger';
import { StoreLocatorOrganizationConfiguration } from ':modules/store-locator/entities/store-locator.organization-configuration.entity';
import { FetchStoreLocatorStoreService } from ':modules/store-locator/services/fetch-store-data/fetch-store-data.service';
import { FetchStoreLocatorSocialNetworksBlockService } from ':modules/store-locator/services/fetch-store-social-networks/fetch-store-social-networks.service';
import StoreLocatorRestaurantPageRepository from ':modules/store-locator/store-locator-restaurant-page.repository';
import { SlackChannel, SlackService } from ':services/slack.service';

@singleton()
export class GetStoreLocatorStoresUseCase {
    constructor(
        private readonly _fetchStoreLocatorStoreService: FetchStoreLocatorStoreService,
        private readonly _asyncLocalStorageService: AsyncLocalStorageService,
        private readonly _storeLocatorRestaurantPageRepository: StoreLocatorRestaurantPageRepository,
        private readonly _fetchStoreLocatorSocialNetworksBlockService: FetchStoreLocatorSocialNetworksBlockService,
        private readonly _slackService: SlackService
    ) {}

    async execute(storeLocatorOrganizationConfig: StoreLocatorOrganizationConfiguration): Promise<GetStoreLocatorStorePageDto[]> {
        try {
            const organizationId = storeLocatorOrganizationConfig.organization.id;

            // Fetch social networks data beforehand (to avoid multiple calls if several stores have the same social networks)
            const socialNetworksData = await this._fetchStoreLocatorSocialNetworksBlockService.fetchSocialProfilesData({
                organizationId,
            });

            const storeLocatorRestaurantPages = await this._storeLocatorRestaurantPageRepository.getStoreLocatorStorePages(organizationId);

            const stores = await Promise.all(
                storeLocatorRestaurantPages.map((storeLocatorRestaurantPage) => {
                    const restaurantId = storeLocatorRestaurantPage.restaurantId.toString();

                    return this._asyncLocalStorageService.createStoreAndRun({ restaurant: { id: restaurantId } }, () =>
                        this._fetchStoreLocatorStoreService.execute({
                            restaurantId,
                            storeLocatorRestaurantPage,
                            storeLocatorOrganizationConfig,
                            socialNetworksData,
                        })
                    );
                })
            );

            // If some stores are not valid, throw an error and stop store locator generation
            const failedStores = stores.filter(({ data }) => lodash.isNil(data));
            if (failedStores.length > 0) {
                throw new MalouError(MalouErrorCode.STORE_LOCATOR_DATA_FETCH_FAILED, {
                    metadata: { failedStores },
                });
            }

            // If backups were used, notify us but don't stop store locator generation
            const backupUsedStores = stores.filter(({ success }) => !success);
            if (backupUsedStores.length > 0) {
                this._slackService.sendAlert({
                    data: {
                        err: new MalouError(MalouErrorCode.STORE_LOCATOR_DATA_BACKUP_USED, {
                            metadata: { backupUsedStores },
                        }),
                    },
                    channel: SlackChannel.STORE_LOCATOR_ALERTS,
                });
            }

            return stores.map(({ data }) => data).filter(isNotNil);
        } catch (err) {
            logger.error('[STORE_LOCATOR] Failed to get stores', { err });

            this._slackService.sendAlert({ data: { err }, channel: SlackChannel.STORE_LOCATOR_ALERTS, shouldPing: true });

            throw err;
        }
    }
}
