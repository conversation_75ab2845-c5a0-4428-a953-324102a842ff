import { singleton } from 'tsyringe';

import { StoreLocatorOrganizationConfigurationResponseDto, UpdateOrganizationConfigurationAiSettingsBodyDto } from '@malou-io/package-dto';

import StoreLocatorOrganizationConfigRepository from ':modules/store-locator/store-locator-organization-config.repository';

export type OrganizationConfigurationAiSettingsUpdate = UpdateOrganizationConfigurationAiSettingsBodyDto['aiSettings'];

@singleton()
export default class UpdateOrganizationConfigAISettingsUseCase {
    constructor(private readonly _storeLocatorOrganizationConfigRepository: StoreLocatorOrganizationConfigRepository) {}

    async execute({
        organizationId,
        aiSettings,
    }: {
        organizationId: string;
        aiSettings: OrganizationConfigurationAiSettingsUpdate;
    }): Promise<StoreLocatorOrganizationConfigurationResponseDto> {
        const updatedConfig = await this._storeLocatorOrganizationConfigRepository.updateAiSettings(organizationId, aiSettings);

        return updatedConfig.toDto();
    }
}
