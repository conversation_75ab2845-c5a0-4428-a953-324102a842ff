import assert from 'assert';
import { singleton } from 'tsyringe';

import { UpdateStoreLocatorStorePagesBodyDto } from '@malou-io/package-dto';
import {
    EntityRepository,
    IStoreLocatorRestaurantPage,
    ReadPreferenceMode,
    StoreLocatorRestaurantPageModel,
    toDbId,
} from '@malou-io/package-models';
import { StoreLocatorLanguage, StoreLocatorPageStatus } from '@malou-io/package-utils';

@singleton()
export default class StoreLocatorRestaurantPageRepository extends EntityRepository<IStoreLocatorRestaurantPage> {
    constructor() {
        super(StoreLocatorRestaurantPageModel);
    }

    async getStoreLocatorStorePages(organizationId: string, options: { isForEdit?: boolean } = {}): Promise<IStoreLocatorRestaurantPage[]> {
        return this.find({
            filter: {
                organizationId: toDbId(organizationId),
                status: options.isForEdit ? StoreLocatorPageStatus.DRAFT : StoreLocatorPageStatus.PUBLISHED,
            },
            options: { lean: true, readPreference: ReadPreferenceMode.SECONDARY },
        });
    }

    async updateStoreLocatorStorePages(
        organizationId: string,
        updatedPages: UpdateStoreLocatorStorePagesBodyDto,
        lang: StoreLocatorLanguage
    ): Promise<void> {
        const draftPages = await this.find({
            filter: {
                restaurantId: { $in: updatedPages.map((p) => toDbId(p.restaurantId)) },
                organizationId: toDbId(organizationId),
                lang,
                status: StoreLocatorPageStatus.DRAFT,
            },
            options: {
                lean: true,
            },
        });

        assert(draftPages.length !== 0, 'Draft pages must exist for the given organization and language');

        const operations: {
            updateOne: {
                filter: Record<string, any>;
                update: { $set: Partial<IStoreLocatorRestaurantPage> };
            };
        }[] = [];

        for (const updatedPage of updatedPages) {
            const draft = draftPages.find(({ restaurantId }) => restaurantId.toString() === updatedPage.restaurantId);

            assert(draft, 'Draft page for restaurant must exist');

            const updateDoc: Partial<IStoreLocatorRestaurantPage> | null = this._toUpdateDocument(draft, updatedPage);

            assert(updateDoc, 'Update document must not be null');

            operations.push({
                updateOne: {
                    filter: {
                        restaurantId: toDbId(updatedPage.restaurantId),
                        organizationId: toDbId(organizationId),
                        lang,
                        status: StoreLocatorPageStatus.DRAFT,
                    },
                    update: {
                        $set: updateDoc,
                    },
                },
            });
        }

        await this.bulkOperations({
            operations,
        });
    }

    private _toUpdateDocument(
        document: IStoreLocatorRestaurantPage,
        updatedData: UpdateStoreLocatorStorePagesBodyDto[number]
    ): IStoreLocatorRestaurantPage {
        return {
            ...document,
            lang: updatedData.lang,
            status: StoreLocatorPageStatus.DRAFT,
            blocks: {
                ...document.blocks,
                ...(updatedData.information && {
                    information: {
                        ...document.blocks.information,
                        title: updatedData.information.title,
                        image: {
                            description: updatedData.information.image.description,
                            url: updatedData.information.image.url,
                            mediaId: updatedData.information.image.mediaId ? toDbId(updatedData.information.image.mediaId) : undefined,
                        },
                        cta: updatedData.information.cta,
                    },
                }),
                ...(updatedData.gallery && {
                    gallery: {
                        ...document.blocks.gallery,
                        ...updatedData.gallery,
                    },
                }),
                ...(updatedData.reviews && {
                    reviews: {
                        ...document.blocks.reviews,
                        title: updatedData.reviews.title,
                        cta: updatedData.reviews.cta,
                    },
                }),
                ...(updatedData.callToActions && {
                    callToActions: {
                        ...document.blocks.callToActions,
                        title: updatedData.callToActions.title,
                        ctas: updatedData.callToActions.links.map((link) => ({
                            text: link.text,
                            url: link.url,
                        })),
                    },
                }),
                ...(updatedData.descriptions && {
                    descriptions: {
                        ...document.blocks.descriptions,
                        items: updatedData.descriptions.items.map((item) => ({
                            title: item.title,
                            image: {
                                description: item.image.description,
                                url: item.image.url,
                                mediaId: item.image.mediaId ? toDbId(item.image.mediaId) : undefined,
                            },
                            blocks: item.blocks.map((block) => ({
                                title: block.title,
                                text: block.text,
                            })),
                        })),
                    },
                }),
                ...(updatedData.socialNetworks && {
                    socialNetworks: {
                        ...document.blocks.socialNetworks,
                        title: updatedData.socialNetworks.title,
                    },
                }),
            },
        };
    }
}
