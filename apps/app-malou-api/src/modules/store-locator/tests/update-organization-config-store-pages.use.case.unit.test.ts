import { container } from 'tsyringe';

import { newDbId } from '@malou-io/package-models';
import { StoreLocatorMapPageElementIds, StoreLocatorRestaurantPageElementIds } from '@malou-io/package-utils';

import { registerRepositories, TestCaseBuilderV2 } from ':helpers/tests/testing-utils';
import { getDefaultStoreLocatorOrganizationConfig } from ':modules/store-locator/builders/store-locator-organization-config.builder';
import StoreLocatorOrganizationConfigRepository from ':modules/store-locator/store-locator-organization-config.repository';
import UpdateOrganizationConfigStorePagesUseCase from ':modules/store-locator/use-cases/update-organization-config-store-pages/update-organization-config-store-pages.use-case';

describe('UpdateOrganizationConfigStorePagesUseCase', () => {
    let useCase: UpdateOrganizationConfigStorePagesUseCase;
    let repository: StoreLocatorOrganizationConfigRepository;

    beforeAll(async () => {
        registerRepositories(['StoreLocatorOrganizationConfigRepository']);
        useCase = container.resolve(UpdateOrganizationConfigStorePagesUseCase);
        repository = container.resolve(StoreLocatorOrganizationConfigRepository);
    });

    it('should update organization config store pages with correct filter and update', async () => {
        const organizationId = newDbId();
        const testCase = new TestCaseBuilderV2<'storeLocatorOrganizationConfigs'>({
            seeds: {
                storeLocatorOrganizationConfigs: {
                    data() {
                        return [
                            getDefaultStoreLocatorOrganizationConfig()
                                .organizationId(organizationId)
                                .styles({
                                    fonts: [{ class: 'primary', src: 'https://example.io' }],
                                    colors: [{ class: 'primary', value: '#FFFFFF' }],
                                    pages: {
                                        store: Object.values(StoreLocatorRestaurantPageElementIds).reduce(
                                            (acc, id) => ({
                                                ...acc,
                                                [id]: ['bg-primary', 'text-primary'],
                                            }),
                                            {}
                                        ),
                                        map: Object.values(StoreLocatorMapPageElementIds).reduce(
                                            (acc, id) => ({
                                                ...acc,
                                                [id]: [],
                                            }),
                                            {}
                                        ),
                                        storeDraft: Object.values(StoreLocatorRestaurantPageElementIds).reduce(
                                            (acc, id) => ({
                                                ...acc,
                                                [id]: ['bg-primary', 'text-primary'],
                                            }),
                                            {}
                                        ),
                                        mapDraft: Object.values(StoreLocatorMapPageElementIds).reduce(
                                            (acc, id) => ({
                                                ...acc,
                                                [id]: [],
                                            }),
                                            {}
                                        ),
                                    },
                                })
                                .build(),
                        ];
                    },
                },
            },
            expectedResult(dependencies) {
                return {
                    ...dependencies.storeLocatorOrganizationConfigs[0],
                    styles: {
                        ...dependencies.storeLocatorOrganizationConfigs[0].styles,
                        pages: {
                            ...dependencies.storeLocatorOrganizationConfigs[0].styles.pages,
                            storeDraft: {
                                ...dependencies.storeLocatorOrganizationConfigs[0].styles.pages.storeDraft,
                                [StoreLocatorRestaurantPageElementIds.INFORMATION_BANNER]: ['text-secondary'],
                                [StoreLocatorRestaurantPageElementIds.INFORMATION_BANNER_CTA_1]: ['bg-primary'],
                                [StoreLocatorRestaurantPageElementIds.INFORMATION_BANNER_CTA_2]: ['bg-secondary'],
                            },
                            mapDraft: {
                                ...dependencies.storeLocatorOrganizationConfigs[0].styles.pages.mapDraft,
                            },
                        },
                    },
                };
            },
        });

        await testCase.build();
        const expectedResult = testCase.getExpectedResult();

        const updateObject = {
            [StoreLocatorRestaurantPageElementIds.INFORMATION_BANNER]: ['text-secondary'],
            [StoreLocatorRestaurantPageElementIds.INFORMATION_BANNER_CTA_1]: ['bg-primary'],
            [StoreLocatorRestaurantPageElementIds.INFORMATION_BANNER_CTA_2]: ['bg-secondary'],
        };

        await useCase.execute({ organizationId: organizationId.toString(), storePages: updateObject });

        const updatedConfig = await repository.findOne({
            filter: { organizationId: organizationId.toString() },
            options: { lean: true },
        });

        expect({ ...updatedConfig, updatedAt: expect.any(Date) }).toEqual({ ...expectedResult, updatedAt: expect.any(Date) });
        expect(updatedConfig?.styles.pages.store).toEqual(
            Object.values(StoreLocatorRestaurantPageElementIds).reduce(
                (acc, id) => ({
                    ...acc,
                    [id]: ['bg-primary', 'text-primary'],
                }),
                {}
            )
        );
    });
});
