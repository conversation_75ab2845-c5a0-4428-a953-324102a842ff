import { StoreLocatorOrganizationConfigurationResponseDto } from '@malou-io/package-dto';
import {
    EntityConstructor,
    PlatformKey,
    StoreLocatorAiSettingsLanguageStyle,
    StoreLocatorMapPageElementIds,
    StoreLocatorRestaurantPageElementIds,
} from '@malou-io/package-utils';

export type StoreLocatorOrganizationConfigurationProps = EntityConstructor<StoreLocatorOrganizationConfiguration> & {
    id: string;
};

export class StoreLocatorOrganizationConfiguration {
    id: string;
    organizationId: string;
    cloudfrontDistributionId: string;
    baseUrl: string;
    isLive: boolean;
    shouldDisplayWhiteMark: boolean;
    styles: {
        fonts: Array<{
            class: string;
            src: string;
            weight?: string;
            style?: string;
        }>;
        colors: Array<{
            class: string;
            value: string;
        }>;
        pages: {
            store: Record<StoreLocatorRestaurantPageElementIds, string[]>;
            map: Record<StoreLocatorMapPageElementIds, string[]>;
            storeDraft: Record<StoreLocatorRestaurantPageElementIds, string[]>;
            mapDraft: Record<StoreLocatorMapPageElementIds, string[]>;
        };
    };
    plugins?: {
        googleAnalytics?: {
            trackingId: string;
        };
    };
    organization: {
        id: string;
        name: string;
    };
    aiSettings: {
        tone: string[];
        languageStyle: StoreLocatorAiSettingsLanguageStyle;
        attributeIds: string[];
        restaurantKeywordIds: string[];
        specialAttributes: Array<{
            restaurantId: string;
            text: string;
        }>;
        attributes: Array<{
            id: string;
            attributeId: string;
            platformKey: PlatformKey;
            attributeName: {
                fr: string;
                en?: string;
                es?: string;
                it?: string;
            };
        }>;
        keywords: Array<{
            restaurantKeywordId: string;
            text: string;
            restaurantId: string;
            keywordId: string;
        }>;
    };

    constructor(props: StoreLocatorOrganizationConfigurationProps) {
        this.id = props.id;
        this.organizationId = props.organizationId;
        this.cloudfrontDistributionId = props.cloudfrontDistributionId;
        this.baseUrl = props.baseUrl;
        this.isLive = props.isLive;
        this.styles = props.styles;
        this.plugins = props.plugins;
        this.aiSettings = props.aiSettings;
        this.organization = props.organization;
        this.shouldDisplayWhiteMark = props.shouldDisplayWhiteMark ?? false;
    }

    toDto(): StoreLocatorOrganizationConfigurationResponseDto {
        return {
            id: this.id,
            organizationId: this.organizationId,
            cloudfrontDistributionId: this.cloudfrontDistributionId,
            baseUrl: this.baseUrl,
            isLive: this.isLive,
            styles: this.styles,
            plugins: this.plugins,
            aiSettings: this.aiSettings,
        };
    }
}
