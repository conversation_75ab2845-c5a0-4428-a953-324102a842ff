import { render } from '@react-email/render';
import assert from 'node:assert';
import { singleton } from 'tsyringe';

import { ReviewIntelligentSubjectTemplate } from '@malou-io/package-emails';
import { IReview, IReviewWithSemanticAnalysisAndTranslations, ReadPreferenceMode, toDbId } from '@malou-io/package-models';
import {
    ApplicationLanguage,
    getPlatformDefinition,
    HeapEventName,
    IntelligentSubjectAutomationAction,
    IntelligentSubjectAutomationRelatedEntity,
    Locale,
    MalouErrorCode,
} from '@malou-io/package-utils';

import { Config } from ':config';
import { MalouError } from ':helpers/classes/malou-error';
import { logger } from ':helpers/logger';
import { ApiKeysRepository } from ':modules/api-keys/api-keys.repository';
import { IntelligentSubjectAutomation } from ':modules/automations/features/intelligent-subjects/entities/intelligent-subject-automation.entity';
import { EmailSenderService } from ':modules/mailing/email-sender.service';
import { TranslateReviewTextService } from ':modules/reports/services/translate-review-text.service';
import RestaurantsRepository from ':modules/restaurants/restaurants.repository';
import { ReviewsRepository } from ':modules/reviews/reviews.repository';
import { UsersRepository } from ':modules/users/users.repository';
import { HeapAnalyticsService } from ':plugins/heap-analytics';
import { Translation } from ':services/translation.service';

interface RecipientInfo {
    id: string | undefined;
    email: string;
    recipientName: string;
    lang: Locale;
}

/**
 * @internal
 * This service is intended for use only within the intelligent-subjects-automation-action-handler directory
 */
@singleton()
export class ReviewsIntelligentSubjectActionHandlerService {
    constructor(
        private readonly _emailSenderService: EmailSenderService,
        private readonly _heapAnalyticsService: HeapAnalyticsService,
        private readonly _apiKeysRepository: ApiKeysRepository,
        private readonly _restaurantsRepository: RestaurantsRepository,
        private readonly _reviewsRepository: ReviewsRepository,
        private readonly _usersRepository: UsersRepository,
        private readonly _translator: Translation,
        private readonly _translateReviewTextService: TranslateReviewTextService
    ) {}

    async handleReviewAction({ automations, reviewId }: { automations: IntelligentSubjectAutomation[]; reviewId: string }): Promise<void> {
        const review = (await this._reviewsRepository.findOneOrFail({
            filter: { _id: toDbId(reviewId) },
            options: { lean: true, readPreference: ReadPreferenceMode.SECONDARY, populate: [{ path: 'translations' }] },
        })) as IReviewWithSemanticAnalysisAndTranslations;

        const { action } = automations[0];

        switch (action) {
            case IntelligentSubjectAutomationAction.SEND_EMAIL:
                /*
                    In this case, we can have multiple automations with different `subject`
                    if the `action` is to send an email, we need to send only one email to all the recipients of the automations
                */
                await this._sendEmailsToRecipients({ review, automations });
                break;
            default:
                throw new MalouError(MalouErrorCode.NOT_IMPLEMENTED, {
                    message: `[ReviewsIntelligentSubjectActionHandlerService] Action ${action} is not implemented`,
                    metadata: {
                        action,
                    },
                });
        }
    }

    private async _sendEmailsToRecipients({
        automations,
        review,
    }: {
        automations: IntelligentSubjectAutomation[];
        review: IReviewWithSemanticAnalysisAndTranslations;
    }): Promise<void> {
        const recipients = automations.map((automation) => automation.recipientEmails).flat();

        // the user who created the automation (first in the automations by default)
        const automationUser = automations.map((automation) => automation.user)[0];

        const uniqueRecipients = [...new Set(recipients)];

        if (!uniqueRecipients.length) {
            logger.info('[ReviewsIntelligentSubjectActionHandlerService] No recipients found for the review', { reviewId: review._id });
            return;
        }

        const reviewTranslations = review.translations ?? {};

        for (const recipientEmail of uniqueRecipients) {
            try {
                const recipientInfo = await this._getRecipientInfo({
                    recipientEmail,
                    automationUserLanguage: automationUser.defaultLanguage,
                });

                const shouldTranslateReview = review.lang !== recipientInfo.lang;
                const textTranslatedToRecipientLang = reviewTranslations[recipientInfo.lang];

                let translatedReviewText = review.text;

                if (shouldTranslateReview && textTranslatedToRecipientLang) {
                    translatedReviewText = textTranslatedToRecipientLang;
                } else if (shouldTranslateReview) {
                    // translate the review text to the receipient lang
                    const userId = recipientInfo.id ?? automationUser.id.toString();
                    const translatedReview = await this._translateReviewTextService.execute(review, {
                        _id: userId,
                        defaultLanguage: recipientInfo.lang,
                    });

                    const translatedText = translatedReview.text!;
                    reviewTranslations[recipientInfo.lang] = translatedText;
                    translatedReviewText = translatedText;
                }

                await this._sendEmail({
                    review: { ...review, text: translatedReviewText },
                    recipientInfo,
                    translatedFrom: shouldTranslateReview ? (review.lang ?? undefined) : undefined,
                });
            } catch (error) {
                logger.error('[ReviewsIntelligentSubjectActionHandlerService] Error sending email', {
                    recipientEmail,
                    reviewId: review._id.toString(),
                    error,
                });
            }
        }
    }

    private async _sendEmail({
        review,
        recipientInfo,
        translatedFrom,
    }: {
        review: IReviewWithSemanticAnalysisAndTranslations;
        recipientInfo: RecipientInfo;
        translatedFrom?: string;
    }): Promise<void> {
        const apiKey = await this._getApiKey();
        const restaurantName = await this._getRestaurantName(review.restaurantId.toString());
        const emailSubject = this._getEmailSubject(recipientInfo.lang);
        const reviewIntelligentSubjectTemplate = ReviewIntelligentSubjectTemplate({
            locale: recipientInfo.lang,
            receiver: recipientInfo.recipientName,
            trackingUrl: this._buildTrackingUrl({ review, apiKey, recipientEmail: recipientInfo.email }),
            reviewerProfilePictureUrl: review.reviewer?.profilePhotoUrl ?? '',
            socialAttachmentUrls: review.socialAttachments?.map((attachment) => attachment.urls.original),
            reviewerName: review.reviewer?.displayName ?? '',
            rating: review.rating ?? 0,
            text: review.text ?? '',
            socialCreatedAt: review.socialCreatedAt,
            platformName: getPlatformDefinition(review.key)?.fullName ?? '',
            platformKey: review.key,
            intelligentSubjects: review.intelligentSubjects ?? [],
            link: `${Config.baseAppUrl}/restaurants/${review.restaurantId.toString()}/reputation/reviews?reviewId=${review._id.toString()}`,
            restaurantName,
            translatedFrom,
        });

        const html = render(reviewIntelligentSubjectTemplate);

        await this._emailSenderService.sendEmail({
            to: recipientInfo.email,
            subject: emailSubject,
            html,
            lang: recipientInfo.lang,
            fromEmail: Config.settings.adminUpdatesNotificationEmail,
        });

        await this._heapAnalyticsService.track({
            eventName: HeapEventName.REVIEW_INTELLIGENT_SUBJECT_EMAIL_SENT,
            identity: recipientInfo.email,
            properties: {
                recipientEmail: recipientInfo.email,
                restaurantId: review.restaurantId.toString(),
                reviewId: review._id.toString(),
            },
        });
    }

    private _getEmailSubject(lang: Locale): string {
        return this._translator.fromLang({ lang }).automations.intelligent_subjects.reviews.subject();
    }

    private _buildTrackingUrl({ review, apiKey, recipientEmail }: { review: IReview; apiKey: string; recipientEmail: string }): string {
        const subjects = review.intelligentSubjects?.map((intelligentSubject) => intelligentSubject.subject).join(',');
        const urlWithoutQuery = `${Config.baseApiUrl}/automations/intelligent-subjects/emails/opened`;
        const today = new Date().getTime();
        return `${urlWithoutQuery}?entityId=${review._id.toString()}&recipientEmail=${recipientEmail}&t=${today}&api_key=${apiKey}
            &relatedEntity=${IntelligentSubjectAutomationRelatedEntity.REVIEWS}&action=${IntelligentSubjectAutomationAction.SEND_EMAIL}
            &restaurantId=${review.restaurantId.toString()}&subjects=${subjects}`;
    }

    private async _getApiKey(): Promise<string> {
        const apiKey = (await this._apiKeysRepository.getApiKeyByName('email'))?.apiKey;
        assert(apiKey, 'Email API key not found');
        return apiKey;
    }

    private async _getRecipientInfo({
        recipientEmail,
        automationUserLanguage,
    }: {
        recipientEmail: string;
        automationUserLanguage: ApplicationLanguage | undefined;
    }): Promise<RecipientInfo> {
        const user = await this._usersRepository.findOne({
            filter: { email: recipientEmail },
            projection: { defaultLanguage: 1, name: 1 },
            options: { lean: true, readPreference: ReadPreferenceMode.SECONDARY_PREFERRED },
        });

        const recipientName = user?.name ?? this._getRecipientDisplayName(recipientEmail);
        const lang = (user?.defaultLanguage as unknown as Locale) ?? automationUserLanguage ?? Locale.EN;
        return { id: user?._id.toString(), email: recipientEmail, recipientName, lang };
    }

    private _getRecipientDisplayName(recipientEmail: string): string {
        const emailParts = recipientEmail.split('@');
        const recipientName = emailParts[0];
        return recipientName;
    }

    private async _getRestaurantName(restaurantId: string): Promise<string> {
        const restaurant = await this._restaurantsRepository.findOneOrFail({
            filter: { _id: toDbId(restaurantId) },
            projection: { name: 1 },
            options: { lean: true, readPreference: ReadPreferenceMode.SECONDARY_PREFERRED },
        });
        return restaurant.name;
    }
}
