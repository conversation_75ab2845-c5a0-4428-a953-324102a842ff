import { render } from '@react-email/render';
import { container } from 'tsyringe';

import {
    ApplicationLanguage,
    IntelligentSubjectAutomationAction,
    IntelligentSubjectName,
    Locale,
    ReviewAnalysisSentiment,
} from '@malou-io/package-utils';

import { registerRepositories, TestCaseBuilderV2 } from ':helpers/tests/testing-utils';
import { getDefaultApiKey } from ':modules/api-keys/tests/api-key.builder';
import IntelligentSubjectAutomationsRepository from ':modules/automations/features/intelligent-subjects/repositories/intelligent-subjects.repository';
import { ReviewsIntelligentSubjectActionHandlerService } from ':modules/automations/features/intelligent-subjects/services/intelligent-subjects-automation-action-handler/reviews/reviews-intelligent-subject-action-handler.service';
import { getDefaultIntelligentSubjectAutomation } from ':modules/automations/tests/intelligent-subject-automation.builder';
import { getDefaultTranslations } from ':modules/keywords/tests/translations.builder';
import { EmailSenderService } from ':modules/mailing/email-sender.service';
import { TranslateReviewTextService } from ':modules/reports/services/translate-review-text.service';
import { getDefaultRestaurant } from ':modules/restaurants/tests/restaurant.builder';
import { getDefaultReview } from ':modules/reviews/tests/reviews.builder';
import { getDefaultUser } from ':modules/users/tests/user.builder';
import { HeapAnalyticsService } from ':plugins/heap-analytics';
import { Translation } from ':services/translation.service';

// Mock @react-email/render
jest.mock('@react-email/render');
const mockRender = render as jest.MockedFunction<typeof render>;

let service: ReviewsIntelligentSubjectActionHandlerService;
let emailSenderService: EmailSenderService;
let heapAnalyticsService: HeapAnalyticsService;
let translator: Translation;
let translateReviewTextService: TranslateReviewTextService;
let intelligentSubjectAutomationsRepository: IntelligentSubjectAutomationsRepository;

describe('ReviewsIntelligentSubjectActionHandlerService', () => {
    beforeEach(() => {
        jest.clearAllMocks();
        container.clearInstances();

        registerRepositories([
            'IntelligentSubjectAutomationsRepository',
            'ApiKeysRepository',
            'RestaurantsRepository',
            'ReviewsRepository',
            'UsersRepository',
            'TranslationsRepository',
        ]);

        // Mock services
        emailSenderService = {
            sendEmail: jest.fn().mockResolvedValue(undefined),
        } as any;

        heapAnalyticsService = {
            track: jest.fn().mockResolvedValue(undefined),
        } as any;

        translator = {
            fromLang: jest.fn().mockReturnValue({
                automations: {
                    intelligent_subjects: {
                        reviews: {
                            subject: jest.fn().mockReturnValue('Test Email Subject'),
                        },
                    },
                },
            }),
        } as any;

        translateReviewTextService = {
            execute: jest.fn().mockImplementation(() => {
                return { text: 'Texte de revue original' };
            }),
        } as any;

        container.register(EmailSenderService, { useValue: emailSenderService });
        container.register(HeapAnalyticsService, { useValue: heapAnalyticsService });
        container.register(Translation, { useValue: translator });
        container.register(TranslateReviewTextService, { useValue: translateReviewTextService });

        // Get repository instances
        intelligentSubjectAutomationsRepository = container.resolve(IntelligentSubjectAutomationsRepository);

        service = container.resolve(ReviewsIntelligentSubjectActionHandlerService);

        // Mock render function
        mockRender.mockReturnValue('<html>Mocked email template</html>');
    });

    describe('handleReviewAction', () => {
        it('should handle SEND_EMAIL action successfully and translate review text', async () => {
            const testCase = new TestCaseBuilderV2<'users' | 'restaurants' | 'reviews' | 'intelligentSubjectAutomations' | 'apiKeys'>({
                seeds: {
                    users: {
                        data() {
                            return [
                                getDefaultUser()
                                    .defaultLanguage(ApplicationLanguage.FR)
                                    .name('Test User')
                                    .email('<EMAIL>')
                                    .build(),
                            ];
                        },
                    },
                    restaurants: {
                        data() {
                            return [getDefaultRestaurant().name('Test Restaurant').build()];
                        },
                    },
                    reviews: {
                        data(dependencies) {
                            return [
                                getDefaultReview()
                                    .restaurantId(dependencies.restaurants()[0]._id)
                                    .text('Original review text')
                                    .lang(ApplicationLanguage.EN)
                                    .intelligentSubjects([
                                        {
                                            subject: IntelligentSubjectName.HYGIENE,
                                            isDetected: true,
                                            sentiment: ReviewAnalysisSentiment.NEGATIVE,
                                        },
                                    ])
                                    .build(),
                            ];
                        },
                    },
                    intelligentSubjectAutomations: {
                        data(dependencies) {
                            return [
                                getDefaultIntelligentSubjectAutomation()
                                    .restaurantId(dependencies.restaurants()[0]._id)
                                    .userId(dependencies.users()[0]._id)
                                    .action(IntelligentSubjectAutomationAction.SEND_EMAIL)
                                    .subject(IntelligentSubjectName.HYGIENE)
                                    .recipientEmails(['<EMAIL>'])
                                    .build(),
                            ];
                        },
                    },
                    apiKeys: {
                        data() {
                            return [getDefaultApiKey().name('email').apiKey('test-api-key').build()];
                        },
                    },
                },
            });

            await testCase.build();
            const seededObjects = testCase.getSeededObjects();
            const review = seededObjects.reviews[0];
            const automations = seededObjects.intelligentSubjectAutomations.map((automation) =>
                intelligentSubjectAutomationsRepository.toEntity({
                    ...automation,
                    user: {
                        _id: seededObjects.users[0]._id,
                        defaultLanguage: seededObjects.users[0].defaultLanguage,
                    },
                })
            );

            // Spy on private _sendEmail method
            const sendEmailSpy = jest.spyOn(service as any, '_sendEmail').mockResolvedValue(undefined);

            await service.handleReviewAction({
                automations,
                reviewId: review._id.toString(),
            });

            expect(translateReviewTextService.execute).toHaveBeenCalledWith(
                expect.objectContaining({
                    _id: review._id,
                    text: 'Original review text',
                }),
                {
                    _id: seededObjects.users[0]._id.toString(),
                    defaultLanguage: seededObjects.users[0].defaultLanguage,
                }
            );

            expect(sendEmailSpy).toHaveBeenCalledWith({
                review: expect.objectContaining({
                    _id: review._id,
                    text: 'Texte de revue original',
                }),
                recipientInfo: {
                    id: seededObjects.users[0]._id.toString(),
                    email: '<EMAIL>',
                    recipientName: 'Test User',
                    lang: Locale.FR,
                },
                translatedFrom: ApplicationLanguage.EN, // Should be the original language of the review
            });
        });

        it('should handle SEND_EMAIL action successfully and use exisiting translation', async () => {
            const testCase = new TestCaseBuilderV2<
                'users' | 'restaurants' | 'reviews' | 'intelligentSubjectAutomations' | 'apiKeys' | 'translations'
            >({
                seeds: {
                    users: {
                        data() {
                            return [
                                getDefaultUser()
                                    .defaultLanguage(ApplicationLanguage.FR)
                                    .name('Test User')
                                    .email('<EMAIL>')
                                    .build(),
                            ];
                        },
                    },
                    restaurants: {
                        data() {
                            return [getDefaultRestaurant().name('Test Restaurant').build()];
                        },
                    },
                    translations: {
                        data() {
                            return [
                                getDefaultTranslations().fr('Texte de revue original').en(undefined).es(undefined).it(undefined).build(),
                            ];
                        },
                    },
                    reviews: {
                        data(dependencies) {
                            return [
                                getDefaultReview()
                                    .restaurantId(dependencies.restaurants()[0]._id)
                                    .text('Original review text')
                                    .lang(ApplicationLanguage.EN)
                                    .intelligentSubjects([
                                        {
                                            subject: IntelligentSubjectName.HYGIENE,
                                            isDetected: true,
                                            sentiment: ReviewAnalysisSentiment.NEGATIVE,
                                        },
                                    ])
                                    .translationsId(dependencies.translations()[0]._id)
                                    .build(),
                            ];
                        },
                    },
                    intelligentSubjectAutomations: {
                        data(dependencies) {
                            return [
                                getDefaultIntelligentSubjectAutomation()
                                    .restaurantId(dependencies.restaurants()[0]._id)
                                    .userId(dependencies.users()[0]._id)
                                    .action(IntelligentSubjectAutomationAction.SEND_EMAIL)
                                    .subject(IntelligentSubjectName.HYGIENE)
                                    .recipientEmails(['<EMAIL>'])
                                    .build(),
                            ];
                        },
                    },
                    apiKeys: {
                        data() {
                            return [getDefaultApiKey().name('email').apiKey('test-api-key').build()];
                        },
                    },
                },
            });

            await testCase.build();
            const seededObjects = testCase.getSeededObjects();
            const review = seededObjects.reviews[0];
            const automations = seededObjects.intelligentSubjectAutomations.map((automation) =>
                intelligentSubjectAutomationsRepository.toEntity({
                    ...automation,
                    user: {
                        _id: seededObjects.users[0]._id,
                        defaultLanguage: seededObjects.users[0].defaultLanguage,
                    },
                })
            );

            // Spy on private _sendEmail method
            const sendEmailSpy = jest.spyOn(service as any, '_sendEmail').mockResolvedValue(undefined);

            await service.handleReviewAction({
                automations,
                reviewId: review._id.toString(),
            });

            expect(translateReviewTextService.execute).not.toHaveBeenCalled();

            expect(sendEmailSpy).toHaveBeenCalledWith({
                review: expect.objectContaining({
                    _id: review._id,
                    text: 'Texte de revue original',
                }),
                recipientInfo: {
                    id: seededObjects.users[0]._id.toString(),
                    email: '<EMAIL>',
                    recipientName: 'Test User',
                    lang: Locale.FR,
                },
                translatedFrom: ApplicationLanguage.EN, // Should be the original language of the review
            });
        });

        it('should handle SEND_EMAIL action successfully for non existing user and translate to user lang who created the auto', async () => {
            const testCase = new TestCaseBuilderV2<'users' | 'restaurants' | 'reviews' | 'intelligentSubjectAutomations' | 'apiKeys'>({
                seeds: {
                    users: {
                        data() {
                            return [
                                getDefaultUser()
                                    .defaultLanguage(ApplicationLanguage.FR)
                                    .name('Test User')
                                    .email('<EMAIL>')
                                    .build(),
                            ];
                        },
                    },
                    restaurants: {
                        data() {
                            return [getDefaultRestaurant().name('Test Restaurant').build()];
                        },
                    },
                    reviews: {
                        data(dependencies) {
                            return [
                                getDefaultReview()
                                    .restaurantId(dependencies.restaurants()[0]._id)
                                    .text('Original review text')
                                    .lang(ApplicationLanguage.EN)
                                    .intelligentSubjects([
                                        {
                                            subject: IntelligentSubjectName.HYGIENE,
                                            isDetected: true,
                                            sentiment: ReviewAnalysisSentiment.NEGATIVE,
                                        },
                                    ])
                                    .build(),
                            ];
                        },
                    },
                    intelligentSubjectAutomations: {
                        data(dependencies) {
                            return [
                                getDefaultIntelligentSubjectAutomation()
                                    .restaurantId(dependencies.restaurants()[0]._id)
                                    .userId(dependencies.users()[0]._id)
                                    .action(IntelligentSubjectAutomationAction.SEND_EMAIL)
                                    .subject(IntelligentSubjectName.HYGIENE)
                                    .recipientEmails(['<EMAIL>'])
                                    .build(),
                            ];
                        },
                    },
                    apiKeys: {
                        data() {
                            return [getDefaultApiKey().name('email').apiKey('test-api-key').build()];
                        },
                    },
                },
            });

            await testCase.build();
            const seededObjects = testCase.getSeededObjects();
            const review = seededObjects.reviews[0];
            const automations = seededObjects.intelligentSubjectAutomations.map((automation) =>
                intelligentSubjectAutomationsRepository.toEntity({
                    ...automation,
                    user: {
                        _id: seededObjects.users[0]._id,
                        defaultLanguage: seededObjects.users[0].defaultLanguage,
                    },
                })
            );

            // Spy on private _sendEmail method
            const sendEmailSpy = jest.spyOn(service as any, '_sendEmail').mockResolvedValue(undefined);

            await service.handleReviewAction({
                automations,
                reviewId: review._id.toString(),
            });

            expect(translateReviewTextService.execute).toHaveBeenCalledWith(
                expect.objectContaining({
                    _id: review._id,
                    text: 'Original review text',
                }),
                {
                    _id: seededObjects.users[0]._id.toString(),
                    defaultLanguage: seededObjects.users[0].defaultLanguage,
                }
            );

            expect(sendEmailSpy).toHaveBeenCalledWith({
                review: expect.objectContaining({
                    _id: review._id,
                    text: 'Texte de revue original',
                }),
                recipientInfo: {
                    id: undefined, // No user ID since the recipient is external
                    email: '<EMAIL>',
                    recipientName: 'externalUser',
                    lang: Locale.FR,
                },
                translatedFrom: ApplicationLanguage.EN, // Should be the original language of the review
            });
        });
    });
});
