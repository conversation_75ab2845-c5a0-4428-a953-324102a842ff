import lodash from 'lodash';
import { DateTime } from 'luxon';
import { singleton } from 'tsyringe';

import { GetMergedInformationUpdateBodyDto } from '@malou-io/package-dto';
import {
    IAttribute,
    ICategory,
    IInformationUpdate,
    IInformationUpdateData,
    IInformationUpdatePlatformState,
    MergedInformationUpdate,
    MergedInformationUpdateByRestaurantId,
    toDbId,
} from '@malou-io/package-models';
import {
    createDateFromMalouDate,
    getPlatformDefinition,
    getRegularHoursToUpdate,
    getSpecialHoursToUpdate,
    InformationUpdateAttributeValue,
    InformationUpdatePlatformStateStatus,
    InformationUpdateProvider,
    InformationUpdateSupportedPlatformKey,
    isNotNil,
    PlatformAccessType,
    PlatformKey,
} from '@malou-io/package-utils';

import { AttributesRepository } from ':modules/attributes/attributes.repository';
import { MapMalouAttributesToPlatformAttributesService } from ':modules/attributes/services/map-malou-attributes-to-platform-attributes/map-malou-attributes-to-platform-attributes.service';
import CategoriesRepository from ':modules/categories/categories.repository';
import { MapCategoriesToPlatformCategoriesService } from ':modules/categories/services/map-gmb-categories-to-manual-platform-categories/map-categories-to-manual-platform-categories.service';
import { InformationUpdatesRepository } from ':modules/information-updates/information-updates.repository';
import { MergedInformationUpdatesRepository } from ':modules/information-updates/merged-information-updates.repository';
import RestaurantsRepository from ':modules/restaurants/restaurants.repository';

@singleton()
export class MergeInformationUpdateService {
    constructor(
        private readonly _mapMalouAttributesToPlatformAttributesService: MapMalouAttributesToPlatformAttributesService,
        private readonly _mapCategoriesToPlatformCategoriesService: MapCategoriesToPlatformCategoriesService,
        private readonly _informationUpdatesRepository: InformationUpdatesRepository,
        private readonly _mergedInformationUpdatesRepository: MergedInformationUpdatesRepository,
        private readonly _restaurantsRepository: RestaurantsRepository,
        private readonly _attributesRepository: AttributesRepository,
        private readonly _categoriesRepository: CategoriesRepository
    ) {}

    async getMergedInformationUpdateData(dataToFetch: GetMergedInformationUpdateBodyDto) {
        const restaurantIds = dataToFetch.map(({ restaurantId }) => restaurantId);
        const mergedInformationUpdateByRestaurantId = await this._mergedInformationUpdatesRepository.find({
            filter: { restaurantId: { $in: restaurantIds }, hasOngoingOperation: true },
            projection: { _id: 0, restaurantId: 1, mergedInformationUpdateByPlatform: 1, platformsWithPendingOperations: 1 },
            options: { lean: true },
        });
        return mergedInformationUpdateByRestaurantId.map((mergedInformationUpdate) => ({
            restaurantId: mergedInformationUpdate.restaurantId.toString(),
            mergedInformationUpdateByPlatform: mergedInformationUpdate.mergedInformationUpdateByPlatform,
            platformsWithPendingOperations: mergedInformationUpdate.platformsWithPendingOperations,
        }));
    }

    async rebuildMergeInformationUpdateData(restaurantIds: string[]) {
        const restaurants = await this._restaurantsRepository.find({
            filter: { _id: { $in: restaurantIds } },
            projection: { _id: 1, access: 1 },
            options: { lean: true },
        });
        const mergeInfoDataParams = restaurants.map((restaurant) => ({
            restaurantId: restaurant._id.toString(),
            platformKeys: restaurant.access
                .filter(
                    (access) =>
                        access.active &&
                        access.accessType !== PlatformAccessType.AUTO &&
                        access.platformKey !== ('pagesjaunes' as PlatformKey)
                )
                .map((access) => access.platformKey),
        }));
        const mergedInformationUpdateData = await this.computeMergeInformationUpdateData(mergeInfoDataParams);

        const bulkOperations = mergedInformationUpdateData.map((mergedInformationUpdate) => {
            return {
                updateOne: {
                    filter: { restaurantId: toDbId(mergedInformationUpdate.restaurantId) },
                    update: {
                        $set: {
                            ...mergedInformationUpdate,
                            restaurantId: toDbId(mergedInformationUpdate.restaurantId),
                            hasOngoingOperation: mergedInformationUpdate.mergedInformationUpdateByPlatform?.length > 0,
                        },
                    },
                    upsert: true,
                },
            };
        });

        await this._mergedInformationUpdatesRepository.bulkOperations({ operations: bulkOperations, options: { ordered: false } });
    }

    async computeMergeInformationUpdateData(
        dataToFetch: GetMergedInformationUpdateBodyDto
    ): Promise<MergedInformationUpdateByRestaurantId[]> {
        const restaurantIds = dataToFetch.map(({ restaurantId }) => restaurantId);

        const [informationUpdates, malouAttributes, gmbCategories] = await Promise.all([
            this._informationUpdatesRepository.getAllInformationUpdateByForRestaurants(restaurantIds),
            this._attributesRepository.find({
                filter: { platformKey: PlatformKey.GMB },
                projection: { attributeName: 1, attributeId: 1 },
                options: { lean: true },
            }),
            this._categoriesRepository.find({
                filter: { platformKey: PlatformKey.GMB },
                projection: { categoryName: 1, categoryId: 1 },
                options: { lean: true },
            }),
        ]);

        return dataToFetch.map((infoUpdate): MergedInformationUpdateByRestaurantId => {
            const informationUpdatesForRestaurant = informationUpdates.filter(
                (informationUpdate) => informationUpdate.restaurantId.toString() === infoUpdate.restaurantId
            );
            const { platformsWithPendingOperations, mergedInformationUpdates } = this._getMergedInformationUpdateDataByPlatform({
                informationUpdates: informationUpdatesForRestaurant,
                malouAttributes,
                gmbCategories,
                platformKeys: infoUpdate.platformKeys,
                informationUpdatePlatformStateStatuses: [
                    InformationUpdatePlatformStateStatus.ERROR,
                    InformationUpdatePlatformStateStatus.MANUAL_UPDATE_ERROR,
                    InformationUpdatePlatformStateStatus.BAD_ACCESS,
                    InformationUpdatePlatformStateStatus.UNCLAIMED_PAGE,
                    InformationUpdatePlatformStateStatus.INVALID_PAGE,
                    InformationUpdatePlatformStateStatus.PENDING,
                ],
            });

            return {
                restaurantId: infoUpdate.restaurantId,
                platformsWithPendingOperations,
                mergedInformationUpdateByPlatform: mergedInformationUpdates,
            };
        });
    }

    private _getMergedInformationUpdateDataByPlatform({
        informationUpdates,
        malouAttributes,
        gmbCategories,
        platformKeys,
        informationUpdatePlatformStateStatuses,
    }: {
        informationUpdates: IInformationUpdate[];
        malouAttributes: Pick<IAttribute, '_id' | 'attributeName' | 'attributeId'>[];
        gmbCategories: Pick<ICategory, '_id' | 'categoryName' | 'categoryId'>[];
        platformKeys: PlatformKey[];
        informationUpdatePlatformStateStatuses: InformationUpdatePlatformStateStatus[];
    }): { platformsWithPendingOperations: PlatformKey[]; mergedInformationUpdates: MergedInformationUpdate[] } {
        const platformsWithPendingOperations: PlatformKey[] = [];
        const mergedInformationUpdates: MergedInformationUpdate[] = [];

        if (!informationUpdates.length) {
            return { platformsWithPendingOperations, mergedInformationUpdates };
        }

        platformKeys.forEach((platformKey) => {
            const informationUpdatesForPlatform = informationUpdates.filter((informationUpdate) => {
                const platformState = informationUpdate.platformStates.find((e) => e.key === platformKey);

                // If an access is inactive during an update, platformState won't exist but we still want to include the informationUpdate to get the whole merged updated data
                return !platformState || informationUpdatePlatformStateStatuses.includes(platformState.status);
            });
            if (!informationUpdatesForPlatform.length) {
                return;
            }

            // If latest updates's provider is Yext, don't display it in admin
            const isHandledByYext =
                informationUpdatesForPlatform
                    .sort((a, b) => (b.createdAt?.getTime() ?? 0) - (a.createdAt?.getTime() ?? 0))
                    .map((informationUpdate) => {
                        const platformState = informationUpdate.platformStates.find((e) => e.key === platformKey);
                        return platformState?.provider;
                    })
                    .filter(isNotNil)
                    .at(0) === InformationUpdateProvider.YEXT;
            if (isHandledByYext) {
                return null;
            }

            // Store platformKey that have pending operations (useful for detailed update status usecase)
            platformsWithPendingOperations.push(platformKey);

            const informationUpdatesForPlatformSorted = informationUpdatesForPlatform.sort(
                (a, b) => a.createdAt?.getTime() - b.createdAt?.getTime()
            );

            const dataToMerge: IInformationUpdateData[] = informationUpdatesForPlatformSorted
                .map((informationUpdate) => informationUpdate.data)
                .filter(isNotNil);
            if (!dataToMerge.length) {
                return;
            }

            // Merge and select only handled fields
            let mergedData = this.mergeInformationUpdateData(...dataToMerge);
            mergedData = this._formatInformationUpdateDataForPlatform({
                data: mergedData,
                platformKey,
                attributesInDb: malouAttributes,
                categoriesInDb: gmbCategories,
            });
            if (Object.keys(mergedData).length === 0) {
                return;
            }

            // Remove non important changes between current and previous data
            const previousData = this._formatInformationUpdateDataForPlatform({
                data: informationUpdatesForPlatformSorted[0]?.previousData,
                platformKey,
                attributesInDb: malouAttributes,
                categoriesInDb: gmbCategories,
            });
            mergedData = this._removeUselessChanges({ mergedData, previousData, platformKey });
            if (Object.keys(mergedData).length === 0) {
                return;
            }

            // The global platformState of the merged objects is the most recent one
            // As we also include informationUpdates with nil platformState in the previous step, we fallback to a pending state one if we don't find any
            const mostRecentPlatformState = informationUpdatesForPlatformSorted
                .at(-1)
                ?.platformStates.find((e) => e.key === platformKey) ?? {
                key: platformKey as InformationUpdateSupportedPlatformKey,
                status: InformationUpdatePlatformStateStatus.PENDING,
                provider: InformationUpdateProvider.MALOU,
            };

            const informationUpdatesSortedByValidatedAtDesc = [...informationUpdatesForPlatformSorted].sort(
                (a, b) => (b.validatedAt?.getTime() ?? 0) - (a.validatedAt?.getTime() ?? 0)
            );
            const lastValidatedAt = informationUpdatesSortedByValidatedAtDesc[0]?.validatedAt;

            mergedInformationUpdates.push({
                platformState: mostRecentPlatformState,
                previousData,
                mergedData,
                lastValidatedAt,
            });
        });

        return {
            platformsWithPendingOperations,
            mergedInformationUpdates,
        };
    }

    private _formatInformationUpdateDataForPlatform({
        data,
        platformKey,
        attributesInDb,
        categoriesInDb,
    }: {
        data: IInformationUpdateData;
        platformKey: PlatformKey;
        attributesInDb: Pick<IAttribute, '_id' | 'attributeName' | 'attributeId'>[];
        categoriesInDb: Pick<ICategory, '_id' | 'categoryName' | 'categoryId'>[];
    }): IInformationUpdateData {
        const handledKeys = getPlatformDefinition(platformKey)?.informationSent ?? [];
        const formattedInformationUpdate = lodash.pick(data, handledKeys);

        if (formattedInformationUpdate.attributes) {
            const mappedAttributes = this._mapMalouAttributesToPlatformAttributesService.execute({
                attributes: formattedInformationUpdate.attributes,
                platformKey,
                attributesInDb,
            });

            if (!mappedAttributes || mappedAttributes.length === 0) {
                delete formattedInformationUpdate.attributes;
            } else {
                formattedInformationUpdate.attributes = mappedAttributes;
            }
        }

        if (formattedInformationUpdate.categoryName) {
            const [categoryName] = this._mapCategoriesToPlatformCategoriesService.execute({
                categories: [formattedInformationUpdate.categoryName],
                platformKey,
                categoriesInDb,
            });

            if (categoryName) {
                formattedInformationUpdate.categoryName = categoryName;
            } else {
                delete formattedInformationUpdate.categoryName;
            }
        }

        if (formattedInformationUpdate.secondaryCategoriesNames) {
            const secondaryCategoriesNames = this._mapCategoriesToPlatformCategoriesService.execute({
                categories: formattedInformationUpdate.secondaryCategoriesNames,
                platformKey,
                categoriesInDb,
            });

            if (secondaryCategoriesNames?.length > 0) {
                formattedInformationUpdate.secondaryCategoriesNames = secondaryCategoriesNames;
            } else {
                delete formattedInformationUpdate.secondaryCategoriesNames;
            }
        }

        if (formattedInformationUpdate.specialHours) {
            formattedInformationUpdate.specialHours = formattedInformationUpdate.specialHours.filter(
                (specialHour) =>
                    DateTime.fromJSDate(createDateFromMalouDate(specialHour.startDate)).startOf('day') >= DateTime.now().startOf('day')
            );
        }

        return lodash.pickBy(formattedInformationUpdate, isNotNil);
    }

    private _removeUselessChanges({
        mergedData,
        previousData,
        platformKey,
    }: {
        mergedData: IInformationUpdateData;
        previousData: IInformationUpdateData;
        platformKey: PlatformKey;
    }): IInformationUpdateData {
        const cleanedMergedData = {};

        // TODO: change any to IInformationUpdateData[key]
        const getUpdatedFields: {
            [key in keyof IInformationUpdateData]?: (mergedData: any, previousData: any, platformKey: PlatformKey) => any;
        } = {
            attributes: this._getUpdatedAttributes,
            secondaryCategoriesNames: this._getUpdatedCategories,
            otherHours: this._getUpdatedOtherHours,
            specialHours: this._getUpdatedSpecialHours,
            regularHours: this._getUpdatedRegularHours,
        };

        (Object.keys(mergedData) as (keyof IInformationUpdateData)[]).forEach((key) => {
            if (getUpdatedFields[key]) {
                cleanedMergedData[key] = getUpdatedFields[key](mergedData[key], previousData[key], platformKey);
            } else {
                cleanedMergedData[key] = lodash.isEqual(mergedData[key], previousData[key]) ? undefined : mergedData[key];
            }
        });

        return lodash.pickBy(cleanedMergedData, isNotNil);
    }

    private _getUpdatedAttributes(
        mergedAttributes: IInformationUpdateData['attributes'],
        previousAttributes: IInformationUpdateData['attributes']
    ): IInformationUpdateData['attributes'] | undefined {
        const updatedAttributes =
            mergedAttributes?.filter((attribute) => {
                const { name: attributeId, value } = attribute;
                const previousAttribute = previousAttributes?.find(({ name }) => name === attributeId);

                const dismissAttribute =
                    !value ||
                    previousAttribute?.value === value ||
                    // NO doesn't add enough value in platforms except when it's a change from YES to NO
                    (!previousAttribute?.value && value === InformationUpdateAttributeValue.NO);

                return !dismissAttribute;
            }) ?? [];

        return updatedAttributes.length > 0 ? updatedAttributes : undefined;
    }

    private _getUpdatedCategories(
        mergedCategories: IInformationUpdateData['secondaryCategoriesNames'],
        previousCategories: IInformationUpdateData['secondaryCategoriesNames']
    ): IInformationUpdateData['secondaryCategoriesNames'] | undefined {
        return lodash.isEqual(mergedCategories?.sort(), previousCategories?.sort()) ? undefined : mergedCategories;
    }

    private _getUpdatedOtherHours(
        currentOtherHours: IInformationUpdateData['otherHours'],
        previousOtherHours: IInformationUpdateData['otherHours']
    ): IInformationUpdateData['otherHours'] | undefined {
        return lodash.isEqual(lodash.sortBy(currentOtherHours, ['hoursTypeId']), lodash.sortBy(previousOtherHours, ['hoursTypeId']))
            ? undefined
            : currentOtherHours;
    }

    private _getUpdatedRegularHours(
        currentRegularHours: IInformationUpdateData['regularHours'],
        previousRegularHours: IInformationUpdateData['regularHours']
    ): IInformationUpdateData['regularHours'] | undefined {
        const fieldsToSortBy = ['openDay', 'openTime', 'closeDay', 'closeTime'];
        const isTheSameValue = lodash.isEqual(
            lodash.sortBy(currentRegularHours, fieldsToSortBy),
            lodash.sortBy(previousRegularHours, fieldsToSortBy)
        );
        if (isTheSameValue) {
            return undefined;
        }

        const datesToUpdate = getRegularHoursToUpdate({ currentRegularHours, previousRegularHours });
        return datesToUpdate.length > 0 ? currentRegularHours : undefined;
    }

    private _getUpdatedSpecialHours(
        currentSpecialHours: IInformationUpdateData['specialHours'],
        previousSpecialHours: IInformationUpdateData['specialHours'],
        platformKey: PlatformKey
    ): IInformationUpdateData['specialHours'] | undefined {
        const fieldsToSortBy = [
            'startDate.year',
            'startDate.month',
            'startDate.day',
            'openTime',
            'endDate.year',
            'endDate.month',
            'endDate.day',
            'closeTime',
        ];
        const isTheSameValue = lodash.isEqual(
            lodash.sortBy(currentSpecialHours, fieldsToSortBy),
            lodash.sortBy(previousSpecialHours, fieldsToSortBy)
        );
        if (isTheSameValue) {
            return undefined;
        }

        const datesToUpdate = getSpecialHoursToUpdate({ currentSpecialHours, previousSpecialHours, platformKey });
        return datesToUpdate.length > 0 ? currentSpecialHours : undefined;
    }

    /**
     * Merge two IInformationUpdateData.
     * Only the first level of the object is merged
     * because, for example, if the field 'address' is updated, it will be the whole object that will be sent,
     * it makes no sense to do deep merge and only update address.country for example.
     */
    mergeInformationUpdateData(...data: IInformationUpdateData[]): IInformationUpdateData {
        const dataWithoutUndefinedValues = data.map((e) => lodash.omitBy(e, (value) => value === undefined));
        return lodash.assign({}, ...dataWithoutUndefinedValues);
    }

    /**
     * Merge two IInformationUpdatePlatformsState list.
     * Do not override existing platform states. Only add new ones.
     */
    mergeInformationUpdatePlatformStates(
        currentPlatformStates: IInformationUpdatePlatformState[],
        newPlatformStates: IInformationUpdatePlatformState[]
    ): IInformationUpdatePlatformState[] {
        const mergedPlatformStates = [...currentPlatformStates];
        newPlatformStates.forEach((newPlatformState) => {
            if (!mergedPlatformStates.some((platformState) => platformState.key === newPlatformState.key)) {
                mergedPlatformStates.push(newPlatformState);
            }
        });
        return mergedPlatformStates;
    }
}
