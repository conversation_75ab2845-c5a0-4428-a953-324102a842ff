import { Builder } from 'builder-pattern';

import { IMergedInformationUpdate, newDbId } from '@malou-io/package-models';

type MergedInformationUpdatePayload = IMergedInformationUpdate;

const _buildMergedInformationUpdate = (mergedInformationUpdate: MergedInformationUpdatePayload) =>
    Builder<MergedInformationUpdatePayload>(mergedInformationUpdate);

export const getDefaultMergedInformationUpdate = () =>
    _buildMergedInformationUpdate({
        _id: newDbId(),
        restaurantId: newDbId(),
        mergedInformationUpdateByPlatform: [],
        platformsWithPendingOperations: [],
        hasOngoingOperation: false,
        createdAt: new Date(),
        updatedAt: new Date(),
    });
