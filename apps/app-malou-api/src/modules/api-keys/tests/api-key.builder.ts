import { Builder } from 'builder-pattern';

import { IApiKey, newDbId } from '@malou-io/package-models';
import { ApiKeyRole } from '@malou-io/package-utils';

type ApiKeyPayload = IApiKey;

const _buildApiKey = (apiKey: ApiKeyPayload) => Builder<ApiKeyPayload>(apiKey);

export const getDefaultApiKey = () =>
    _buildApiKey({
        _id: newDbId(),
        apiKey: 'default-test-api-key-' + Math.random().toString(36).substring(7),
        name: 'test-api-key',
        userId: newDbId(),
        role: ApiKeyRole.INTERNAL_APP,
        active: true,
        createdAt: new Date(),
        updatedAt: new Date(),
    });
