import { singleton } from 'tsyringe';

import {
    EntityRepository,
    ISegmentAnalysisParentTopics,
    ISegmentAnalysisParentTopicsWithTranslations,
    SegmentAnalysisParentTopicModel,
    toDbId,
    toDbIds,
} from '@malou-io/package-models';
import { ReviewAnalysisTag } from '@malou-io/package-utils';

import { SegmentAnalysisParentTopic } from ':modules/segment-analysis-parent-topics/entities/segment-analysis-parent-topic.entity';
import { Translations } from ':modules/translations/entities/translations.entity';

export type SegmentAnalysisPayload = Omit<ISegmentAnalysisParentTopics, 'createdAt' | 'updatedAt' | '_id'>;

@singleton()
export class SegmentAnalysisParentTopicsRepository extends EntityRepository<ISegmentAnalysisParentTopics> {
    constructor() {
        super(SegmentAnalysisParentTopicModel);
    }

    async createSegmentAnalysisParentTopic(payload: {
        name: string;
        restaurantId: string;
        category: ReviewAnalysisTag;
        translationsId?: string;
    }): Promise<SegmentAnalysisParentTopic> {
        const newSegmentAnalysisParentTopic = await this.upsert({
            filter: {
                name: payload.name,
                restaurantId: toDbId(payload.restaurantId),
                category: payload.category,
            },
            update: {
                name: payload.name,
                restaurantId: toDbId(payload.restaurantId),
                category: payload.category,
                isUserInput: true,
                ...(payload.translationsId ? { translationsId: toDbId(payload.translationsId) } : {}),
            },
            options: { new: true, populate: [{ path: 'translations' }] },
        });
        return this.toEntity(newSegmentAnalysisParentTopic);
    }

    async getByRestaurantIds(restaurantIds: string[]): Promise<SegmentAnalysisParentTopic[]> {
        const results = await this.find({
            filter: {
                restaurantId: { $in: toDbIds(restaurantIds) },
            },
            options: {
                populate: [{ path: 'translations' }],
                lean: true,
            },
        });
        return results.map(this.toEntity);
    }

    async getByIds(ids: string[]): Promise<SegmentAnalysisParentTopic[]> {
        const results = await this.find({
            filter: {
                _id: { $in: toDbIds(ids) },
            },
            options: {
                populate: [{ path: 'translations' }],
                lean: true,
            },
        });
        return results.map(this.toEntity);
    }

    async getIdsByRestaurantIds(restaurantIds: string[]): Promise<string[]> {
        const results = await this.find({
            filter: {
                restaurantId: { $in: toDbIds(restaurantIds) },
            },
            projection: { _id: 1 },
            options: {
                lean: true,
            },
        });
        return results.map((doc) => doc._id.toString());
    }

    async getByRestaurantIdsAndName(topicName: string, restaurantIds: string[]): Promise<SegmentAnalysisParentTopic[]> {
        const results = await this.find({
            filter: {
                name: topicName,
                restaurantId: { $in: toDbIds(restaurantIds) },
            },
            options: {
                populate: [{ path: 'translations' }],
                lean: true,
            },
        });
        return results.map(this.toEntity);
    }

    async getByRestaurantIdsAndCategory(topicCategory: ReviewAnalysisTag, restaurantIds: string[]): Promise<SegmentAnalysisParentTopic[]> {
        const results = await this.find({
            filter: {
                category: topicCategory,
                restaurantId: { $in: toDbIds(restaurantIds) },
            },
            options: {
                populate: [{ path: 'translations' }],
                lean: true,
            },
        });
        return results.map(this.toEntity);
    }

    toEntity(document: ISegmentAnalysisParentTopicsWithTranslations): SegmentAnalysisParentTopic {
        return new SegmentAnalysisParentTopic({
            id: document._id.toString(),
            restaurantId: document.restaurantId.toString(),
            mergedParentTopicNames: document.mergedParentTopicNames,
            isUserInput: document.isUserInput,
            category: document.category,
            subcategory: document.subcategory,
            sentiment: document.sentiment || null,
            isFavorite: document.isFavorite,
            name: document.name,
            createdAt: document.createdAt,
            updatedAt: document.updatedAt,
            ...(document.translationsId ? { translationsId: document.translationsId.toString() } : {}),
            ...(document.translations
                ? {
                      translations: new Translations({
                          id: document.translations._id.toString(),
                          fr: document.translations.fr,
                          en: document.translations.en,
                          es: document.translations.es,
                          it: document.translations.it,
                          language: document.translations.language,
                          source: document.translations.source,
                      }),
                  }
                : {}),
        });
    }
}
