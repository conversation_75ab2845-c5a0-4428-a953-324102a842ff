import { sortBy } from 'lodash';
import { singleton } from 'tsyringe';

import { ReviewAnalysisSentiment, ReviewAnalysisSubCategory, ReviewAnalysisTag } from '@malou-io/package-utils';

import { CategoryParentTopicsPayload } from ':modules/ai/interfaces/ai.interfaces';
import {
    SegmentAnalysesRepository,
    SegmentAnalysisParentTopicWithSegmentCount,
} from ':modules/segment-analyses/segment-analyses.repository';
import { SegmentAnalysisParentTopicsRepository } from ':modules/segment-analysis-parent-topics/segment-analysis-parent-topics.repository';

@singleton()
export class GetAllSegmentAnalysisParentTopicsForRestaurantService {
    constructor(
        private readonly _segmentAnalysesParentTopicsRepository: SegmentAnalysisParentTopicsRepository,
        private readonly _segmentAnalysesRepository: SegmentAnalysesRepository
    ) {}

    async execute({
        restaurantId,
        isNewSemanticAnalysisV2FeatureEnabled = false,
    }: {
        restaurantId: string;
        isNewSemanticAnalysisV2FeatureEnabled?: boolean;
    }): Promise<CategoryParentTopicsPayload[]> {
        const allParentTopicsSorted = await this._getAllParentTopicsSortedForRestaurant(restaurantId);

        const existingParentTopicsByCategoryOrSubcategory = [
            ...Object.values(ReviewAnalysisSubCategory).map((subcategory) => {
                return {
                    category: subcategory,
                    subcategory,
                    topics: allParentTopicsSorted
                        .filter((topic) => topic.subcategory === subcategory)
                        .filter((topic) => (isNewSemanticAnalysisV2FeatureEnabled ? !!topic.sentiment : !topic.sentiment)) // V2 topics have sentiment, V1s don't
                        .map((topic) => this._mapMergedParentTopicsToPayload(topic))
                        .flat(),
                };
            }),
            ...Object.values(ReviewAnalysisTag)
                .filter((category) => category !== ReviewAnalysisTag.EXPEDITIOUSNESS)
                .map((category) => {
                    return {
                        category,
                        topics: allParentTopicsSorted
                            .filter((topic) => !topic.subcategory && topic.category === category)
                            .filter((topic) => (isNewSemanticAnalysisV2FeatureEnabled ? !!topic.sentiment : !topic.sentiment)) // V2 topics have sentiment, V1s don't
                            .map((topic) => this._mapMergedParentTopicsToPayload(topic))
                            .flat(),
                    };
                }),
        ];
        return existingParentTopicsByCategoryOrSubcategory;
    }

    private async _getAllParentTopicsSortedForRestaurant(restaurantId: string): Promise<SegmentAnalysisParentTopicWithSegmentCount[]> {
        const existingParentTopicIds = await this._segmentAnalysesParentTopicsRepository.getIdsByRestaurantIds([restaurantId]);
        const existingParentTopics: SegmentAnalysisParentTopicWithSegmentCount[] =
            await this._segmentAnalysesRepository.getSegmentAnalysisParentTopicsWithSegmentCount(existingParentTopicIds);

        const parentTopicIdsNotLinkedToSegments = existingParentTopicIds.filter(
            (id) => !existingParentTopics.some((topic) => topic.id.toString() === id.toString())
        );
        const parentTopicsNotLinkedToSegments =
            await this._segmentAnalysesParentTopicsRepository.getByIds(parentTopicIdsNotLinkedToSegments);

        const allTopics = [
            ...existingParentTopics,
            ...parentTopicsNotLinkedToSegments.map((topic) => ({
                ...topic,
                mergedParentTopicNames: topic.mergedParentTopicNames ?? [],
                segmentCount: 0,
            })),
        ];
        const sortedExistingParentTopics = sortBy(allTopics, (topic) => -topic.segmentCount); // descending by segment count
        return sortedExistingParentTopics;
    }

    private _mapMergedParentTopicsToPayload(
        topic: SegmentAnalysisParentTopicWithSegmentCount
    ): { title: string; topicId: string; sentiment: ReviewAnalysisSentiment | null }[] {
        const mainTopic = { title: topic.name, topicId: topic.id.toString(), sentiment: topic.sentiment ?? null };
        const additionalMergedTopics =
            topic.mergedParentTopicNames?.map((name) => ({
                title: name,
                sentiment: topic.sentiment ?? null,
                topicId: topic.id.toString(),
            })) ?? [];
        return [mainTopic, ...additionalMergedTopics];
    }
}
