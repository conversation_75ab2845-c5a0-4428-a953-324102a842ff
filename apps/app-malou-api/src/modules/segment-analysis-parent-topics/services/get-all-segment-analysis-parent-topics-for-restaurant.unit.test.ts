import { container } from 'tsyringe';

import { newDbId } from '@malou-io/package-models';
import { ReviewAnalysisSentiment, ReviewAnalysisSubCategory, ReviewAnalysisTag } from '@malou-io/package-utils';

import { registerRepositories, TestCaseBuilderV2 } from ':helpers/tests/testing-utils';
import { getDefaultRestaurant } from ':modules/restaurants/tests/restaurant.builder';
import { getDefaultSegmentAnalysis } from ':modules/segment-analyses/tests/segment-analysis.builder';
import { GetAllSegmentAnalysisParentTopicsForRestaurantService } from ':modules/segment-analysis-parent-topics/services/get-all-segment-analysis-parent-topics-for-restaurant.service';
import { getDefaultSegmentAnalysisParentTopics } from ':modules/segment-analysis-parent-topics/tests/segment-analysis-parent-topics.builder';

describe('GetAllSegmentAnalysisParentTopicsForRestaurantService', () => {
    beforeAll(() => {
        registerRepositories(['RestaurantsRepository', 'SegmentAnalysisParentTopicsRepository', 'SegmentAnalysesRepository']);
    });

    describe('execute', () => {
        it('should make list of all restaurant topics', async () => {
            const getAllSegmentAnalysisParentTopicsForRestaurantService = container.resolve(
                GetAllSegmentAnalysisParentTopicsForRestaurantService
            );

            const testCase = new TestCaseBuilderV2<'restaurants' | 'segmentAnalysisParentTopics'>({
                seeds: {
                    restaurants: {
                        data() {
                            return [getDefaultRestaurant().build()];
                        },
                    },

                    segmentAnalysisParentTopics: {
                        data(dependencies) {
                            return [
                                getDefaultSegmentAnalysisParentTopics()
                                    .restaurantId(dependencies.restaurants()[0]._id)
                                    .name('Topic 1')
                                    .build(),
                                getDefaultSegmentAnalysisParentTopics()
                                    .restaurantId(dependencies.restaurants()[0]._id)
                                    .name('Topic 2')
                                    .subcategory(ReviewAnalysisSubCategory.MENU_ITEMS)
                                    .build(),
                                getDefaultSegmentAnalysisParentTopics()
                                    .restaurantId(dependencies.restaurants()[0]._id)
                                    .name('Topic 3')
                                    .build(),
                                getDefaultSegmentAnalysisParentTopics()
                                    .restaurantId(dependencies.restaurants()[0]._id)
                                    .name('Topic 4')
                                    .build(),
                                getDefaultSegmentAnalysisParentTopics().restaurantId(newDbId()).build(),
                            ];
                        },
                    },
                },
                expectedResult: (dependencies) => {
                    return [
                        {
                            category: ReviewAnalysisSubCategory.MENU_ITEMS,
                            subcategory: ReviewAnalysisSubCategory.MENU_ITEMS,
                            topics: [
                                {
                                    title: dependencies.segmentAnalysisParentTopics[1].name,
                                    sentiment: null,
                                    topicId: dependencies.segmentAnalysisParentTopics[1]._id.toString(),
                                },
                            ],
                        },
                        {
                            category: ReviewAnalysisSubCategory.STAFF_MEMBERS,
                            subcategory: ReviewAnalysisSubCategory.STAFF_MEMBERS,
                            topics: [],
                        },
                        {
                            category: ReviewAnalysisTag.ATMOSPHERE,
                            topics: [],
                        },
                        {
                            category: ReviewAnalysisTag.FOOD,
                            topics: [],
                        },
                        {
                            category: ReviewAnalysisTag.HYGIENE,
                            topics: [
                                {
                                    title: dependencies.segmentAnalysisParentTopics[0].name,
                                    sentiment: null,
                                    topicId: dependencies.segmentAnalysisParentTopics[0]._id.toString(),
                                },
                                {
                                    title: dependencies.segmentAnalysisParentTopics[2].name,
                                    sentiment: null,
                                    topicId: dependencies.segmentAnalysisParentTopics[2]._id.toString(),
                                },
                                {
                                    title: dependencies.segmentAnalysisParentTopics[3].name,
                                    sentiment: null,
                                    topicId: dependencies.segmentAnalysisParentTopics[3]._id.toString(),
                                },
                            ],
                        },
                        {
                            category: ReviewAnalysisTag.OVERALL_EXPERIENCE,
                            topics: [],
                        },
                        {
                            category: ReviewAnalysisTag.PRICE,
                            topics: [],
                        },
                        {
                            category: ReviewAnalysisTag.SERVICE,
                            topics: [],
                        },
                    ];
                },
            });
            await testCase.build();
            const seededObjects = testCase.getSeededObjects();
            const restaurantId = seededObjects.restaurants[0]._id.toString();
            const expectedResult = testCase.getExpectedResult();

            const result = await getAllSegmentAnalysisParentTopicsForRestaurantService.execute({
                restaurantId,
            });

            expect(result).toEqual(expectedResult);
        });

        it('should include user input topics in the list', async () => {
            const getAllSegmentAnalysisParentTopicsForRestaurantService = container.resolve(
                GetAllSegmentAnalysisParentTopicsForRestaurantService
            );

            const testCase = new TestCaseBuilderV2<'restaurants' | 'segmentAnalysisParentTopics'>({
                seeds: {
                    restaurants: {
                        data() {
                            return [getDefaultRestaurant().build()];
                        },
                    },
                    segmentAnalysisParentTopics: {
                        data(dependencies) {
                            return [
                                getDefaultSegmentAnalysisParentTopics()
                                    .restaurantId(dependencies.restaurants()[0]._id)
                                    .name('User Input Topic')
                                    .isUserInput(true)
                                    .build(),
                                getDefaultSegmentAnalysisParentTopics().restaurantId(newDbId()).isUserInput(true).build(),
                            ];
                        },
                    },
                },
                expectedResult: (dependencies) => {
                    return [
                        {
                            category: ReviewAnalysisSubCategory.MENU_ITEMS,
                            subcategory: ReviewAnalysisSubCategory.MENU_ITEMS,
                            topics: [],
                        },
                        {
                            category: ReviewAnalysisSubCategory.STAFF_MEMBERS,
                            subcategory: ReviewAnalysisSubCategory.STAFF_MEMBERS,
                            topics: [],
                        },
                        {
                            category: ReviewAnalysisTag.ATMOSPHERE,
                            topics: [],
                        },
                        {
                            category: ReviewAnalysisTag.FOOD,
                            topics: [],
                        },
                        {
                            category: ReviewAnalysisTag.HYGIENE,
                            topics: [
                                {
                                    title: dependencies.segmentAnalysisParentTopics[0].name,
                                    sentiment: null,
                                    topicId: dependencies.segmentAnalysisParentTopics[0]._id.toString(),
                                },
                            ],
                        },
                        {
                            category: ReviewAnalysisTag.OVERALL_EXPERIENCE,
                            topics: [],
                        },
                        {
                            category: ReviewAnalysisTag.PRICE,
                            topics: [],
                        },
                        {
                            category: ReviewAnalysisTag.SERVICE,
                            topics: [],
                        },
                    ];
                },
            });
            await testCase.build();
            const seededObjects = testCase.getSeededObjects();
            const restaurantId = seededObjects.restaurants[0]._id.toString();
            const expectedResult = testCase.getExpectedResult();

            const result = await getAllSegmentAnalysisParentTopicsForRestaurantService.execute({
                restaurantId,
            });

            expect(result).toEqual(expectedResult);
        });

        it('should include topics from private reviews in the list', async () => {
            const getAllSegmentAnalysisParentTopicsForRestaurantService = container.resolve(
                GetAllSegmentAnalysisParentTopicsForRestaurantService
            );

            const testCase = new TestCaseBuilderV2<'restaurants' | 'segmentAnalysisParentTopics'>({
                seeds: {
                    restaurants: {
                        data() {
                            return [getDefaultRestaurant().build()];
                        },
                    },
                    segmentAnalysisParentTopics: {
                        data(dependencies) {
                            return [
                                getDefaultSegmentAnalysisParentTopics()
                                    .restaurantId(dependencies.restaurants()[0]._id)
                                    .name('Private Topic')
                                    .build(),
                                getDefaultSegmentAnalysisParentTopics().restaurantId(newDbId()).build(),
                            ];
                        },
                    },
                },
                expectedResult: (dependencies) => {
                    return [
                        {
                            category: ReviewAnalysisSubCategory.MENU_ITEMS,
                            subcategory: ReviewAnalysisSubCategory.MENU_ITEMS,
                            topics: [],
                        },
                        {
                            category: ReviewAnalysisSubCategory.STAFF_MEMBERS,
                            subcategory: ReviewAnalysisSubCategory.STAFF_MEMBERS,
                            topics: [],
                        },
                        {
                            category: ReviewAnalysisTag.ATMOSPHERE,
                            topics: [],
                        },
                        {
                            category: ReviewAnalysisTag.FOOD,
                            topics: [],
                        },
                        {
                            category: ReviewAnalysisTag.HYGIENE,
                            topics: [
                                {
                                    title: dependencies.segmentAnalysisParentTopics[0].name,
                                    sentiment: null,
                                    topicId: dependencies.segmentAnalysisParentTopics[0]._id.toString(),
                                },
                            ],
                        },
                        {
                            category: ReviewAnalysisTag.OVERALL_EXPERIENCE,
                            topics: [],
                        },
                        {
                            category: ReviewAnalysisTag.PRICE,
                            topics: [],
                        },
                        {
                            category: ReviewAnalysisTag.SERVICE,
                            topics: [],
                        },
                    ];
                },
            });
            await testCase.build();
            const seededObjects = testCase.getSeededObjects();
            const restaurantId = seededObjects.restaurants[0]._id.toString();
            const expectedResult = testCase.getExpectedResult();

            const result = await getAllSegmentAnalysisParentTopicsForRestaurantService.execute({
                restaurantId,
            });

            expect(result).toEqual(expectedResult);
        });

        it('should make list of all restaurant topics with sentiment not null when feature flag V2 is enabled', async () => {
            const getAllSegmentAnalysisParentTopicsForRestaurantService = container.resolve(
                GetAllSegmentAnalysisParentTopicsForRestaurantService
            );

            const testCase = new TestCaseBuilderV2<'restaurants' | 'segmentAnalysisParentTopics'>({
                seeds: {
                    restaurants: {
                        data() {
                            return [getDefaultRestaurant().build()];
                        },
                    },
                    segmentAnalysisParentTopics: {
                        data(dependencies) {
                            return [
                                getDefaultSegmentAnalysisParentTopics()
                                    .restaurantId(dependencies.restaurants()[0]._id)
                                    .sentiment(ReviewAnalysisSentiment.POSITIVE)
                                    .name('Topic 1')
                                    .build(),
                                getDefaultSegmentAnalysisParentTopics()
                                    .restaurantId(dependencies.restaurants()[0]._id)
                                    .sentiment(ReviewAnalysisSentiment.POSITIVE)
                                    .name('Topic 2')
                                    .subcategory(ReviewAnalysisSubCategory.MENU_ITEMS)
                                    .build(),
                                getDefaultSegmentAnalysisParentTopics()
                                    .restaurantId(dependencies.restaurants()[0]._id)
                                    .sentiment(ReviewAnalysisSentiment.NEGATIVE)
                                    .name('Topic 3')
                                    .build(),
                                getDefaultSegmentAnalysisParentTopics()
                                    .restaurantId(dependencies.restaurants()[0]._id)
                                    .sentiment(ReviewAnalysisSentiment.NEGATIVE)
                                    .name('Topic 4')
                                    .build(),
                                getDefaultSegmentAnalysisParentTopics().restaurantId(newDbId()).build(),
                            ];
                        },
                    },
                },
                expectedResult: (dependencies) => {
                    return [
                        {
                            category: ReviewAnalysisSubCategory.MENU_ITEMS,
                            subcategory: ReviewAnalysisSubCategory.MENU_ITEMS,
                            topics: [
                                {
                                    title: dependencies.segmentAnalysisParentTopics[1].name,
                                    sentiment: ReviewAnalysisSentiment.POSITIVE,
                                    topicId: dependencies.segmentAnalysisParentTopics[1]._id.toString(),
                                },
                            ],
                        },
                        {
                            category: ReviewAnalysisSubCategory.STAFF_MEMBERS,
                            subcategory: ReviewAnalysisSubCategory.STAFF_MEMBERS,
                            topics: [],
                        },
                        {
                            category: ReviewAnalysisTag.ATMOSPHERE,
                            topics: [],
                        },
                        {
                            category: ReviewAnalysisTag.FOOD,
                            topics: [],
                        },
                        {
                            category: ReviewAnalysisTag.HYGIENE,
                            topics: [
                                {
                                    title: dependencies.segmentAnalysisParentTopics[0].name,
                                    sentiment: ReviewAnalysisSentiment.POSITIVE,
                                    topicId: dependencies.segmentAnalysisParentTopics[0]._id.toString(),
                                },
                                {
                                    title: dependencies.segmentAnalysisParentTopics[2].name,
                                    sentiment: ReviewAnalysisSentiment.NEGATIVE,
                                    topicId: dependencies.segmentAnalysisParentTopics[2]._id.toString(),
                                },
                                {
                                    title: dependencies.segmentAnalysisParentTopics[3].name,
                                    sentiment: ReviewAnalysisSentiment.NEGATIVE,
                                    topicId: dependencies.segmentAnalysisParentTopics[3]._id.toString(),
                                },
                            ],
                        },
                        {
                            category: ReviewAnalysisTag.OVERALL_EXPERIENCE,
                            topics: [],
                        },
                        {
                            category: ReviewAnalysisTag.PRICE,
                            topics: [],
                        },
                        {
                            category: ReviewAnalysisTag.SERVICE,
                            topics: [],
                        },
                    ];
                },
            });
            await testCase.build();
            const seededObjects = testCase.getSeededObjects();
            const restaurantId = seededObjects.restaurants[0]._id.toString();
            const expectedResult = testCase.getExpectedResult();

            const result = await getAllSegmentAnalysisParentTopicsForRestaurantService.execute({
                restaurantId,
                isNewSemanticAnalysisV2FeatureEnabled: true,
            });

            expect(result).toEqual(expectedResult);
        });

        it('should sort topics by segment count in descending order', async () => {
            const getAllSegmentAnalysisParentTopicsForRestaurantService = container.resolve(
                GetAllSegmentAnalysisParentTopicsForRestaurantService
            );

            const testCase = new TestCaseBuilderV2<'restaurants' | 'segmentAnalysisParentTopics' | 'segmentAnalyses'>({
                seeds: {
                    restaurants: {
                        data() {
                            return [getDefaultRestaurant().build()];
                        },
                    },
                    segmentAnalysisParentTopics: {
                        data(dependencies) {
                            return [
                                getDefaultSegmentAnalysisParentTopics()
                                    .restaurantId(dependencies.restaurants()[0]._id)
                                    .name('Topic with 1 segment')
                                    .category(ReviewAnalysisTag.HYGIENE)
                                    .build(),
                                getDefaultSegmentAnalysisParentTopics()
                                    .restaurantId(dependencies.restaurants()[0]._id)
                                    .name('Topic with 3 segments')
                                    .category(ReviewAnalysisTag.HYGIENE)
                                    .build(),
                                getDefaultSegmentAnalysisParentTopics()
                                    .restaurantId(dependencies.restaurants()[0]._id)
                                    .name('Topic with 2 segments')
                                    .category(ReviewAnalysisTag.HYGIENE)
                                    .build(),
                                getDefaultSegmentAnalysisParentTopics()
                                    .restaurantId(dependencies.restaurants()[0]._id)
                                    .name('Topic with 4 segments')
                                    .category(ReviewAnalysisTag.HYGIENE)
                                    .build(),
                            ];
                        },
                    },
                    segmentAnalyses: {
                        data(dependencies) {
                            return [
                                getDefaultSegmentAnalysis()
                                    .segment('segment 1')
                                    .segmentAnalysisParentTopicIds([
                                        dependencies.segmentAnalysisParentTopics()[0]._id,
                                        dependencies.segmentAnalysisParentTopics()[3]._id,
                                    ])
                                    .build(),
                                getDefaultSegmentAnalysis()
                                    .segment('segment 2')
                                    .segmentAnalysisParentTopicIds([
                                        dependencies.segmentAnalysisParentTopics()[1]._id,
                                        dependencies.segmentAnalysisParentTopics()[3]._id,
                                    ])
                                    .build(),
                                getDefaultSegmentAnalysis()
                                    .segment('segment 3')
                                    .segmentAnalysisParentTopicIds([
                                        dependencies.segmentAnalysisParentTopics()[1]._id,
                                        dependencies.segmentAnalysisParentTopics()[3]._id,
                                    ])
                                    .build(),
                                getDefaultSegmentAnalysis()
                                    .segment('segment 4')
                                    .segmentAnalysisParentTopicIds([dependencies.segmentAnalysisParentTopics()[1]._id])
                                    .build(),
                                getDefaultSegmentAnalysis()
                                    .segment('segment 5')
                                    .segmentAnalysisParentTopicIds([
                                        dependencies.segmentAnalysisParentTopics()[2]._id,
                                        dependencies.segmentAnalysisParentTopics()[3]._id,
                                    ])
                                    .build(),
                                getDefaultSegmentAnalysis()
                                    .segment('segment 6')
                                    .segmentAnalysisParentTopicIds([dependencies.segmentAnalysisParentTopics()[2]._id])
                                    .build(),
                            ];
                        },
                    },
                },
                expectedResult: (dependencies) => {
                    return [
                        {
                            category: ReviewAnalysisSubCategory.MENU_ITEMS,
                            subcategory: ReviewAnalysisSubCategory.MENU_ITEMS,
                            topics: [],
                        },
                        {
                            category: ReviewAnalysisSubCategory.STAFF_MEMBERS,
                            subcategory: ReviewAnalysisSubCategory.STAFF_MEMBERS,
                            topics: [],
                        },
                        {
                            category: ReviewAnalysisTag.ATMOSPHERE,
                            topics: [],
                        },
                        {
                            category: ReviewAnalysisTag.FOOD,
                            topics: [],
                        },
                        {
                            category: ReviewAnalysisTag.HYGIENE,
                            topics: [
                                {
                                    title: 'Topic with 4 segments',
                                    sentiment: null,
                                    topicId: dependencies.segmentAnalysisParentTopics[3]._id.toString(),
                                },
                                {
                                    title: 'Topic with 3 segments',
                                    sentiment: null,
                                    topicId: dependencies.segmentAnalysisParentTopics[1]._id.toString(),
                                },
                                {
                                    title: 'Topic with 2 segments',
                                    sentiment: null,
                                    topicId: dependencies.segmentAnalysisParentTopics[2]._id.toString(),
                                },
                                {
                                    title: 'Topic with 1 segment',
                                    sentiment: null,
                                    topicId: dependencies.segmentAnalysisParentTopics[0]._id.toString(),
                                },
                            ],
                        },
                        {
                            category: ReviewAnalysisTag.OVERALL_EXPERIENCE,
                            topics: [],
                        },
                        {
                            category: ReviewAnalysisTag.PRICE,
                            topics: [],
                        },
                        {
                            category: ReviewAnalysisTag.SERVICE,
                            topics: [],
                        },
                    ];
                },
            });

            await testCase.build();
            const seededObjects = testCase.getSeededObjects();
            const restaurantId = seededObjects.restaurants[0]._id.toString();
            const expectedResult = testCase.getExpectedResult();

            const result = await getAllSegmentAnalysisParentTopicsForRestaurantService.execute({
                restaurantId,
            });

            expect(result).toEqual(expectedResult);
        });
    });
});
