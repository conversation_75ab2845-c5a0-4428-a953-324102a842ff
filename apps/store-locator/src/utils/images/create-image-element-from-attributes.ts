export default function createImageElementFromAttributes({
    src,
    srcSet,
    attributes = {},
    alt = '',
    classNames = '',
}: {
    src: string;
    srcSet: string;
    attributes?: Record<string, string>;
    alt?: string;
    classNames?: string;
}): string {
    return `
        <img
            src="${src}"
            srcset="${srcSet}"
            ${Object.entries(attributes)
                .map(([key, value]) => `${key}="${value}"`)
                .join(' ')}
            alt="${alt}"
            class="${classNames}" />
    `;
}
