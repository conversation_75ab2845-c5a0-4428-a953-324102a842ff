import type {
    IMapConfig,
    TransformedImageProps,
} from ':interfaces/pages.interfaces';
import type { GetImageResult } from 'astro';
import { getImage } from 'astro:assets';

import type { GetStoreLocatorMapDto } from '@malou-io/package-dto';

export async function generateMapImages({
    activePin,
    inactivePin,
    noStoreImage,
    stores,
}: {
    activePin: GetStoreLocatorMapDto['mapComponents']['pins']['activePin'];
    inactivePin: GetStoreLocatorMapDto['mapComponents']['pins']['inactivePin'];
    noStoreImage: GetStoreLocatorMapDto['mapComponents']['popup']['noStoreImage'];
    stores: GetStoreLocatorMapDto['stores'];
}): Promise<{
    activeMarkerIcon: TransformedImageProps;
    inactiveMarkerIcon: TransformedImageProps;
    stores: IMapConfig['stores'];
}> {
    const noStoreImageTransformed = await getImage({
        src: noStoreImage.url,
        formats: ['webp'],
        fallbackFormats: ['jpg'],
        inferSize: true,
        height: 140,
        width: 260,
        densities: [1, 2, 3],
    });

    const [activePinImage, inactivePinImage, transformedStores] =
        await Promise.all([
            getImage({
                src: activePin.url,
                formats: ['webp'],
                fallbackFormats: ['jpg'],
                height: 40,
                width: 40,
                densities: [1, 2, 3],
            }),
            getImage({
                src: inactivePin.url,
                formats: ['webp'],
                fallbackFormats: ['jpg'],
                height: 40,
                width: 40,
                densities: [1, 2, 3],
            }),
            transformStoresImages(stores, {
                ...noStoreImageTransformed,
                alt: noStoreImage.description,
            }),
        ]);

    const {
        fallbackFormat: activeMarkerIconFallbackFormat,
        ...activeMarkerIconAttributes
    } = activePinImage.attributes;
    const {
        fallbackFormat: inactiveMarkerIconFallbackFormat,
        ...inactiveMarkerIconAttributes
    } = inactivePinImage.attributes;

    return {
        activeMarkerIcon: {
            src: activePinImage.src,
            srcSet: activePinImage.srcSet.attribute,
            attributes: activeMarkerIconAttributes,
            alt: activePin.description,
        },
        inactiveMarkerIcon: {
            src: inactivePinImage.src,
            srcSet: inactivePinImage.srcSet.attribute,
            attributes: inactiveMarkerIconAttributes,
            alt: inactivePin.description,
        },
        stores: transformedStores,
    };
}

async function transformStoresImages(
    stores: GetStoreLocatorMapDto['stores'],
    noStoreImage: GetImageResult & { alt: string },
): Promise<IMapConfig['stores']> {
    const { fallbackFormat: noStoreFallbackFormat, ...noStoreAttributes } =
        noStoreImage.attributes;
    return Promise.all(
        stores.map(async (store) => {
            const restaurantImageProps = store.isNotOpenedYet
                ? {
                      src: noStoreImage.src,
                      srcSet: noStoreImage.srcSet.attribute,
                      attributes: noStoreAttributes,
                      alt: noStoreImage.alt,
                  }
                : await getImage({
                      src: store.image.url,
                      formats: ['webp'],
                      fallbackFormat: 'jpg',
                      inferSize: true,
                      height: 140,
                      width: 260,
                      densities: [1, 2, 3],
                  }).then((image) => {
                      const { fallbackFormat, ...attributes } =
                          image.attributes;
                      return {
                          src: image.src,
                          srcSet: image.srcSet.attribute,
                          attributes,
                          alt: store.image.description,
                      };
                  });

            return {
                ...store,
                imageProps: restaurantImageProps,
            };
        }),
    );
}
