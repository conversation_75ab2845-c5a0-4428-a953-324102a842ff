---
import Head from ':components/blocks/Head.astro';
import LeafletMap from ':components/map/LeafletMap.astro';
import SearchInput from ':components/map/SearchInput.astro';
import StoresList from ':components/map/StoresList.astro';
import type { StoreForFooter } from ':interfaces/pages.interfaces';
import ':styles/global.css';
import { getStyles } from ':utils/get-element-styles';
import type {
    GetStoreLocatorMapDto,
    GetStoreLocatorPagesDto,
} from '@malou-io/package-dto';

interface Props {
    map: GetStoreLocatorMapDto;
    urls: GetStoreLocatorPagesDto['urls'];
    storesForFooter: StoreForFooter[];
    HeaderComponent: any;
    FooterComponent: any;
}

const { map, HeaderComponent, FooterComponent, storesForFooter, urls } =
    Astro.props as Props;

const { styles } = map;

const mapSearchData = map.stores.map((store) => ({
    id: store.id,
    coordinates: store.coordinates,
}));

const getElementStyles = getStyles({ styles });
---

<!doctype html>
<html lang={map.lang}>
    <head>
        <Head headBlock={map.headBlock} />

        <!-- Global map data for client-side scripts -->
        <script define:vars={{ mapSearchData }}>
            function getMapSearchData() {
                return mapSearchData;
            }
            window.getMapSearchData = getMapSearchData;
        </script>
    </head>

    <body class="bg-tertiary pb-24 md:pb-0">
        <HeaderComponent urls={urls} />

        {/* Map component body */}
        <div
            class={`${getElementStyles({ elementId: 'map-and-store-list-wrapper' })} flex flex-col-reverse gap lg:h-[90vh] lg:flex-row`}
        >
            <div class="w-full lg:w-[28%]">
                <StoresList
                    stores={map.stores}
                    styles={styles}
                    inactivePin={map.mapComponents.pins.inactivePin}
                />
            </div>
            <div class="h-fit w-full lg:w-[72%]">
                <LeafletMap
                    stores={map.stores}
                    styles={styles}
                    mapComponents={map.mapComponents}
                />
            </div>
            <SearchInput
                styles={styles}
                classList="lg:hidden flex"
                inputClass="search-on-map-mobile"
                stores={map.stores}
            />
        </div>

        <FooterComponent stores={storesForFooter} />

        <script src="./../scripts/map/search-store.ts"></script>
    </body>
</html>
