import type { IMapSearchData } from ':interfaces/pages.interfaces';
import { onChangeMapView, onStoresListFiltered } from ':scripts/map/events';
import { getDistanceInKm } from ':scripts/map/utils';

declare global {
    interface Window {
        getMapSearchData: () => IMapSearchData[];
    }
}

document.addEventListener('DOMContentLoaded', initSearchStore);

function initSearchStore() {
    // Search on map
    const inputDesktop = document.querySelector<HTMLInputElement>(
        '.search-on-map-desktop',
    );
    const inputMobile = document.querySelector<HTMLInputElement>(
        '.search-on-map-mobile',
    );
    const list = document.getElementById('store-list');
    if (!list || !inputDesktop || !inputMobile) {
        console.error('Store list element not found');
        return;
    }
    const allStores = [...list.children] as HTMLElement[];

    // Sync input1 → input2
    inputDesktop?.addEventListener('input', (e) => {
        const htmInputEventTarget = e.target as HTMLInputElement;
        if (!inputMobile || !htmInputEventTarget) return;
        inputMobile.value = htmInputEventTarget.value;
        onQueryChange(normalizeText(htmInputEventTarget.value), allStores);
    });

    // Sync input2 → input1
    inputMobile.addEventListener('input', (e) => {
        const htmInputEventTarget = e.target as HTMLInputElement;
        if (!inputMobile || !htmInputEventTarget) return;
        inputMobile.value = htmInputEventTarget.value;
        onQueryChange(normalizeText(htmInputEventTarget.value), allStores);
    });

    // On user location button click
    const userLocationBtn = document.querySelectorAll('.user-location');
    if (userLocationBtn.length > 0) {
        userLocationBtn.forEach((btn) => {
            btn.addEventListener('click', () => {
                getUserCoordinates();
            });
        });
    }
}

// Functions
function onQueryChange(query: string, allStores: HTMLElement[]) {
    const filteredStores = allStores
        .map((el) => {
            const name = normalizeText(el.dataset.name);
            const address = normalizeText(el.dataset.address);
            if (name.includes(query) || address.includes(query)) {
                el.style.display = '';
                return el;
            } else {
                el.style.display = 'none';
                return null;
            }
        })
        .filter((el) => el !== null);

    const noResultsElement = document.getElementById('no-stores');
    if (!noResultsElement) return;
    if (filteredStores?.length === 0) {
        noResultsElement.classList.remove('hidden');
        noResultsElement.classList.add('flex');
        noResultsElement.style.display = '';
    } else {
        noResultsElement.style.display = 'none';
    }

    onStoresListFiltered(
        filteredStores
            .filter((el) => el.dataset && el.dataset.lat && el.dataset.lng)
            .map((el) => [
                parseFloat(el.dataset.lat!),
                parseFloat(el.dataset.lng!),
            ]),
    );
}

function getUserCoordinates() {
    if (navigator.geolocation) {
        navigator.geolocation.getCurrentPosition(
            function (position) {
                const lat = position.coords.latitude;
                const lng = position.coords.longitude;
                const stores = window.getMapSearchData();
                updateStoresDistances(stores, lat, lng);
                onChangeMapView(lat, lng, true);
            },
            function (error) {
                console.log('Error getting user position:', error);
                alert('Impossible de récupérer votre position.');
            },
        );
    } else {
        alert("La géolocalisation n'est pas supportée par ce navigateur.");
    }
}

function updateStoresDistances(
    stores: IMapSearchData[],
    userLat: number,
    userLng: number,
) {
    const storesDistances = stores.map((store) => {
        const distance = getDistanceInKm(
            userLat,
            userLng,
            store.coordinates.lat,
            store.coordinates.lng,
        );
        return {
            id: store.id,
            distance: distance.toFixed(2),
        };
    });

    storesDistances.forEach((storeDistance) => {
        const storeElement = document.getElementById(storeDistance.id);
        if (storeElement) {
            storeElement.textContent = `${storeDistance.distance} km`;
            storeElement.classList.remove('hidden');
            storeElement.classList.add('flex');
        }
    });

    // Sorting the stores list based on distance
    const list = document.getElementById('store-list');
    if (!list) return;
    Array.from(list.children)
        .sort((a, b) => {
            const aDistanceElement = a.querySelector('div[id]');
            const bDistanceElement = b.querySelector('div[id]');

            if (!aDistanceElement || !bDistanceElement) return 0;
            const aDistance = parseFloat(
                aDistanceElement?.innerHTML.replace(/ km/, '') ?? '0',
            );
            const bDistance = parseFloat(
                bDistanceElement?.innerHTML.replace(/ km/, '') ?? '0',
            );

            return aDistance - bDistance;
        })
        .forEach((el) => list.appendChild(el));
}

function normalizeText(text: string | undefined): string {
    if (!text) return '';
    return text
        .normalize('NFD') // Decompose accented characters (é → e + ́)
        .replace(/[\u0300-\u036f]/g, ' ') // Remove diacritics
        .replace(/[^a-zA-Z0-9\s]/g, ' ') // Remove special characters except spaces
        .replace(/\s+/g, ' ') // Replace multiple spaces with a single space
        .trim() // Trim leading/trailing spaces
        .toLowerCase(); // Convert to lowercase
}
