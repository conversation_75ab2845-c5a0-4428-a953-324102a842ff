// ------  Main Script ------
import type { IMapConfig } from ':interfaces/pages.interfaces';
import { getPopupContent } from ':scripts/map/content.ts';
import loadCss from ':scripts/map/load-css.ts';
import loadExternalScriptsAndCss from ':scripts/map/load-external-js-and-css.ts';
import createImageElementFromAttributes from ':utils/images/create-image-element-from-attributes';
import {
    control,
    divIcon,
    latLng,
    latLngBounds,
    type LatLngExpression,
    type Layer,
    map as leafletMap,
    marker as leafletMarker,
    popup as leafletPopup,
    type Map,
    Marker,
    markerClusterGroup,
    type PointExpression,
    tileLayer,
} from 'leaflet';
import 'leaflet.markercluster';

declare global {
    interface Window {
        getMapConfig: () => IMapConfig;
    }
}

// Constants for map parameters
const MAP_OPTIONS = {
    zoom: 13,
    zoomControl: false,
    scrollWheelZoom: false,
    touchZoom: false,
};

const MARKER_CLUSTER_OPTIONS = {
    spiderfyOnMaxZoom: false,
    showCoverageOnHover: false,
};

const CUSTOM_ICON_OPTIONS: Record<string, PointExpression> = {
    iconSize: [40, 40],
    iconAnchor: [16, 40],
    popupAnchor: [0, -40],
    shadowSize: [41, 41],
    shadowAnchor: [13, 41],
} as const;

// -- Main script starts here ---

const { stores, icons, translation, styles } = window.getMapConfig();

loadCss(styles);
document.addEventListener('DOMContentLoaded', initMap);

// ------ End Main Script ------

async function initMap() {
    await loadExternalScriptsAndCss();

    // Compute center  based on the coordinates
    const coordinates = stores.map((store) =>
        latLng(store.coordinates.lat, store.coordinates.lng),
    );
    const bounds = latLngBounds(coordinates);
    const adjustedCenter = bounds.getCenter();

    // Initialize the map
    const map = leafletMap('map', { center: adjustedCenter, ...MAP_OPTIONS });

    // Remove the loader once the map is ready
    map.whenReady(() => {
        const mapLoader = document.getElementById('map-loader');
        if (mapLoader) {
            mapLoader.remove();
        }
    });

    // Set the zoom level based on the bounds
    const zoomLevel = map.getBoundsZoom(bounds);
    map.setZoom(zoomLevel);

    control
        .zoom({
            position: 'topright',
        })
        .addTo(map);

    // --------- Control the zoom  ---------

    const warning = document.getElementById('zoom-warning');

    const timeouts = new WeakMap<HTMLElement, ReturnType<typeof setTimeout>>();

    map.getContainer().addEventListener('wheel', (e) => {
        if (!warning) return;
        if (!(e.ctrlKey || e.metaKey)) {
            warning.classList.add('visible');
            clearTimeout(timeouts.get(warning));
            timeouts.set(
                warning,
                setTimeout(() => {
                    warning.classList.remove('visible');
                }, 2000),
            );
        }
    });

    document.addEventListener('keydown', (e) => {
        if (e.ctrlKey || e.metaKey) {
            map.scrollWheelZoom.enable();
        }
    });

    document.addEventListener('keyup', () => {
        map.scrollWheelZoom.disable();
    });

    // --------------------------------------

    // Tile layer
    tileLayer('https://tile.openstreetmap.org/{z}/{x}/{y}.png', {
        attribution: '&copy; OpenStreetMap contributors',
    }).addTo(map);

    //------ MARKER CLUSTERING ------

    // Create a marker cluster group
    const markers = markerClusterGroup(MARKER_CLUSTER_OPTIONS);

    // Add store markers
    stores.forEach((store) => {
        const popup = leafletPopup({
            closeButton: false,
        }).setContent(getPopupContent(store, styles, translation));

        const marker = leafletMarker(
            [store.coordinates.lat, store.coordinates.lng],
            getIconOptions(store.isNotOpenedYet, icons),
        ).bindPopup(popup);

        markers.addLayer(marker);
    });

    // Add the marker cluster group to the map
    map.addLayer(markers);

    // ------------------------------

    // Change map view listener
    let tempLayer: Layer | null = null;
    window.addEventListener('change-map-view', async (event) => {
        const customEvent = event as CustomEvent;
        const { lat, lng, isUserLocation } = customEvent.detail;

        if (tempLayer) {
            map.removeLayer(tempLayer);
            tempLayer = null;
        }

        await flyToAndWait(map, [lat, lng], 10);
        if (isUserLocation) {
            leafletMarker([lat, lng]).addTo(map);
        } else {
            markers.eachLayer((layer) => {
                if (
                    layer instanceof Marker &&
                    layer.getLatLng().lat === lat &&
                    layer.getLatLng().lng === lng
                ) {
                    // If the layer is in a marker group, its popup will not open
                    // so we duplicate it and add it to the map before opening
                    if (!map.hasLayer(layer)) {
                        tempLayer = layer;
                        map.addLayer(tempLayer);
                    }
                    layer.openPopup();

                    if (tempLayer) {
                        tempLayer.on('popupclose', () => {
                            if (tempLayer !== null) {
                                map.removeLayer(tempLayer);
                                tempLayer = null;
                            }
                        });
                    }
                }
            });
        }
    });

    // Function to fly to the center of the filtered stores
    window.addEventListener('go-to-center', async (event) => {
        const coordinates = (event as CustomEvent).detail.coordinates;
        if (coordinates.length !== 0) {
            const bounds = latLngBounds(coordinates);
            const adjustedCenter = bounds.getCenter();
            const zoomLevel = map.getBoundsZoom(bounds);
            const { lat, lng } = adjustedCenter;
            await flyToAndWait(map, [lat, lng], zoomLevel);
        }
    });
}

// Function to fly to a specific location and wait for the map to finish moving
function flyToAndWait(
    map: Map,
    latlng: LatLngExpression,
    zoom: number,
): Promise<void> {
    return new Promise((resolve) => {
        const onMoveEnd = () => {
            map.off('moveend', onMoveEnd); // cleanup
            resolve();
        };

        map.on('moveend', onMoveEnd);
        map.flyTo(latlng, zoom);
    });
}

// Function to get icon options based on store status
function getIconOptions(isNotOpenedYet: boolean, icons: IMapConfig['icons']) {
    const iconToUse = isNotOpenedYet
        ? icons.inactiveMarkerIcon
        : icons.activeMarkerIcon;
    const customIcon = divIcon({
        html: createImageElementFromAttributes({
            src: iconToUse.src,
            srcSet: iconToUse.srcSet,
            attributes: iconToUse.attributes,
            alt: iconToUse.alt,
        }),
        className: '',
        ...CUSTOM_ICON_OPTIONS,
    });

    const iconOptions = {
        draggable: false,
        icon: customIcon,
    };
    return iconOptions;
}
