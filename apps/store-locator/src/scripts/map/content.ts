import type { IMapConfig } from ':interfaces/pages.interfaces';
import createImageElementFromAttributes from ':utils/images/create-image-element-from-attributes.ts';

// Function to create the popup content
export function getPopupContent(
    store: IMapConfig['stores'][number],
    styles: Record<string, string>, // todo store-locator @hamza type with more strict values
    translation: Record<string, string>, // todo store-locator @hamza type with more strict values
) {
    const imageHeight = store.isNotOpenedYet ? 'h-[60px]' : 'h-[80px]';
    return `
            <div class="flex flex-col pb-1 p-0 sm:pb-4 ${styles.popupWrapperStyle}">
                <div class="mb-0.5 sm:mb-2">
                    <div class="${imageHeight} sm:h-[140px]">
                        ${createImageElementFromAttributes({
                            src: store.imageProps.src,
                            srcSet: store.imageProps.srcSet,
                            attributes: store.imageProps.attributes,
                            alt: store.imageProps.alt,
                            classNames: `${imageHeight} rounded-t-lg sm:h-[140px] w-full object-cover`,
                        })}
                    </div>
                    ${
                        store.isNotOpenedYet
                            ? `
                                <div class="h-[20px] sm:h-[27px] flex justify-center items-center ${styles.popupStoreNotOpenedYetStyle}">
                                    <p class="text-xs sm:text-sm font-bold !m-0">${translation.openSoonText}</p>
                                </div>`
                            : ''
                    }
                </div>

                <div class="px-4 mb-0 sm:mb-2">
                    <div class="font-bold mb-1 sm:mb-3 ${styles.popupTitleStyle} truncate">${store.restaurantName}</div>
                    <a class="flex items-start gap-2 mb-0.5 sm:mb-3 !text-inherit !text-[11px]" href="${store.itineraryUrl}" target="_blank">
                        ${getPinSvg(styles)}
                        <p class='!m-0 truncate'>${store.fullAddress}</p>
                    </a>
                    ${
                        store.phone
                            ? ` <a class="flex items-start gap-2 !text-[11px] !text-inherit" href="tel:${store.phone}">
                                    ${getPhoneSvg(styles)}
                                    <p class='!m-0'>${store.phone}</p>
                                </a>`
                            : ''
                    }
                   
                </div>
                
                <div class='flex px-4 gap-1 justify-center mt-0.5 sm:mt-2 !text-[10px] sm:!text-[13px]'>
                    ${store.cta ? `<a class="!text-inherit underline" href=${store.cta.url}>${store.cta.text}</a> •` : ''}
                    <a class="!text-inherit underline" href="/${store.relativePath}">${translation.moreDetailsText}</a>
                </div>
            </div>
        `;
}

// Functions to generate the SVG icons
function getPinSvg(styles: Record<string, string>) {
    return `
        <svg viewBox="0 0 14 20" class="${styles.popupIconsStyle} w-[8px] h-[10px] sm:h-[15px] sm:w-[12px]" xmlns="http://www.w3.org/2000/svg">
            <g clip-path="url(#clip0_37732_1192)">
                <path fill-rule="evenodd" clip-rule="evenodd" d="M0 7.25116C0 3.45545 3.03144 0.378418 6.77092 0.378418C10.5104 0.378418 13.5418 3.45545 13.5418 7.25116C13.5418 8.55968 13.1816 9.78279 12.5562 10.8239L7.66905 19.4174C7.26989 20.1192 6.2719 20.1192 5.87279 19.4174L0.985631 10.8239C0.360275 9.78279 0 8.55968 0 7.25116ZM6.77097 9.23663C8.18367 9.23663 9.32887 8.07421 9.32887 6.64026C9.32887 5.20632 8.18367 4.04388 6.77097 4.04388C5.35827 4.04388 4.21306 5.20632 4.21306 6.64026C4.21306 8.07421 5.35827 9.23663 6.77097 9.23663Z" />
            </g>
            <defs>
                <clipPath id="clip0_37732_1192">
                    <rect width="14" height="20" fill="white"/>
                </clipPath>
            </defs>
        </svg>
        `;
}

function getPhoneSvg(styles: Record<string, string>) {
    return `
            <svg viewBox="0 0 21 21" class="${styles.popupIconsStyle} w-[10px] h-[10px] sm:h-[15px] sm:w-[15px]" xmlns="http://www.w3.org/2000/svg">
                <path d="M17.4563 18.375C15.6333 18.375 13.8323 17.9776 12.0531 17.1828C10.274 16.388 8.65521 15.2615 7.19688 13.8031C5.73854 12.3448 4.61198 10.726 3.81719 8.94688C3.0224 7.16771 2.625 5.36667 2.625 3.54375C2.625 3.28125 2.7125 3.0625 2.8875 2.8875C3.0625 2.7125 3.28125 2.625 3.54375 2.625H7.0875C7.29167 2.625 7.47396 2.69427 7.63438 2.83281C7.79479 2.97135 7.88958 3.13542 7.91875 3.325L8.4875 6.3875C8.51667 6.62083 8.50938 6.81771 8.46562 6.97813C8.42188 7.13854 8.34167 7.27708 8.225 7.39375L6.10312 9.5375C6.39479 10.0771 6.74115 10.5984 7.14219 11.1016C7.54323 11.6047 7.98438 12.0896 8.46562 12.5562C8.91771 13.0083 9.39167 13.4276 9.8875 13.8141C10.3833 14.2005 10.9083 14.5542 11.4625 14.875L13.5188 12.8187C13.65 12.6875 13.8214 12.5891 14.0328 12.5234C14.2443 12.4578 14.4521 12.4396 14.6562 12.4688L17.675 13.0813C17.8792 13.1396 18.0469 13.2453 18.1781 13.3984C18.3094 13.5516 18.375 13.7229 18.375 13.9125V17.4563C18.375 17.7188 18.2875 17.9375 18.1125 18.1125C17.9375 18.2875 17.7188 18.375 17.4563 18.375Z" />
            </svg>
        `;
}
